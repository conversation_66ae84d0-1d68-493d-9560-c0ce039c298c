#!/bin/bash

echo "=== Zigbee2MQTT 連接診斷腳本 ==="
echo "時間: $(date)"
echo

# 檢查 Docker 服務狀態
echo "1. 檢查 Docker 服務狀態..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker 服務未運行，請先啟動 Docker"
    exit 1
else
    echo "✅ Docker 服務正常運行"
fi
echo

# 檢查 Zigbee 協調器連通性
echo "2. 檢查 Zigbee 協調器連通性..."
echo "測試 ping 192.168.1.108:"
if ping -c 2 192.168.1.108 >/dev/null 2>&1; then
    echo "✅ 可以 ping 通 192.168.1.108"
else
    echo "❌ 無法 ping 通 192.168.1.108"
fi

echo "測試端口 6638 連接:"
if nc -zv 192.168.1.108 6638 2>/dev/null; then
    echo "✅ 端口 6638 可連接"
else
    echo "❌ 端口 6638 無法連接"
fi
echo

# 檢查容器狀態
echo "3. 檢查 Zigbee2MQTT 容器狀態..."
if docker ps --filter "name=zigbee2mqtt" --format "{{.Names}}" | grep -q zigbee2mqtt; then
    echo "✅ Zigbee2MQTT 容器正在運行"
    docker ps --filter "name=zigbee2mqtt" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "❌ Zigbee2MQTT 容器未運行"
fi
echo

# 檢查 MQTT 服務
echo "4. 檢查 MQTT 服務..."
if docker ps --filter "name=mqtt" --format "{{.Names}}" | grep -q mqtt; then
    echo "✅ MQTT 容器正在運行"
    docker ps --filter "name=mqtt" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "❌ MQTT 容器未運行"
fi

echo "測試 MQTT 端口 1883:"
if nc -zv localhost 1883 2>/dev/null; then
    echo "✅ MQTT 端口 1883 可連接"
else
    echo "❌ MQTT 端口 1883 無法連接"
fi
echo

# 檢查最新日誌
echo "5. 檢查 Zigbee2MQTT 最新日誌..."
if docker ps --filter "name=zigbee2mqtt" --format "{{.Names}}" | grep -q zigbee2mqtt; then
    echo "最近 20 行日誌:"
    docker logs --tail 20 zigbee2mqtt-zigbee2mqtt-1 2>/dev/null || echo "無法獲取日誌"
else
    echo "容器未運行，檢查本地日誌檔案..."
    if [ -d "zigbee2mqtt/data/log" ]; then
        latest_log=$(ls -t zigbee2mqtt/data/log/*/log.log 2>/dev/null | head -1)
        if [ -n "$latest_log" ]; then
            echo "最新日誌檔案: $latest_log"
            tail -20 "$latest_log"
        else
            echo "找不到日誌檔案"
        fi
    fi
fi
echo

# 檢查配置檔案
echo "6. 檢查關鍵配置..."
echo "Zigbee 協調器配置:"
grep -A 3 "serial:" zigbee2mqtt/data/configuration.yaml 2>/dev/null || echo "無法讀取配置檔案"
echo
echo "MQTT 配置:"
grep -A 2 "mqtt:" zigbee2mqtt/data/configuration.yaml 2>/dev/null || echo "無法讀取配置檔案"
echo

echo "=== 診斷完成 ==="
echo
echo "🔧 建議的解決步驟："
echo "1. 確保 Docker 和 MQTT 服務正在運行"
echo "2. 重啟 Zigbee2MQTT 容器: cd zigbee2mqtt && docker-compose up -d"
echo "3. 檢查日誌: docker logs -f zigbee2mqtt-zigbee2mqtt-1"
echo "4. 如果問題持續，檢查 192.168.1.108 裝置是否正常運行"
