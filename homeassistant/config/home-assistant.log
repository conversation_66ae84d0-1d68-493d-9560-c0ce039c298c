2025-08-27 01:29:46.749 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration aqara_gateway which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.751 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration bambu_lab which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.754 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.757 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo_control which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.760 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration localtuya which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.763 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration smartthinq_sensors which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.765 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration sonos_cloud which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.768 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration samsungtv_smart which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.770 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration dyson_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.773 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration panasonic_smart_app which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.775 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration xiaomi_gateway3 which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.778 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tesla_custom which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.783 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration webrtc which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.785 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration adaptive_lighting which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.789 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration hacs which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.792 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tuya_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:46.794 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration wellbeing which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:29:48.875 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/miio/miot_device.py:23: FutureWarning: functools.partial will be a method descriptor in future Python versions; wrap it in enum.member() if you want to preserve the old behavior
  Bool = partial(_str2bool)

2025-08-27 01:29:49.555 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/google/__init__.py:2: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)

2025-08-27 01:29:50.007 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_095ab94995bba665113c13404faa609c6ca4235aa1fec7b4b1a3d4c3ab165fad data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-27 01:29:50.078 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_2646248e5270f24bcffdcde890cc10afc9472ee3d18407e82b2c7333dc067335 data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-27 01:29:50.338 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the telnet of the gateway (************)!
2025-08-27 01:29:50.338 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Failed to connect to MQTT server ************ due to exception: [Errno 111] Connection refused
2025-08-27 01:29:50.370 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:29:50.370 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:29:50.370 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:29:50.370 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:29:50.393 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38614f07890>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:29:50.393 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38614f07b10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:29:50.434 ERROR (MainThread) [homeassistant.components.mqtt.client] Failed to connect to MQTT server due to exception: [Errno 111] Connection refused
2025-08-27 01:29:51.719 ERROR (MainThread) [homeassistant.helpers.translation] Error while parsing localized (zh-Hant) string component.esphome.config.abort.name_conflict_migrated
2025-08-27 01:29:51.830 ERROR (MainThread) [frontend.js.modern.202508111] Uncaught error from Chrome ********* on macOS 10.15.7
TypeError: Cannot read properties of undefined (reading 'attributes')
/hacsfiles/Bubble-Card/bubble-card.js:2783:53281
Object.climate (/hacsfiles/Bubble-Card/bubble-card.js:2783:53737)
mn.updateBubbleCard (/hacsfiles/Bubble-Card/bubble-card.js:2783:56861)
mn.connectedCallback (/hacsfiles/Bubble-Card/bubble-card.js:2783:55929)
apply (src/scoped-custom-element-registry.ts:424:39)
/hacsfiles/Bubble-Card/bubble-card.js:2776:45913
pop-up (/hacsfiles/Bubble-Card/bubble-card.js:2783:44)
mn.updateBubbleCard (/hacsfiles/Bubble-Card/bubble-card.js:2783:56861)
mn.connectedCallback (/hacsfiles/Bubble-Card/bubble-card.js:2783:55929)
apply (src/scoped-custom-element-registry.ts:424:39)
2025-08-27 01:29:58.334 ERROR (MainThread) [homeassistant.components.template.cover] Received invalid cover is_on state:  for entity cover.garage. Expected: open, opening, closed, closing, true, false, none
2025-08-27 01:30:00.293 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d4690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:00.294 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d4550>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:10.553 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38608cd9090>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:10.699 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38608cd8cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:20.384 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:30:31.006 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862a8096d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:31.043 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860fb25f90>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:30:50.391 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:30:52.298 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:31:11.320 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38614e05310>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:31:11.442 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38614e04550>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:31:20.396 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:31:50.399 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:31:58.163 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:32:20.404 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:32:31.527 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38615727890>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:32:31.718 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38611ee4a50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:32:50.413 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:33:04.113 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:33:20.418 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:33:50.426 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:33:51.789 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3861c0d4e10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:33:51.826 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38611ee4a50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:33:58.159 WARNING (MainThread) [homeassistant.components.http.ban] Login attempt or request with invalid authentication from ******** (********). Requested URL: '/auth/token'. (Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36)
2025-08-27 01:34:00.247 WARNING (MainThread) [homeassistant.components.http.ban] Login attempt or request with invalid authentication from ******** (********). Requested URL: '/auth/token'. (Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36)
2025-08-27 01:34:00.835 WARNING (MainThread) [homeassistant.components.websocket_api.http.connection] [267757088228992] from ******** (Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36): Disconnected: Received close message during auth phase
2025-08-27 01:34:09.993 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:34:20.437 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:34:50.451 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:35:11.955 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf386197c02d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:35:12.280 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860791b110>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:35:15.885 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:35:20.458 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:35:50.463 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:36:20.472 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:36:21.741 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:36:32.138 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3861c0d4e10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:36:32.375 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3861132b250>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:36:50.477 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:37:20.483 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:37:27.663 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:37:50.487 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:37:52.220 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860791b610>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:37:52.587 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38608271d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:38:20.496 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:38:33.539 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:38:50.504 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:39:12.610 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860cb560d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:39:12.694 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3861ac6a850>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:39:20.513 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:39:39.400 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:39:50.516 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:40:20.524 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:40:32.739 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf386099a56d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:40:33.201 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860fb25f90>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:40:45.221 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:40:50.534 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:41:20.545 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:41:50.552 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:41:51.293 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:41:53.173 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf386099a56d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:41:53.368 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38609a616d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:42:20.557 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:42:50.561 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:42:57.120 ERROR (A1MINI-Chamber-134) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:43:13.555 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862cd7f110>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:43:13.630 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d4690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:43:20.573 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:43:50.579 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:44:20.591 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:44:33.700 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862cd7fb10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:44:33.826 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862a809a90>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:44:50.601 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:45:20.610 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:45:50.621 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:45:53.915 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38616d56ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:45:54.082 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38618c916d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:46:20.624 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:46:50.626 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:47:14.253 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38608271d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:47:14.420 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38616d56ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:47:20.629 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:47:50.638 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:48:20.643 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:48:34.734 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862a8096d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:48:34.735 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862a809a90>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:48:50.650 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:49:20.661 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:49:50.676 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:49:54.854 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862cd7f9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:49:54.860 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862cd7f110>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:50:20.686 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:50:50.695 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:51:15.190 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38615727890>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:51:15.370 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38614e04f50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:51:20.703 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:51:50.707 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:52:20.713 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:52:35.574 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3862a8082d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:52:35.595 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d47d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:52:50.720 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:53:20.729 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:53:50.735 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:53:55.912 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38611ee4a50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:53:56.030 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d4690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:54:20.743 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:54:50.751 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:55:16.392 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3861b06efd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:55:16.398 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38609a61950>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:55:20.759 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:55:50.764 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:56:20.767 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:56:36.508 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf3860c0d4690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:56:36.875 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf38608271d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:56:50.772 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:57:19.398 ERROR (MainThread) [homeassistant.core] Unable to remove unknown job listener (<Job onetime listen homeassistant_stop <bound method BambuDataUpdateCoordinator._async_shutdown of <custom_components.bambu_lab.coordinator.BambuDataUpdateCoordinator object at 0xf38612082900>> HassJobType.Callback <_OneTimeListener custom_components.bambu_lab.coordinator:<bound method BambuDataUpdateCoordinator._async_shutdown of <custom_components.bambu_lab.coordinator.BambuDataUpdateCoordinator object at 0xf38612082900>>>>, None)
Traceback (most recent call last):
  File "/usr/src/homeassistant/homeassistant/core.py", line 1730, in _async_remove_listener
    self._listeners[event_type].remove(filterable_job)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
ValueError: list.remove(x): x not in list
2025-08-27 01:57:24.530 WARNING (MainThread) [custom_components.tuya_local.device] QK-CBLC5 Left receive loop has terminated
2025-08-27 01:57:24.530 WARNING (MainThread) [custom_components.tuya_local.device] QK-CBLC5 Right receive loop has terminated
