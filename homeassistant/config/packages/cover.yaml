cover:
  - platform: template
    covers:
      garage:
        device_class: garage
        friendly_name: "Garage"
        icon_template: >-
          {% if is_state('binary_sensor.garage_contact', 'on') %}
            mdi:garage-open
          {% elif is_state('binary_sensor.garage_contact', 'off') %}
            mdi:garage
          {% endif %}
        value_template: >-
          {% if is_state('binary_sensor.garage_contact', 'on') %}
            open
          {% elif is_state('binary_sensor.garage_contact', 'off') %}
            closed
          {% endif %}
        unique_id: garage_cover
        open_cover:
         - action: switch.turn_on
           target:
             entity_id: switch.garage_open
        #  - delay: "00:00:01"
        #  - action: switch.turn_off
        #    target:
        #      entity_id: switch.garage_open
        close_cover:
         - action: switch.turn_on
           target:
             entity_id: switch.garage_close
        #  - delay: "00:00:01"
        #  - action: switch.turn_off
        #    target:
        #      entity_id: switch.garage_close
