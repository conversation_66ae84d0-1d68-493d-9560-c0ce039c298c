2025-08-27 01:28:04.819 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration aqara_gateway which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.822 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration bambu_lab which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.825 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.827 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tapo_control which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.830 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration localtuya which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.832 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration smartthinq_sensors which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.834 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration sonos_cloud which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.837 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration samsungtv_smart which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.839 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration dyson_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.842 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration panasonic_smart_app which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.845 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration xiaomi_gateway3 which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.847 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tesla_custom which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.849 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration webrtc which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.852 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration adaptive_lighting which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.855 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration hacs which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.858 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration tuya_local which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:04.860 WARNING (SyncWorker_0) [homeassistant.loader] We found a custom integration wellbeing which has not been tested by Home Assistant. This component might cause stability problems, be sure to disable it if you experience issues with Home Assistant
2025-08-27 01:28:06.573 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/miio/miot_device.py:23: FutureWarning: functools.partial will be a method descriptor in future Python versions; wrap it in enum.member() if you want to preserve the old behavior
  Bool = partial(_str2bool)

2025-08-27 01:28:07.391 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/google/__init__.py:2: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)

2025-08-27 01:28:07.873 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the telnet of the gateway (************)!
2025-08-27 01:28:07.875 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Failed to connect to MQTT server ************ due to exception: [Errno 111] Connection refused
2025-08-27 01:28:07.895 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf9109db7ce10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:07.896 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf9109db7cf50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:07.993 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:28:07.993 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:28:07.995 WARNING (MainThread) [custom_components.tuya_local.helpers.config] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:28:07.995 WARNING (MainThread) [custom_components.tuya_local.entity] The use of number for RGBCW Lightbulb is deprecated and should be replaced by time.timer.
2025-08-27 01:28:08.162 ERROR (MainThread) [homeassistant.components.mqtt.client] Failed to connect to MQTT server due to exception: [Errno 111] Connection refused
2025-08-27 01:28:08.994 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_2646248e5270f24bcffdcde890cc10afc9472ee3d18407e82b2c7333dc067335 data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-27 01:28:09.067 ERROR (MainThread) [homeassistant.components.lg_thinq.coordinator] Error fetching lg_thinq_095ab94995bba665113c13404faa609c6ca4235aa1fec7b4b1a3d4c3ab165fad data: ThinQAPIException: NOT_CONNECTED_DEVICE (1222) - Not connected device
2025-08-27 01:28:17.791 ERROR (MainThread) [homeassistant.components.template.cover] Received invalid cover is_on state:  for entity cover.garage. Expected: open, opening, closed, closing, true, false, none
2025-08-27 01:28:20.456 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf910b2d1b390>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:20.456 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf910b8f6bc50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:30.695 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf91091b0c690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:30.721 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf91091b0cf50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:37.929 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:28:51.040 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf9109d9f0690>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:28:51.119 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf9109d9f1bd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:29:07.935 ERROR (MainThread) [custom_components.aqara_gateway.core.gateway] Can not connecto the mqtt of the gateway (************)!
2025-08-27 01:29:09.916 ERROR (A1MINI-Chamber-136) [custom_components.bambu_lab.pybambu] Chamber image connection rejected by the printer. Check provided access code and IP address.
2025-08-27 01:29:31.179 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf91099449090>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:29:31.486 ERROR (MainThread) [custom_components.tapo_control] Unable to connect to Tapo: Cameras Control controller: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0xf91096b8d090>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-27 01:29:35.750 ERROR (MainThread) [homeassistant.core] Unable to remove unknown job listener (<Job onetime listen homeassistant_stop <bound method BambuDataUpdateCoordinator._async_shutdown of <custom_components.bambu_lab.coordinator.BambuDataUpdateCoordinator object at 0xf9109a5878c0>> HassJobType.Callback <_OneTimeListener custom_components.bambu_lab.coordinator:<bound method BambuDataUpdateCoordinator._async_shutdown of <custom_components.bambu_lab.coordinator.BambuDataUpdateCoordinator object at 0xf9109a5878c0>>>>, None)
Traceback (most recent call last):
  File "/usr/src/homeassistant/homeassistant/core.py", line 1730, in _async_remove_listener
    self._listeners[event_type].remove(filterable_job)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
ValueError: list.remove(x): x not in list
2025-08-27 01:29:40.911 WARNING (MainThread) [custom_components.tuya_local.device] QK-CBLC5 Right receive loop has terminated
2025-08-27 01:29:40.911 WARNING (MainThread) [custom_components.tuya_local.device] QK-CBLC5 Left receive loop has terminated
2025-08-27 01:29:42.797 ERROR (MainThread) [homeassistant] Error doing job: Task exception was never retrieved (None)
Traceback (most recent call last):
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/usr/local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('************', 1026)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/config/custom_components/tapo_control/__init__.py", line 1150, in unsubscribe
    await hass.data[DOMAIN][entry.entry_id]["events"].async_stop()
  File "/usr/src/homeassistant/homeassistant/components/onvif/event.py", line 150, in async_stop
    await self.pullpoint_manager.async_stop()
  File "/usr/src/homeassistant/homeassistant/components/onvif/event.py", line 314, in async_stop
    await self._async_cancel_and_unsubscribe()
  File "/usr/src/homeassistant/homeassistant/components/onvif/event.py", line 334, in _async_cancel_and_unsubscribe
    await self._async_unsubscribe_pullpoint()
  File "/usr/src/homeassistant/homeassistant/components/onvif/event.py", line 350, in _async_unsubscribe_pullpoint
    await self._pullpoint_manager.shutdown()
  File "/usr/local/lib/python3.13/site-packages/onvif/managers.py", line 98, in shutdown
    await self.stop()
  File "/usr/local/lib/python3.13/site-packages/onvif/managers.py", line 86, in stop
    await self._subscription.Unsubscribe()
  File "/usr/local/lib/python3.13/site-packages/zeep/proxy.py", line 64, in __call__
    return await self._proxy._binding.send_async(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/zeep/wsdl/bindings/soap.py", line 156, in send_async
    response = await client.transport.post_xml(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        options["address"], envelope, http_headers
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/onvif/wrappers.py", line 52, in _async_wrap_connection_error_retry
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/onvif/client.py", line 142, in post_xml
    return await super().post_xml(address, envelope, headers)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/onvif/zeep_aiohttp.py", line 209, in post_xml
    response, content = await self._post_internal(address, message, headers)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/onvif/zeep_aiohttp.py", line 151, in _post_internal
    response = await self.session.post(
               ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/site-packages/aiohttp/connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host ************:1026 ssl:default [Connect call failed ('************', 1026)]
