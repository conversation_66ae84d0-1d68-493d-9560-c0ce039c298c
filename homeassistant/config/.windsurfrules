# Home Assistant Windsurf 規則
# 這些規則將幫助您更有效地管理 Home Assistant 配置

# 檔案類型關聯
*.yaml: yaml
*.yml: yaml
configuration.yaml: yaml
automations.yaml: yaml
scripts.yaml: yaml
scenes.yaml: yaml
secrets.yaml: yaml
*.json: json

# 自動完成設定
automations.yaml:
  suggestions:
    - alias: "自動化名稱"
    - description: "自動化描述"
    - trigger:
    - condition:
    - action:

# 語法高亮設定
yaml:
  entities:
    - light
    - switch
    - sensor
    - binary_sensor
    - automation
    - script
    - scene
    - climate
    - media_player

# 自動化規則提示
automation_templates:
  - name: "基本自動化"
    template: |
      - alias: "新自動化"
        description: "自動化描述"
        trigger:
          - platform: state
            entity_id: sensor.example
        condition: []
        action:
          - service: light.turn_on
            target:
              entity_id: light.example
  
  - name: "時間觸發自動化"
    template: |
      - alias: "定時自動化"
        description: "在特定時間執行"
        trigger:
          - platform: time
            at: "07:00:00"
        condition: []
        action:
          - service: switch.turn_on
            target:
              entity_id: switch.example

# 檢查規則
lint_rules:
  - name: "檢查實體 ID 格式"
    pattern: "entity_id: [a-z]+\\.[a-z0-9_]+"
    message: "實體 ID 應該遵循 domain.entity_id 格式"
  
  - name: "檢查服務格式"
    pattern: "service: [a-z]+\\.[a-z_]+"
    message: "服務應該遵循 domain.service 格式"
