{"domain": "dyson_local", "name": "<PERSON><PERSON><PERSON>", "codeowners": ["@libdyson-wg", "@dotvezz", "@cmgrayb"], "config_flow": true, "dependencies": ["mqtt", "zeroconf"], "documentation": "https://github.com/libdyson-wg/ha-dyson", "import_executor": true, "iot_class": "local_push", "issue_tracker": "https://github.com/libdyson-wg/ha-dyson/issues", "requirements": ["libdyson-neon==1.6.0"], "version": "1.7.0"}