{"config": {"error": {"no_servers": "请选择至少一个服务器", "cant_login": "无法登录，请检查用户名和密码", "cant_connect": "无法连接到网络", "wrong_model": "网关型号不支持", "wrong_token": "错误的网关Token", "wrong_telnet": "错误的打开telnet命令", "verify": "点击链接去验证"}, "step": {"user": {"title": "选择动作", "data": {"action": "动作"}}, "cloud": {"title": "添加小米云账号", "description": "选择设备所属的服务器", "data": {"username": "邮箱/小米 ID", "password": "密码", "servers": "服务器"}}, "cloud_captcha": {"title": "Verify account", "description": "Enter code from: ![]({image})", "data": {"code": "Code"}}, "cloud_verify": {"title": "Verify account", "description": "Enter code from: {address}", "data": {"code": "Code"}}, "token": {"description": "可以通过 [添加小米云账号](https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token) 自动或 [手动](https://github.com/Maxmudjon/com.xiaomi-miio/blob/master/docs/obtain_token.md) 获取网关Token。请阅读 [已支持的网关固件版本](https://github.com/AlexxIT/XiaomiGateway3#supported-firmwares)", "data": {"host": "网关IP", "token": "Token", "key": "Key"}}}}, "options": {"step": {"init": {"title": "小米云的设备信息", "data": {"did": "设备"}, "description": "{device_info}"}, "user": {"title": "网关设定", "data": {"host": "主机", "token": "Token", "ble": "支持 BLE 设备", "stats": "Zigbee 和 BLE 效能数据", "debug": "调试信息"}}}}, "device_automation": {"trigger_type": {"button": "按下按键", "button_1": "按下第 1 键", "button_2": "按下第 2 键", "button_3": "按下第 3 键", "button_4": "按下第 4 键", "button_5": "按下第 5 键", "button_6": "按下第 6 键", "button_both": "两键同时按下", "button_both_12": "同时按下第 1、2 键", "button_both_13": "同时按下第 1、3 键", "button_both_23": "同时按下第 2、3 键"}}, "entity": {"select": {"command": {"state": {"pair": "Zigbee配对", "bind": "Zigbee绑定", "ota": "Zigbee OTA", "reconfig": "Zigbee重新配置", "parent_scan": "Zigbee父设备扫描", "firmware_lock": "网关固件锁定", "reboot": "网关重启", "ftp": "网关运行FTP", "flashzb": "Zigbee刷写EZSP", "openmiio_restart": "重载OpenmiIO"}}, "data": {"state": {"enabled": "已启用", "disabled": "已禁用", "unknown": "未知", "ok": "完成", "error": "出错", "permit_join": "允许加入", "stop_join": "停止加入", "cancel": "取消", "key_secure": "发送网络密钥（安全）", "key_legacy": "发送网络密钥（传统）", "no_firmware": "无固件", "bind": "绑定", "unbind": "解除绑定", "no_devices": "没有设备", "original": "原始固件", "custom": "自定义固件"}}}}}