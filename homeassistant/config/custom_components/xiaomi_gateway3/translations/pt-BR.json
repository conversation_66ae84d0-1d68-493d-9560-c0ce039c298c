{"config": {"error": {"no_servers": "Selecione pelo menos um servidor", "cant_login": "Não consigo fazer login, verifique o nome de usuário e a senha", "cant_connect": "Não é possível conectar ao gateway", "wrong_model": "Modelo de gateway não compatível", "wrong_token": "Token Mi Home errado", "wrong_telnet": "Comando telnet aberto errado", "verify": "Click to verify url"}, "step": {"user": {"title": "Selecionar ação", "data": {"action": "Ação"}}, "cloud": {"title": "Adicionar conta <PERSON>", "description": "Selecione apenas os servidores em que você possui dispositivos", "data": {"username": "E-mail / ID da conta Mi", "password": "<PERSON><PERSON>", "servers": "<PERSON><PERSON><PERSON>"}}, "cloud_captcha": {"title": "Verify account", "description": "Enter code from: ![]({image})", "data": {"code": "Code"}}, "cloud_verify": {"title": "Verify account", "description": "Enter code from: {address}", "data": {"code": "Code"}}, "token": {"description": "Você pode obter o token Mi Home automaticamente com [integração na nuvem](https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token) ou [manualmente](https://github.com/Maxmudjon/ com.xiaomi-miio/blob/master/docs/obtain_token.md).", "data": {"host": "Host", "token": "Token"}}}}, "options": {"step": {"cloud": {"title": "Informações de dispositivos MiCloud", "data": {"did": "Dispositivo"}, "description": "{device_info}"}, "user": {"title": "Configuração do Gateway", "data": {"host": "Host", "token": "Token", "telnet_cmd": "Abra o comando Telnet", "ble": "Suporta dispositivos Bluetooth", "stats": "Adicionar sensores estatísticos", "debug": "Debug"}}}}, "device_automation": {"trigger_type": {"button": "Pressione o botão", "button_1": "1º botão pressionado", "button_2": "2º botão pressionado", "button_3": "3º botão pressionado", "button_4": "4º botão pressionado", "button_5": "5º botão pressionado", "button_6": "6º botão pressionado", "button_both": "Ambos os botões pressionados", "button_both_12": "1º e 2º botão pressionado", "button_both_13": "1º e 3º botão pressionado", "button_both_23": "2º e 3º botão pressionado"}}}