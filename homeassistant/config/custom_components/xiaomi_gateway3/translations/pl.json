{"config": {"error": {"no_servers": "Wybierz co najmniej jeden serwer", "cant_login": "<PERSON><PERSON> można <PERSON>, sprawdź czy wpisałeś poprawny login i hasło", "cant_connect": "Nie można nawiązać połączenia z bramką", "wrong_model": "Niewspierany model bram<PERSON>", "wrong_token": "Niepoprawny token Mi Home", "wrong_telnet": "Nieprawidłowa komenda otwarcia protokołu telnet", "verify": "Click to verify url"}, "step": {"user": {"title": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "<PERSON><PERSON><PERSON><PERSON>"}}, "cloud": {"title": "<PERSON><PERSON><PERSON> k<PERSON>", "description": "Wybierz tylko te serwery na której masz urządzenia", "data": {"username": "Email / ID konta <PERSON>", "password": "<PERSON><PERSON><PERSON>", "servers": "Serwery"}}, "cloud_captcha": {"title": "Verify account", "description": "Enter code from: ![]({image})", "data": {"code": "Code"}}, "cloud_verify": {"title": "Verify account", "description": "Enter code from: {address}", "data": {"code": "Code"}}, "token": {"description": "Mo<PERSON><PERSON>z uzy<PERSON>ć token automatycznie używając [danych logowania do konta <PERSON>](https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token) bądź [ręcznie](https://github.com/Maxmudjon/com.xiaomi-miio/blob/master/docs/obtain_token.md).", "data": {"host": "Host (Adres IP bramki)", "token": "Token"}}}}, "options": {"step": {"init": {"title": "Informacje o urządzeniach z Xiaomi Home", "data": {"did": "Urząd<PERSON><PERSON>"}, "description": "{device_info}"}, "user": {"title": "Ustawi<PERSON> bra<PERSON>ki", "data": {"host": "Host", "token": "Token", "telnet_cmd": "Komenda do otwarcia protokołu telnet", "ble": "Wsparcie dla urządzeń BLE (Bluetoth Low Energy)", "stats": "Dane dotyczące wydajności Zigbee i BLE", "debug": "Debugowanie"}}}}, "device_automation": {"trigger_type": {"button": "Naciśnięcie przycisku", "button_1": "Naciśnięcie 1 przycisku", "button_2": "Naciśnięcie 2 przycisku", "button_3": "Naciśnięcie 3 przycisku", "button_4": "Naciśnięcie 4 przycisku", "button_5": "Naciśnięcie 5 przycisku", "button_6": "Naciśnięcie 6 przycisku", "button_both": "Naciśnięcie obydwu przycisków", "button_both_12": "Naciśnięcie przycisków 1 i 2", "button_both_13": "Naciśnięcie przycisków 1 i 3", "button_both_23": "Naciśnięcie przycisków 2 i 3"}}}