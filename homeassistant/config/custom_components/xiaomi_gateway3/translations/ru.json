{"config": {"error": {"no_servers": "Пожалуйста, выберите хотя бы один сервер", "cant_login": "Ошибка, проверьте имя пользователя и пароль", "cant_connect": "Не получается подключиться к шлюзу", "wrong_model": "Модель шлюза не поддерживается", "wrong_token": "Неверный Mi Home токен", "wrong_telnet": "Неверная команда для открытия Telnet", "verify": "Перейдите по verify url"}, "step": {"user": {"title": "Выберите действие", "data": {"action": "Действие"}}, "cloud": {"title": "Доба<PERSON>и<PERSON>ь Mi <PERSON> аккаунт", "description": "Выбирайте только те серверы, на которых у вас есть устройства", "data": {"username": "Email / Mi Account ID", "password": "Пароль", "servers": "Серверы"}}, "cloud_captcha": {"title": "Проверка аккаунта", "description": "Введите код из: ![]({image})", "data": {"code": "<PERSON>од"}}, "cloud_verify": {"title": "Проверка аккаунта", "description": "Введите код из: {address}", "data": {"code": "<PERSON>од"}}, "token": {"description": "Вы можете получить Mi Home токен автоматически с помощью [облачной интеграции](https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token) или [вручную](https://github.com/Maxmudjon/com.xiaomi-miio/blob/master/docs/obtain_token.md).", "data": {"host": "IP-адрес", "token": "Токен", "key": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "options": {"step": {"cloud": {"title": "Информация о устройствах MiCloud", "data": {"did": "Устройство"}, "description": "{device_info}"}, "user": {"title": "Настройка шлюза", "data": {"host": "IP-адрес", "token": "Токен", "key": "<PERSON><PERSON><PERSON><PERSON>", "telnet_cmd": "Команда для открытия Telnet", "ble": "Поддержка Bluetooth устройств", "stats": "Добавить сенсоры статистики", "debug": "Логи отладки (debug)"}}}}, "device_automation": {"trigger_type": {"action": "Действие"}}, "entity": {"select": {"command": {"state": {"info": "Информация об устройстве", "update": "Обновление устройства", "pair": "Сопряжен<PERSON>е Zigbee", "force_pair": "Принудительное сопряжение Zigbee", "bind": "Привязка Zigbee", "ota": "Zigbee OTA", "reconfig": "Переконфигурация Zigbee", "parent_scan": "Сканирование родительских устройств Zigbee", "firmware_lock": "Блокировка прошивки шлюза", "reboot": "Перезагрузка шлюза", "run_ftp": "Запуск FTP на шлюзе", "flash_ezsp": "Прошивка EZSP Zigbee", "openmiio_restart": "Перезагрузка OpenmiIO", "disable": "Отключение шлюза", "enable": "Включение шлюза", "rejoin": "Повторное подключение Zigbee", "remove": "Удаление устройства"}}, "data": {"state": {"enabled": "Включено", "disabled": "Отключено", "unknown": "Неизвестно", "ok": "OK", "error": "ОШИБКА", "permit_join": "Готов к подключению", "stop_join": "Сопряжение остановлено", "cancel": "Отмена", "key_secure": "Отправить сетевой ключ (безопасный)", "key_legacy": "Отправить сетевой ключ (устаревший)", "no_firmware": "Нет прошивки", "bind": "Привязать", "unbind": "Отвязать", "no_devices": "Нет устройств", "original": "Оригинальный", "custom": "Пользовательский"}}}}}