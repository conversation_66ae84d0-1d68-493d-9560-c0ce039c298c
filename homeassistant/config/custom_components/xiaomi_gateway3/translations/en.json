{"config": {"error": {"no_servers": "Please select at least one server", "cant_login": "Can't login, check username and password", "cant_connect": "Can't connect to gateway", "no_key": "Unsupported gateway firmware without key", "wrong_model": "Unsupported gateway model", "wrong_token": "Wrong Mi Home token", "wrong_telnet": "Wrong open telnet command", "verify": "Click to verify url"}, "step": {"user": {"title": "Select Action", "data": {"action": "Action"}}, "cloud": {"title": "Add <PERSON> Account", "description": "Select only those servers where you have devices", "data": {"username": "Email / Mi Account ID", "password": "Password", "servers": "Servers"}}, "cloud_captcha": {"title": "Verify account", "description": "Enter code from: ![]({image})", "data": {"code": "Code"}}, "cloud_verify": {"title": "Verify account", "description": "Enter code from: {address}", "data": {"code": "Code"}}, "token": {"description": "You can obtain Mi Home token automatically with [Cloud integration](https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token) or [manually](https://github.com/Maxmudjon/com.xiaomi-miio/blob/master/docs/obtain_token.md).", "data": {"host": "Host", "token": "Token", "key": "Key"}}}}, "options": {"step": {"cloud": {"title": "MiCloud devices info", "data": {"did": "<PERSON><PERSON>"}, "description": "{device_info}"}, "user": {"title": "Gateway Config", "data": {"host": "Host", "token": "Token", "key": "Key", "telnet_cmd": "Open Telnet command", "ble": "Support Bluetooth devices", "stats": "Add statistic sensors", "debug": "Debug logs"}}}}, "device_automation": {"trigger_type": {"action": "Action"}}, "entity": {"select": {"command": {"state": {"info": "Device info", "update": "Device update", "pair": "Zigbee pairing", "force_pair": "Zigbee force pairing", "bind": "Zigbee binding", "ota": "Zigbee OTA", "reconfig": "Zigbee reconfig", "parent_scan": "Zigbee parent scan", "firmware_lock": "Gateway firmware lock", "reboot": "Gateway reboot", "run_ftp": "Gateway run FTP", "flash_ezsp": "Zigbee flash EZSP", "openmiio_restart": "OpenmiIO restart", "disable": "Gateway disable", "enable": "Gateway enable", "rejoin": "<PERSON><PERSON><PERSON> rejoin", "remove": "Zigbee delete"}}, "data": {"state": {"enabled": "Enabled", "disabled": "Disabled", "unknown": "Unknown", "ok": "OK", "error": "ERROR", "permit_join": "Ready to join", "stop_join": "Pairing stopped", "cancel": "Cancel", "key_secure": "Send network key (secure)", "key_legacy": "Send network key (legacy)", "no_firmware": "No firmware", "bind": "Bind", "unbind": "Unbind", "no_devices": "No devices", "original": "Original", "custom": "Custom"}}}}}