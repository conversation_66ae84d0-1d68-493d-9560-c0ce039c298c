{"config": {"error": {"no_servers": "Kérjük válasszon egy szervert", "cant_login": "Sikertelen bejelentkezés! Ellenőrizze a bejelentkezési adatokat", "cant_connect": "Sikertelen csatlakozás", "wrong_model": "<PERSON><PERSON>l", "wrong_token": "Hibás Mi Home token", "wrong_telnet": "Hibás telnet parancs", "verify": "Kattintson az URL ellenőrzéséhez"}, "step": {"user": {"title": "Válasszon feladatot", "data": {"action": "Feladat"}}, "cloud": {"title": "Mi <PERSON> fiók hozzáadása", "description": "Csak azokat a szervereket válassza ki, amelyeken vannak eszközei", "data": {"username": "E-mail cím / Mi Account azonosító", "password": "Je<PERSON><PERSON><PERSON>", "servers": "Szerver"}}, "cloud_captcha": {"title": "Verify account", "description": "Enter code from: ![]({image})", "data": {"code": "Code"}}, "cloud_verify": {"title": "Verify account", "description": "Enter code from: {address}", "data": {"code": "Code"}}, "token": {"description": "A Mi Home tokent automatikusan beszerezheti a [Cloud integrációval] (https://github.com/AlexxIT/XiaomiGateway3#obtain-mi-home-device-token), vagy [man<PERSON><PERSON><PERSON><PERSON>](https://github.com/Maxmudjon/com.xiaomi-miio/blob/master/docs/obtain_token.md).", "data": {"host": "Hoszt", "token": "Token"}}}}, "options": {"step": {"cloud": {"title": "MiCloud eszköz infó", "data": {"did": "Eszköz"}, "description": "{device_info}"}, "user": {"title": "Központi egység konfiguráció", "data": {"host": "Hoszt", "token": "Token", "telnet_cmd": "Nyissa meg a Telnet parancshoz", "ble": "Bluetooth eszközök támogatása", "stats": "Statisztikai szenzorok hozzáadása", "debug": "Fejlesztői naplózás"}}}}, "device_automation": {"trigger_type": {"button": "<PERSON><PERSON>", "button_1": "1. gomb", "button_2": "2. gomb", "button_3": "3. gomb", "button_4": "4. gomb", "button_5": "5. gomb", "button_6": "6. gomb", "button_both": "<PERSON><PERSON><PERSON><PERSON> gomb megny<PERSON>", "button_both_12": "1. <PERSON><PERSON> 2. gomb me<PERSON>", "button_Open Telnet commandboth_13": "1. <PERSON><PERSON> 3. gomb me<PERSON>", "button_both_23": "2. <PERSON><PERSON> 3. gomb <PERSON><PERSON>"}}, "entity": {"select": {"command": {"state": {"pair": "Zigbee párosítás", "bind": "Zigbee rögzítés", "ota": "Zigbee OTA", "reconfig": "Zigbee újrakonfigurálása", "parent_scan": "Zigbee szkennelés", "firmware_lock": "Központi egység firmware verziójának rögzítése", "reboot": "Központi egység újraindítása", "ftp": "Központi egység FTP hozzáférés", "flashzb": "Zigbee EZSP telepítés"}}, "data": {"state": {"enabled": "Engedélyezés", "disabled": "<PERSON><PERSON><PERSON>", "unknown": "Ismeretlen", "ok": "OK", "error": "HIBA", "permit_join": "Készen áll a csatlakozásra", "stop_join": "A párosítás leállt", "cancel": "M<PERSON>gs<PERSON>", "key_secure": "Hálózati kulcs küldése (biztonságos)", "key_legacy": "Hálózati kulcs küldése (örökölt)", "no_firmware": "Nincs firmware", "bind": "<PERSON><PERSON><PERSON><PERSON>", "unbind": "<PERSON><PERSON><PERSON>", "no_devices": "<PERSON><PERSON><PERSON>", "original": "Eredeti", "custom": "<PERSON><PERSON><PERSON><PERSON>"}}}}}