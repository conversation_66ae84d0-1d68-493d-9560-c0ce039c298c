select_picture_mode:
  name: Select Picture Mode
  description: Send to samsung TV the command to change picture mode.
  fields:
    entity_id:
      name: Entity Name
      description: Name of the target entity
      required: true
      example: "media_player.tv"
      selector:
        entity:
          integration: samsungtv_smart
    picture_mode:
      name: Picture Mode
      description: Name of the picture mode to switch to. Possible options
        can be found in the picture_mode_list state attribute.
      required: true
      example: "Standard"
      selector:
        text:

set_art_mode:
  name: Set Art Mode
  description: Send to samsung TV the command to set art mode.
  fields:
    entity_id:
      name: Entity Name
      description: Name of the target entity
      required: true
      example: "media_player.tv"
      selector:
        entity:
          integration: samsungtv_smart
