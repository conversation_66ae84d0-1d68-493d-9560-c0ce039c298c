{"domain": "samsungtv_smart", "name": "SamsungTV Smart", "after_dependencies": ["smartthings"], "codeowners": ["@jaruba", "@ollo69", "@screwdgeh"], "config_flow": true, "dependencies": ["http"], "documentation": "https://github.com/ollo69/ha-samsungtv-smart", "integration_type": "device", "iot_class": "cloud_polling", "issue_tracker": "https://github.com/ollo69/ha-samsungtv-smart/issues", "requirements": ["websocket-client!=1.4.0,>=0.58.0", "wakeonlan>=2.0.0", "aiofiles>=0.8.0", "casttube>=0.2.1"], "version": "0.14.4"}