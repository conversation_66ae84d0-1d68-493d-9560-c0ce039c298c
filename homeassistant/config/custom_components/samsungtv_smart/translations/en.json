{"config": {"abort": {"already_configured": "This Samsung TV is already configured.", "already_in_progress": "Samsung TV configuration is already in progress.", "host_unique_id": "Reconfiguration not possible beacause hostname is used as unique id. Please remove this entry and configure a new one.", "reconfigure_successful": "Samsung TV reconfiguration completed successfully.", "unsupported_version": "This integration require at least HomeAssistant version {req_ver}, you are running version {run_ver}."}, "error": {"auth_missing": "Home Assistant is not authorized to connect to this Samsung TV. Please check your TV's settings to authorize Home Assistant.", "invalid_host": "Invalid host.", "not_successful": "Unable to create WebSocket connection with this Samsung TV device.", "not_supported": "This Samsung TV device is currently not supported.", "only_key_or_st": "Specify only access token or SmartThings entry.", "st_api_key_fail": "Failed to retrieve access token from SmartThings entry.", "st_device_not_found": "SmartThings TV deviceID not found.", "st_device_used": "SmartThings TV deviceID already used.", "wrong_api_key": "Wrong SmartThings token."}, "flow_title": "SamsungTV Smart: {model}", "step": {"confirm": {"description": "Do you want to set up Samsung TV {model}? If you never connected Home Assistant before you should see a popup on your TV asking for authorization. Manual configurations for this TV will be overwritten."}, "user": {"data": {"host": "Host or IP address", "name": "Name assigned to the entity", "use_ha_name_for_ws": "Use HA instance name for identification on TV", "api_key": "SmartThings generated token (optional)", "st_entry_unique_id": "SmartThings entry used to provide SmartThings credential"}, "description": "Enter your Samsung TV information. SmartThings is optional but really suggested.\nAfter confirm you should see a popup on your TV asking for authorization."}, "reconfigure": {"data": {"host": "Host or IP address", "api_key": "SmartThings generated token (optional, if empty will be used the existing)", "st_entry_unique_id": "SmartThings entry used to provide SmartThings credential"}, "description": "Enter your Samsung TV information to update. SmartThings configuration can be changed but not removed.\nAfter confirm you could see a popup on your TV asking for authorization."}, "stdevice": {"data": {"st_devices": "SmartThings TV"}, "description": "You have multiple TVs configured on your account. Select the TV you are configuring from the list."}, "stdeviceid": {"data": {"device_id": "SmartThings TV deviceID"}, "description": "Automatic SmartThings deviceID detection failed. To continue you must identify the deviceID on the SmartThings site (see documentation) and insert it here."}}}, "options": {"step": {"init": {"title": "SamsungTV Smart options", "data": {"use_st_status_info": "Use SmartThings TV Status information", "use_st_channel_info": "Use SmartThings TV Channels information", "show_channel_number": "Use SmartThings TV Channels number information", "app_load_method": "Applications list load mode at startup", "logo_option": "Display a logo for known sources, apps and channels", "use_local_logo": "Allow use of local logo images", "power_on_method": "Method used to turn on TV", "show_adv_opt": "Show options menu"}}, "menu": {"title": "SamsungTV Smart options menu", "menu_options": {"adv_opt": "Advanced options", "app_list": "Applications list configuration", "channel_list": "Channels list configuration", "init": "Standard options", "save_exit": "Save options and exit", "source_list": "Sources list configuration", "sync_ent": "Synched entities configuration"}}, "adv_opt": {"title": "SamsungTV Smart advanced options", "data": {"app_launch_method": "Applications launch method used", "dump_apps": "Dump apps list on log file at startup (when possible)", "use_mute_check": "Use volume mute status to detect fake power ON", "wol_repeat": "Number of time WOL packet is sent to turn on TV", "power_on_delay": "Seconds to delay power ON status", "ping_port": "TCP port used to check power status (0 for ICMP)", "ext_power_entity": "Binary sensor to help detect power status", "toggle_art_mode": "Power button switch to art mode (Frame TV only)"}}, "sync_ent": {"title": "SamsungTV Smart synched entities", "data": {"sync_turn_off": "List of entities to Power OFF with TV", "sync_turn_on": "List of entities to power ON with TV"}}, "app_list": {"title": "SamsungTV Smart applications list configuration", "data": {"app_list": "Applications list:"}}, "channel_list": {"title": "SamsungTV Smart channels list configuration", "data": {"channel_list": "Channels list:"}}, "source_list": {"title": "SamsungTV Smart sources list configuration", "data": {"source_list": "Sources list:"}}}, "error": {"invalid_tv_list": "Invalid format. Please check documentation"}}}