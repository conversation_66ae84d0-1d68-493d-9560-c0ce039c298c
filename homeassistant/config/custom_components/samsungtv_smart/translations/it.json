{"config": {"abort": {"already_configured": "Questo Samsung TV è già configurato.", "already_in_progress": "La configurazione di Samsung TV è già in corso.", "host_unique_id": "Riconfigurazione non possibile poichè il nome host è usato come unique id. Rimuovere questa entry e configurarne una nuova.", "reconfigure_successful": "Riconfigurazione Samsung TV completata correttamente.", "unsupported_version": "Questa integrazione richiede almeno la versione {req_ver} di HomeAssistant, tu stai usando la versione {run_ver}."}, "error": {"auth_missing": "Home Assistant non è autorizzato a connettersi a questo Samsung TV. Controlla le impostazioni del tuo TV per autorizzare Home Assistant.", "invalid_host": "Nome host non valido.", "not_successful": "Impossibile aprire la connessione WebSocket con questo dispositivo Samsung TV.", "not_supported": "Questo dispositivo Samsung TV non è attualmente supportato.", "only_key_or_st": "Specificare solo il token di accesso o l'entry SmartThings.", "st_api_key_fail": "Errore nel recuperare il token di accesso dall'entry SmartThings selezionata.", "st_device_not_found": "SmartThings TV deviceID non trovato.", "st_device_used": "SmartThings TV deviceID già utilizzato.", "wrong_api_key": "SmartThings token errato."}, "flow_title": "SamsungTV Smart: {model}", "step": {"confirm": {"description": "Vuoi configurare Samsung TV {model}? Se non hai mai connesso Home Assistant in precedenza, dovresti vedere un messaggio sul tuo TV in cui è richiesta l'autorizzazione. Le configurazioni manuali per questo TV verranno sovrascritte."}, "user": {"data": {"host": "Host o indirizzo IP", "name": "Nome dell'entità", "use_ha_name_for_ws": "Usare il nome dell'istanza HA per identificarsi sulla TV", "api_key": "SmartThings token generato (opzionale)", "st_entry_unique_id": "Entry SmartThings usata per fornire le credenziali SmartThings"}, "description": "Inserisci le informazioni del tuo Samsung TV. L'uso di SmartThings è opzionale ma fortemente consigliato.\nDopo aver confermato i dati, dovresti vedere un messaggio sul TV in cui è richiesta l'autorizzazione."}, "reconfigure": {"data": {"host": "Host o indirizzo IP", "api_key": "SmartThings token generato (opzionale, se vuoto verrà usato quello esistente)", "st_entry_unique_id": "Entry SmartThings usata per fornire le credenziali SmartThings"}, "description": "Inserisci le informazioni da aggiornare del tuo Samsung TV. Le configurazioni SmartThings possono essere cambiate ma non rimosse.\nDopo aver confermato i dati, potresti vedere un messaggio sul TV in cui è richiesta l'autorizzazione."}, "stdevice": {"data": {"st_devices": "SmartThings TV"}, "description": "Hai più di un TV configurato sul tuo account. Seleziona il TV che stai configurando dalla lista."}, "stdeviceid": {"data": {"device_id": "SmartThings TV deviceID"}, "description": "Identificazione automatica del deviceID SmartThings fallita. Per continuare devi identificare il deviceID sul sito SmartThings (vedere documentazione) e inserirlo qui."}}}, "options": {"step": {"init": {"title": "Opzioni SamsungTV Smart", "data": {"use_st_status_info": "Usa informazioni Stato TV da SmartThings", "use_st_channel_info": "Usa informazioni Canale TV da SmartThings", "show_channel_number": "Usa informazioni Numero Canale TV da SmartThings", "app_load_method": "Modalità caricamento lista applicazioni all'avvio", "logo_option": "Visualizza il logo per sorgenti, apps e canali conosciuti", "use_local_logo": "Permetti l'uso delle immagini logo locali", "power_on_method": "<PERSON>odo usato per accendere la TV", "show_adv_opt": "Mostra menu opzioni"}}, "menu": {"title": "Menù opzioni SamsungTV Smart", "menu_options": {"adv_opt": "Opzioni avanzate", "app_list": "Configurazione lista applicazioni", "channel_list": "Configurazione lista canali", "init": "Opzioni standard", "save_exit": "Salva le opzioni ed esci", "source_list": "Configurazione lista sorgenti", "sync_ent": "Configurazione entità collegate"}}, "adv_opt": {"title": "Opzioni avanzate SamsungTV Smart", "data": {"app_launch_method": "Metodo usato per lanciare le applicazioni", "dump_apps": "Mostra lista apps nel file di log all'avvio (se possibile)", "use_mute_check": "Utilizza lo stato di volume muto per identificare false accensioni", "wol_repeat": "Numero di volte che il pacchetto WOL viene inviato per accendere la TV", "power_on_delay": "Secondi di ritardo per passaggio allo stato ON", "ping_port": "Porta TCP usata per identificare lo stato (0 per ICMP)", "ext_power_entity": "Binary sensor usato per aiutare a identificare lo stato", "toggle_art_mode": "Pulsante di accensione passa a art mode (solo per Frame TV)"}}, "sync_ent": {"title": "Entità collegate SamsungTV Smart", "data": {"sync_turn_off": "Elenco entità da Spegnere con la TV", "sync_turn_on": "Elenco entità da Accendere con la TV"}}, "app_list": {"title": "Configurazione lista applicazioni SamsungTV Smart", "data": {"app_list": "Lista applicazioni:"}}, "channel_list": {"title": "Configurazione lista canali SamsungTV Smart", "data": {"channel_list": "Lista canali:"}}, "source_list": {"title": "Configurazione lista sorgenti SamsungTV Smart", "data": {"source_list": "Lista sorgenti:"}}}, "error": {"invalid_tv_list": "Formato not valido. Controlla la documentazione"}}}