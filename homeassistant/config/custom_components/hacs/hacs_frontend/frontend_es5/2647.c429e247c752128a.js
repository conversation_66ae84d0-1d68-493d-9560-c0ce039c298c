/*! For license information please see 2647.c429e247c752128a.js.LICENSE.txt */
"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["2647"],{84632:function(t,e,o){o.d(e,{Q2:function(){return n},ou:function(){return i}});o(71695),o(19134),o(11740),o(47706),o(39527),o(41360),o(47021),o(71513),o(75656),o(50100),o(18084);var r=o(13089);const n=()=>{const t={},e=new URLSearchParams(r.E.location.search);for(const[o,r]of e.entries())t[o]=r;return t},i=t=>{const e=new URLSearchParams;return Object.entries(t).forEach((([t,o])=>{e.append(t,o)})),e.toString()}},31622:function(t,e,o){o.d(e,{z:()=>g});var r=o("9065"),n=o("50778"),i=(o("71695"),o("47021"),o("93695"),o("16060"),o("4428")),d=o("91532"),a=o("57243"),p=o("35359"),c=o("20552");let l,s,u,b,m=t=>t;class x extends a.oi{constructor(){super(...arguments),this.raised=!1,this.unelevated=!1,this.outlined=!1,this.dense=!1,this.disabled=!1,this.trailingIcon=!1,this.fullwidth=!1,this.icon="",this.label="",this.expandContent=!1,this.shouldRenderRipple=!1,this.rippleHandlers=new d.A((()=>(this.shouldRenderRipple=!0,this.ripple)))}renderOverlay(){return(0,a.dy)(l||(l=m``))}renderRipple(){const t=this.raised||this.unelevated;return this.shouldRenderRipple?(0,a.dy)(s||(s=m`<mwc-ripple class="ripple" .primary="${0}" .disabled="${0}"></mwc-ripple>`),!t,this.disabled):""}focus(){const t=this.buttonElement;t&&(this.rippleHandlers.startFocus(),t.focus())}blur(){const t=this.buttonElement;t&&(this.rippleHandlers.endFocus(),t.blur())}getRenderClasses(){return{"mdc-button--raised":this.raised,"mdc-button--unelevated":this.unelevated,"mdc-button--outlined":this.outlined,"mdc-button--dense":this.dense}}render(){return(0,a.dy)(u||(u=m` <button id="button" class="mdc-button ${0}" ?disabled="${0}" aria-label="${0}" aria-haspopup="${0}" @focus="${0}" @blur="${0}" @mousedown="${0}" @mouseenter="${0}" @mouseleave="${0}" @touchstart="${0}" @touchend="${0}" @touchcancel="${0}"> ${0} ${0} <span class="leading-icon"> <slot name="icon"> ${0} </slot> </span> <span class="mdc-button__label">${0}</span> <span class="slot-container ${0}"> <slot></slot> </span> <span class="trailing-icon"> <slot name="trailingIcon"> ${0} </slot> </span> </button>`),(0,p.$)(this.getRenderClasses()),this.disabled,this.label||this.icon,(0,c.o)(this.ariaHasPopup),this.handleRippleFocus,this.handleRippleBlur,this.handleRippleActivate,this.handleRippleMouseEnter,this.handleRippleMouseLeave,this.handleRippleActivate,this.handleRippleDeactivate,this.handleRippleDeactivate,this.renderOverlay(),this.renderRipple(),this.icon&&!this.trailingIcon?this.renderIcon():"",this.label,(0,p.$)({flex:this.expandContent}),this.icon&&this.trailingIcon?this.renderIcon():"")}renderIcon(){return(0,a.dy)(b||(b=m` <mwc-icon class="mdc-button__icon"> ${0} </mwc-icon>`),this.icon)}handleRippleActivate(t){const e=()=>{window.removeEventListener("mouseup",e),this.handleRippleDeactivate()};window.addEventListener("mouseup",e),this.rippleHandlers.startPress(t)}handleRippleDeactivate(){this.rippleHandlers.endPress()}handleRippleMouseEnter(){this.rippleHandlers.startHover()}handleRippleMouseLeave(){this.rippleHandlers.endHover()}handleRippleFocus(){this.rippleHandlers.startFocus()}handleRippleBlur(){this.rippleHandlers.endFocus()}}x.shadowRootOptions={mode:"open",delegatesFocus:!0},(0,r.gn)([i.L,(0,n.Cb)({type:String,attribute:"aria-haspopup"})],x.prototype,"ariaHasPopup",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],x.prototype,"raised",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],x.prototype,"unelevated",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],x.prototype,"outlined",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean})],x.prototype,"dense",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],x.prototype,"disabled",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,attribute:"trailingicon"})],x.prototype,"trailingIcon",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],x.prototype,"fullwidth",void 0),(0,r.gn)([(0,n.Cb)({type:String})],x.prototype,"icon",void 0),(0,r.gn)([(0,n.Cb)({type:String})],x.prototype,"label",void 0),(0,r.gn)([(0,n.Cb)({type:Boolean})],x.prototype,"expandContent",void 0),(0,r.gn)([(0,n.IO)("#button")],x.prototype,"buttonElement",void 0),(0,r.gn)([(0,n.GC)("mwc-ripple")],x.prototype,"ripple",void 0),(0,r.gn)([(0,n.SB)()],x.prototype,"shouldRenderRipple",void 0),(0,r.gn)([(0,n.hO)({passive:!0})],x.prototype,"handleRippleActivate",null);var h=o("22344");let g=class extends x{};g.styles=[h.W],g=(0,r.gn)([(0,n.Mo)("mwc-button")],g)},22344:function(t,e,o){o.d(e,{W:function(){return n}});let r;const n=(0,o(57243).iv)(r||(r=(t=>t)`.mdc-button{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto,sans-serif;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:.875rem;font-size:var(--mdc-typography-button-font-size, .875rem);line-height:2.25rem;line-height:var(--mdc-typography-button-line-height, 2.25rem);font-weight:500;font-weight:var(--mdc-typography-button-font-weight,500);letter-spacing:.0892857143em;letter-spacing:var(--mdc-typography-button-letter-spacing, .0892857143em);text-decoration:none;text-decoration:var(--mdc-typography-button-text-decoration,none);text-transform:uppercase;text-transform:var(--mdc-typography-button-text-transform,uppercase)}.mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:0;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(.4, 0, .2, 1);background-color:#fff;background-color:var(--mdc-elevation-overlay-color,#fff)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:0;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:0 0}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:0}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}.mdc-button .mdc-button__icon[dir=rtl],[dir=rtl] .mdc-button .mdc-button__icon{margin-left:8px;margin-right:0}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{display:none}@media screen and (forced-colors:active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{pointer-events:none;border:2px solid transparent;border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);height:calc(100% + 4px);width:calc(100% + 4px);display:block}}@media screen and (forced-colors:active)and (forced-colors:active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{border-color:CanvasText}}@media screen and (forced-colors:active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring::after,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring::after{content:"";border:2px solid transparent;border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);height:calc(100% + 4px);width:calc(100% + 4px)}}@media screen and (forced-colors:active)and (forced-colors:active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring::after,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring::after{border-color:CanvasText}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}.mdc-button__label+.mdc-button__icon[dir=rtl],[dir=rtl] .mdc-button__label+.mdc-button__icon{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(.4, 0, .2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:transparent}.mdc-button{height:36px;border-radius:4px;border-radius:var(--mdc-shape-small,4px)}.mdc-button:not(:disabled){color:#6200ee;color:var(--mdc-theme-primary,#6200ee)}.mdc-button:disabled{color:rgba(0,0,0,.38)}.mdc-button .mdc-button__icon{font-size:1.125rem;width:1.125rem;height:1.125rem}.mdc-button .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-shape-small,4px)}.mdc-button--raised,.mdc-button--unelevated{height:36px;border-radius:4px;border-radius:var(--mdc-shape-small,4px)}.mdc-button--raised:not(:disabled),.mdc-button--unelevated:not(:disabled){background-color:#6200ee;background-color:var(--mdc-theme-primary,#6200ee)}.mdc-button--raised:disabled,.mdc-button--unelevated:disabled{background-color:rgba(0,0,0,.12)}.mdc-button--raised:not(:disabled),.mdc-button--unelevated:not(:disabled){color:#fff;color:var(--mdc-theme-on-primary,#fff)}.mdc-button--raised:disabled,.mdc-button--unelevated:disabled{color:rgba(0,0,0,.38)}.mdc-button--raised .mdc-button__icon,.mdc-button--unelevated .mdc-button__icon{font-size:1.125rem;width:1.125rem;height:1.125rem}.mdc-button--raised .mdc-button__ripple,.mdc-button--unelevated .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-shape-small,4px)}.mdc-button--outlined{height:36px;border-radius:4px;border-radius:var(--mdc-shape-small,4px);padding:0 15px 0 15px;border-width:1px}.mdc-button--outlined:not(:disabled){color:#6200ee;color:var(--mdc-theme-primary,#6200ee)}.mdc-button--outlined:disabled{color:rgba(0,0,0,.38)}.mdc-button--outlined .mdc-button__icon{font-size:1.125rem;width:1.125rem;height:1.125rem}.mdc-button--outlined .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-shape-small,4px)}.mdc-button--outlined:not(:disabled){border-color:rgba(0,0,0,.12)}.mdc-button--outlined:disabled{border-color:rgba(0,0,0,.12)}.mdc-button--outlined.mdc-button--icon-trailing{padding:0 11px 0 15px}.mdc-button--outlined.mdc-button--icon-leading{padding:0 15px 0 11px}.mdc-button--outlined .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:1px}.mdc-button--outlined .mdc-button__touch{left:calc(-1 * 1px);width:calc(100% + 2 * 1px)}.mdc-button--raised{box-shadow:0px 3px 1px -2px rgba(0,0,0,.2),0px 2px 2px 0px rgba(0,0,0,.14),0px 1px 5px 0px rgba(0,0,0,.12);transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1)}.mdc-button--raised:focus,.mdc-button--raised:hover{box-shadow:0px 2px 4px -1px rgba(0,0,0,.2),0px 4px 5px 0px rgba(0,0,0,.14),0px 1px 10px 0px rgba(0,0,0,.12)}.mdc-button--raised:active{box-shadow:0px 5px 5px -3px rgba(0,0,0,.2),0px 8px 10px 1px rgba(0,0,0,.14),0px 3px 14px 2px rgba(0,0,0,.12)}.mdc-button--raised:disabled{box-shadow:0px 0px 0px 0px rgba(0,0,0,.2),0px 0px 0px 0px rgba(0,0,0,.14),0px 0px 0px 0px rgba(0,0,0,.12)}:host{display:inline-flex;outline:0;-webkit-tap-highlight-color:transparent;vertical-align:top}:host([fullwidth]){width:100%}:host([raised]),:host([unelevated]){--mdc-ripple-color:#fff;--mdc-ripple-focus-opacity:0.24;--mdc-ripple-hover-opacity:0.08;--mdc-ripple-press-opacity:0.24}.leading-icon .mdc-button__icon,.leading-icon ::slotted(*),.trailing-icon .mdc-button__icon,.trailing-icon ::slotted(*){margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.leading-icon .mdc-button__icon[dir=rtl],.leading-icon ::slotted([dir=rtl]),.trailing-icon .mdc-button__icon[dir=rtl],.trailing-icon ::slotted([dir=rtl]),[dir=rtl] .leading-icon .mdc-button__icon,[dir=rtl] .leading-icon ::slotted(*),[dir=rtl] .trailing-icon .mdc-button__icon,[dir=rtl] .trailing-icon ::slotted(*){margin-left:8px;margin-right:0}.trailing-icon .mdc-button__icon,.trailing-icon ::slotted(*){margin-left:8px;margin-right:0}.trailing-icon .mdc-button__icon[dir=rtl],.trailing-icon ::slotted([dir=rtl]),[dir=rtl] .trailing-icon .mdc-button__icon,[dir=rtl] .trailing-icon ::slotted(*){margin-left:0;margin-right:8px}.slot-container{display:inline-flex;align-items:center;justify-content:center}.slot-container.flex{flex:auto}.mdc-button{flex:auto;overflow:hidden;padding-left:8px;padding-left:var(--mdc-button-horizontal-padding,8px);padding-right:8px;padding-right:var(--mdc-button-horizontal-padding,8px)}.mdc-button--raised{box-shadow:0px 3px 1px -2px rgba(0,0,0,.2),0px 2px 2px 0px rgba(0,0,0,.14),0px 1px 5px 0px rgba(0,0,0,.12);box-shadow:var(--mdc-button-raised-box-shadow,0px 3px 1px -2px rgba(0,0,0,.2),0px 2px 2px 0px rgba(0,0,0,.14),0px 1px 5px 0px rgba(0,0,0,.12))}.mdc-button--raised:focus{box-shadow:0px 2px 4px -1px rgba(0,0,0,.2),0px 4px 5px 0px rgba(0,0,0,.14),0px 1px 10px 0px rgba(0,0,0,.12);box-shadow:var(--mdc-button-raised-box-shadow-focus,var(--mdc-button-raised-box-shadow-hover,0px 2px 4px -1px rgba(0,0,0,.2),0px 4px 5px 0px rgba(0,0,0,.14),0px 1px 10px 0px rgba(0,0,0,.12)))}.mdc-button--raised:hover{box-shadow:0px 2px 4px -1px rgba(0,0,0,.2),0px 4px 5px 0px rgba(0,0,0,.14),0px 1px 10px 0px rgba(0,0,0,.12);box-shadow:var(--mdc-button-raised-box-shadow-hover,0px 2px 4px -1px rgba(0,0,0,.2),0px 4px 5px 0px rgba(0,0,0,.14),0px 1px 10px 0px rgba(0,0,0,.12))}.mdc-button--raised:active{box-shadow:0px 5px 5px -3px rgba(0,0,0,.2),0px 8px 10px 1px rgba(0,0,0,.14),0px 3px 14px 2px rgba(0,0,0,.12);box-shadow:var(--mdc-button-raised-box-shadow-active,0px 5px 5px -3px rgba(0,0,0,.2),0px 8px 10px 1px rgba(0,0,0,.14),0px 3px 14px 2px rgba(0,0,0,.12))}.mdc-button--raised:disabled{box-shadow:0px 0px 0px 0px rgba(0,0,0,.2),0px 0px 0px 0px rgba(0,0,0,.14),0px 0px 0px 0px rgba(0,0,0,.12);box-shadow:var(--mdc-button-raised-box-shadow-disabled,0px 0px 0px 0px rgba(0,0,0,.2),0px 0px 0px 0px rgba(0,0,0,.14),0px 0px 0px 0px rgba(0,0,0,.12))}.mdc-button--raised,.mdc-button--unelevated{padding-left:16px;padding-left:var(--mdc-button-horizontal-padding,16px);padding-right:16px;padding-right:var(--mdc-button-horizontal-padding,16px)}.mdc-button--outlined{border-width:1px;border-width:var(--mdc-button-outline-width,1px);padding-left:calc(16px - 1px);padding-left:calc(var(--mdc-button-horizontal-padding,16px) - var(--mdc-button-outline-width,1px));padding-right:calc(16px - 1px);padding-right:calc(var(--mdc-button-horizontal-padding,16px) - var(--mdc-button-outline-width,1px))}.mdc-button--outlined:not(:disabled){border-color:rgba(0,0,0,.12);border-color:var(--mdc-button-outline-color,rgba(0,0,0,.12))}.mdc-button--outlined .ripple{top:calc(-1 * 1px);top:calc(-1 * var(--mdc-button-outline-width,1px));left:calc(-1 * 1px);left:calc(-1 * var(--mdc-button-outline-width,1px));right:initial;right:initial;border-width:1px;border-width:var(--mdc-button-outline-width,1px);border-style:solid;border-color:transparent}.mdc-button--outlined .ripple[dir=rtl],[dir=rtl] .mdc-button--outlined .ripple{left:initial;left:initial;right:calc(-1 * 1px);right:calc(-1 * var(--mdc-button-outline-width,1px))}.mdc-button--dense{height:28px;margin-top:0;margin-bottom:0}.mdc-button--dense .mdc-button__touch{height:100%}:host([disabled]){pointer-events:none}:host([disabled]) .mdc-button{color:rgba(0,0,0,.38);color:var(--mdc-button-disabled-ink-color,rgba(0,0,0,.38))}:host([disabled]) .mdc-button--raised,:host([disabled]) .mdc-button--unelevated{background-color:rgba(0,0,0,.12);background-color:var(--mdc-button-disabled-fill-color,rgba(0,0,0,.12))}:host([disabled]) .mdc-button--outlined{border-color:rgba(0,0,0,.12);border-color:var(--mdc-button-disabled-outline-color,rgba(0,0,0,.12))}`))},93695:function(t,e,o){var r=o("9065"),n=o("57243"),i=o("50778");let d;const a=(0,n.iv)(d||(d=(t=>t)`:host{font-family:var(--mdc-icon-font, "Material Icons");font-weight:400;font-style:normal;font-size:var(--mdc-icon-size, 24px);line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:"liga"}`));let p,c=t=>t,l=class extends n.oi{render(){return(0,n.dy)(p||(p=c`<span><slot></slot></span>`))}};l.styles=[a],l=(0,r.gn)([(0,i.Mo)("mwc-icon")],l)},60901:function(t,e,o){o.r(e),o.d(e,{REDIRECTS:function(){return s}});var r=o(73577),n=(o(52247),o(71695),o(97499),o(47021),o(57243)),i=o(50778),d=o(64364),a=o(84632);o(3035);let p,c,l=t=>t;const s={hacs_repository:{redirect:"/hacs/repository",params:{owner:"string",repository:"string",category:"string?"}}};(0,r.Z)([(0,i.Mo)("hacs-my-redirect")],(function(t,e){return{F:class extends e{constructor(...e){super(...e),t(this)}},d:[{kind:"field",decorators:[(0,i.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,i.Cb)({attribute:!1})],key:"hacs",value:void 0},{kind:"field",decorators:[(0,i.Cb)({attribute:!1})],key:"route",value:void 0},{kind:"field",decorators:[(0,i.SB)()],key:"_error",value:void 0},{kind:"method",key:"firstUpdated",value:function(t){const e=this.route.path.indexOf("/",1),o=this.route.path.substr(e+1),r=s[o];if(!r)return void(this._error=this.hacs.localize("my.not_supported",{link:(0,n.dy)(p||(p=l`<a target="_blank" rel="noreferrer noopener" href="https://my.home-assistant.io/faq.html#supported-pages"> ${0} </a>`),this.hacs.localize("my.faq_link"))}));let i;try{i=this._createRedirectUrl(r)}catch(a){return void(this._error=this.hacs.localize("my.error"))}(0,d.c)(i,{replace:!0})}},{kind:"method",key:"render",value:function(){return this._error?(0,n.dy)(c||(c=l`<hass-error-screen .error="${0}"></hass-error-screen>`),this._error):n.Ld}},{kind:"method",key:"_createRedirectUrl",value:function(t){const e=this._createRedirectParams(t);return`${t.redirect}${e}`}},{kind:"method",key:"_createRedirectParams",value:function(t){const e=(0,a.Q2)();if(!t.params&&!Object.keys(e).length)return"";const o={};for(const[r,n]of Object.entries(t.params||{}))if(e[r]||!n.endsWith("?")){if(!e[r]||!this._checkParamType(n,e[r]))throw Error();o[r]=e[r]}return`?${(0,a.ou)(o)}`}},{kind:"method",key:"_checkParamType",value:function(t,e){return"string"===t||"string?"===t}}]}}),n.oi)}}]);
//# sourceMappingURL=2647.c429e247c752128a.js.map