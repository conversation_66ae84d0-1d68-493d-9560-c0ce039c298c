"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["279"],{34082:function(e,t,i){i.d(t,{T:function(){return s}});i(19134),i(5740);const n=/^(\w+)\.(\w+)$/,s=e=>n.test(e)},94999:function(e,t,i){i.a(e,(async function(e,t){try{var n=i(73577),s=(i(19083),i(71695),i(40251),i(61006),i(39527),i(99790),i(13334),i(47021),i(57243)),a=i(50778),l=i(27486),r=i(11297),d=i(34082),u=i(59498),o=e([u]);u=(o.then?(await o)():o)[0];let c,h,y,v=e=>e;(0,n.Z)([(0,a.Mo)("ha-entities-picker")],(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"field",decorators:[(0,a.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array})],key:"value",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Boolean})],key:"disabled",value(){return!1}},{kind:"field",decorators:[(0,a.Cb)({type:Boolean})],key:"required",value(){return!1}},{kind:"field",decorators:[(0,a.Cb)()],key:"helper",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"include-domains"})],key:"includeDomains",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"exclude-domains"})],key:"excludeDomains",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"include-device-classes"})],key:"includeDeviceClasses",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"include-unit-of-measurement"})],key:"includeUnitOfMeasurement",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"include-entities"})],key:"includeEntities",value:void 0},{kind:"field",decorators:[(0,a.Cb)({type:Array,attribute:"exclude-entities"})],key:"excludeEntities",value:void 0},{kind:"field",decorators:[(0,a.Cb)({attribute:"picked-entity-label"})],key:"pickedEntityLabel",value:void 0},{kind:"field",decorators:[(0,a.Cb)({attribute:"pick-entity-label"})],key:"pickEntityLabel",value:void 0},{kind:"field",decorators:[(0,a.Cb)({attribute:!1})],key:"entityFilter",value:void 0},{kind:"field",decorators:[(0,a.Cb)({attribute:!1,type:Array})],key:"createDomains",value:void 0},{kind:"method",key:"render",value:function(){if(!this.hass)return s.Ld;const e=this._currentEntities;return(0,s.dy)(c||(c=v` ${0} <div> <ha-entity-picker allow-custom-entity .hass="${0}" .includeDomains="${0}" .excludeDomains="${0}" .includeEntities="${0}" .excludeEntities="${0}" .includeDeviceClasses="${0}" .includeUnitOfMeasurement="${0}" .entityFilter="${0}" .label="${0}" .helper="${0}" .disabled="${0}" .createDomains="${0}" .required="${0}" @value-changed="${0}"></ha-entity-picker> </div> `),e.map((e=>(0,s.dy)(h||(h=v` <div> <ha-entity-picker allow-custom-entity .curValue="${0}" .hass="${0}" .includeDomains="${0}" .excludeDomains="${0}" .includeEntities="${0}" .excludeEntities="${0}" .includeDeviceClasses="${0}" .includeUnitOfMeasurement="${0}" .entityFilter="${0}" .value="${0}" .label="${0}" .disabled="${0}" .createDomains="${0}" @value-changed="${0}"></ha-entity-picker> </div> `),e,this.hass,this.includeDomains,this.excludeDomains,this.includeEntities,this.excludeEntities,this.includeDeviceClasses,this.includeUnitOfMeasurement,this.entityFilter,e,this.pickedEntityLabel,this.disabled,this.createDomains,this._entityChanged))),this.hass,this.includeDomains,this.excludeDomains,this.includeEntities,this._excludeEntities(this.value,this.excludeEntities),this.includeDeviceClasses,this.includeUnitOfMeasurement,this.entityFilter,this.pickEntityLabel,this.helper,this.disabled,this.createDomains,this.required&&!e.length,this._addEntity)}},{kind:"field",key:"_excludeEntities",value(){return(0,l.Z)(((e,t)=>void 0===e?t:[...t||[],...e]))}},{kind:"get",key:"_currentEntities",value:function(){return this.value||[]}},{kind:"method",key:"_updateEntities",value:async function(e){this.value=e,(0,r.B)(this,"value-changed",{value:e})}},{kind:"method",key:"_entityChanged",value:function(e){e.stopPropagation();const t=e.currentTarget.curValue,i=e.detail.value;if(i===t||void 0!==i&&!(0,d.T)(i))return;const n=this._currentEntities;i&&!n.includes(i)?this._updateEntities(n.map((e=>e===t?i:e))):this._updateEntities(n.filter((e=>e!==t)))}},{kind:"method",key:"_addEntity",value:async function(e){e.stopPropagation();const t=e.detail.value;if(!t)return;if(e.currentTarget.value="",!t)return;const i=this._currentEntities;i.includes(t)||this._updateEntities([...i,t])}},{kind:"field",static:!0,key:"styles",value(){return(0,s.iv)(y||(y=v`div{margin-top:8px}`))}}]}}),s.oi);t()}catch(c){t(c)}}))},92697:function(e,t,i){i.a(e,(async function(e,n){try{i.r(t),i.d(t,{HaEntitySelector:function(){return m}});var s=i(73577),a=i(72621),l=(i(71695),i(39527),i(99790),i(36993),i(47021),i(57243)),r=i(50778),d=i(24785),u=i(11297),o=i(82659),c=i(45634),h=i(94999),y=i(59498),v=e([h,y]);[h,y]=v.then?(await v)():v;let k,f,b,p=e=>e,m=(0,s.Z)([(0,r.Mo)("ha-selector-entity")],(function(e,t){class i extends t{constructor(...t){super(...t),e(this)}}return{F:i,d:[{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"selector",value:void 0},{kind:"field",decorators:[(0,r.SB)()],key:"_entitySources",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"value",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"label",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"helper",value:void 0},{kind:"field",decorators:[(0,r.Cb)({type:Boolean})],key:"disabled",value(){return!1}},{kind:"field",decorators:[(0,r.Cb)({type:Boolean})],key:"required",value(){return!0}},{kind:"field",decorators:[(0,r.SB)()],key:"_createDomains",value:void 0},{kind:"method",key:"_hasIntegration",value:function(e){var t;return(null===(t=e.entity)||void 0===t?void 0:t.filter)&&(0,d.r)(e.entity.filter).some((e=>e.integration))}},{kind:"method",key:"willUpdate",value:function(e){var t,i;e.has("selector")&&void 0!==this.value&&(null!==(t=this.selector.entity)&&void 0!==t&&t.multiple&&!Array.isArray(this.value)?(this.value=[this.value],(0,u.B)(this,"value-changed",{value:this.value})):null!==(i=this.selector.entity)&&void 0!==i&&i.multiple||!Array.isArray(this.value)||(this.value=this.value[0],(0,u.B)(this,"value-changed",{value:this.value})))}},{kind:"method",key:"render",value:function(){var e,t,i;return this._hasIntegration(this.selector)&&!this._entitySources?l.Ld:null!==(e=this.selector.entity)&&void 0!==e&&e.multiple?(0,l.dy)(f||(f=p` ${0} <ha-entities-picker .hass="${0}" .value="${0}" .helper="${0}" .includeEntities="${0}" .excludeEntities="${0}" .entityFilter="${0}" .createDomains="${0}" .disabled="${0}" .required="${0}"></ha-entities-picker> `),this.label?(0,l.dy)(b||(b=p`<label>${0}</label>`),this.label):"",this.hass,this.value,this.helper,this.selector.entity.include_entities,this.selector.entity.exclude_entities,this._filterEntities,this._createDomains,this.disabled,this.required):(0,l.dy)(k||(k=p`<ha-entity-picker .hass="${0}" .value="${0}" .label="${0}" .helper="${0}" .includeEntities="${0}" .excludeEntities="${0}" .entityFilter="${0}" .createDomains="${0}" .disabled="${0}" .required="${0}" allow-custom-entity></ha-entity-picker>`),this.hass,this.value,this.label,this.helper,null===(t=this.selector.entity)||void 0===t?void 0:t.include_entities,null===(i=this.selector.entity)||void 0===i?void 0:i.exclude_entities,this._filterEntities,this._createDomains,this.disabled,this.required)}},{kind:"method",key:"updated",value:function(e){(0,a.Z)(i,"updated",this,3)([e]),e.has("selector")&&this._hasIntegration(this.selector)&&!this._entitySources&&(0,o.m)(this.hass).then((e=>{this._entitySources=e})),e.has("selector")&&(this._createDomains=(0,c.bq)(this.selector))}},{kind:"field",key:"_filterEntities",value(){return e=>{var t;return null===(t=this.selector)||void 0===t||null===(t=t.entity)||void 0===t||!t.filter||(0,d.r)(this.selector.entity.filter).some((t=>(0,c.lV)(t,e,this._entitySources)))}}}]}}),l.oi);n()}catch(k){n(k)}}))},82659:function(e,t,i){i.d(t,{m:()=>a});i("71695"),i("40251"),i("47021");const n=async(e,t,i,s,a,...l)=>{const r=a,d=r[e],u=d=>s&&s(a,d.result)!==d.cacheKey?(r[e]=void 0,n(e,t,i,s,a,...l)):d.result;if(d)return d instanceof Promise?d.then(u):u(d);const o=i(a,...l);return r[e]=o,o.then((i=>{r[e]={result:i,cacheKey:null==s?void 0:s(a,i)},setTimeout((()=>{r[e]=void 0}),t)}),(()=>{r[e]=void 0})),o},s=e=>e.callWS({type:"entity/source"}),a=e=>n("_entitySources",3e4,s,(e=>Object.keys(e.states).length),e)}}]);
//# sourceMappingURL=279.c8939e97d9cf1f24.js.map