"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["669"],{7433:function(e,i,t){t.a(e,(async function(e,s){try{t.r(i),t.d(i,{FlowPreviewGeneric:function(){return b}});var r=t(73577),d=t(72621),n=(t(71695),t(19423),t(40251),t(47021),t(57243)),o=t(50778),a=t(44699),u=t(80917),l=t(56587),h=t(11297),c=(t(17949),e([u]));u=(c.then?(await c)():c)[0];let v,_,k=e=>e,b=(0,r.Z)([(0,o.Mo)("flow-preview-generic")],(function(e,i){class t extends i{constructor(...i){super(...i),e(this)}}return{F:t,d:[{kind:"field",decorators:[(0,o.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,o.Cb)({attribute:!1})],key:"flowType",value:void 0},{kind:"field",key:"handler",value:void 0},{kind:"field",decorators:[(0,o.Cb)()],key:"domain",value:void 0},{kind:"field",decorators:[(0,o.Cb)({attribute:!1})],key:"stepId",value:void 0},{kind:"field",decorators:[(0,o.Cb)({attribute:!1})],key:"flowId",value:void 0},{kind:"field",decorators:[(0,o.Cb)({attribute:!1})],key:"stepData",value:void 0},{kind:"field",decorators:[(0,o.SB)()],key:"_preview",value:void 0},{kind:"field",decorators:[(0,o.SB)()],key:"_error",value:void 0},{kind:"field",key:"_unsub",value:void 0},{kind:"method",key:"disconnectedCallback",value:function(){(0,d.Z)(t,"disconnectedCallback",this,3)([]),this._unsub&&(this._unsub.then((e=>e())),this._unsub=void 0)}},{kind:"method",key:"willUpdate",value:function(e){e.has("stepData")&&this._debouncedSubscribePreview()}},{kind:"method",key:"render",value:function(){return this._error?(0,n.dy)(v||(v=k`<ha-alert alert-type="error">${0}</ha-alert>`),this._error):(0,n.dy)(_||(_=k`<entity-preview-row .hass="${0}" .stateObj="${0}"></entity-preview-row>`),this.hass,this._preview)}},{kind:"field",key:"_setPreview",value(){return e=>{const i=(new Date).toISOString();this._preview=Object.assign({entity_id:`${this.stepId}.___flow_preview___`,last_changed:i,last_updated:i,context:{id:"",parent_id:null,user_id:null}},e)}}},{kind:"field",key:"_debouncedSubscribePreview",value(){return(0,l.D)((()=>{this._subscribePreview()}),250)}},{kind:"method",key:"_subscribePreview",value:async function(){if(this._unsub&&((await this._unsub)(),this._unsub=void 0),"repair_flow"!==this.flowType)try{this._unsub=(0,a.H)(this.hass,this.domain,this.flowId,this.flowType,this.stepData,this._setPreview),(0,h.B)(this,"set-flow-errors",{errors:{}})}catch(e){"string"==typeof e.message?this._error=e.message:(this._error=void 0,(0,h.B)(this,"set-flow-errors",e.message)),this._unsub=void 0,this._preview=void 0}}}]}}),n.oi);s()}catch(v){s(v)}}))}}]);
//# sourceMappingURL=669.bf467f9fedb637ee.js.map