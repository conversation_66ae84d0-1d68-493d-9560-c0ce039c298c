"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["2309"],{51198:function(r,o,t){t.a(r,(async function(r,e){try{t.r(o),t.d(o,{EditorState:function(){return l.yy},EditorView:function(){return p.tk},autocompletion:function(){return m.ys},crosshairCursor:function(){return p.S2},defaultKeymap:function(){return a.wQ},drawSelection:function(){return p.Uw},foldGutter:function(){return n.mi},foldingCompartment:function(){return h},foldingOnIndent:function(){return w},haSyntaxHighlighting:function(){return C},haTheme:function(){return J},highlightActiveLine:function(){return p.ZO},highlightSelectionMatches:function(){return u.sW},highlightingFor:function(){return n.QN},history:function(){return a.m8},historyKeymap:function(){return a.f$},indentationMarkers:function(){return g.C},keymap:function(){return p.$f},langCompartment:function(){return b},langs:function(){return f},lineNumbers:function(){return p.Eu},linewrapCompartment:function(){return v},readonlyCompartment:function(){return x},rectangularSelection:function(){return p.Zs},searchKeymap:function(){return u.Lp},tabKeyBindings:function(){return y},tags:function(){return d.pJ}});t(19134),t(47706),t(81804);var a=t(8877),n=t(51801),c=t(3191),i=t(91847),l=t(96381),p=t(10857),d=t(13419),m=t(14688),u=t(73755),g=t(74092),s=r([m,u,g,a,p,n]);[m,u,g,a,p,n]=s.then?(await s)():s;const f={jinja2:n.il.define(c.b),yaml:n.il.define(i.r)},b=new l.F6,x=new l.F6,v=new l.F6,h=new l.F6,y=[{key:"Tab",run:a.at},{key:"Shift-Tab",run:a.xi}],J=p.tk.theme({"&":{color:"var(--primary-text-color)",backgroundColor:"var(--code-editor-background-color, var(--mdc-text-field-fill-color, whitesmoke))",borderRadius:"var(--mdc-shape-small, 4px) var(--mdc-shape-small, 4px) 0px 0px",caretColor:"var(--secondary-text-color)",height:"var(--code-mirror-height, auto)",maxHeight:"var(--code-mirror-max-height, unset)"},"&.cm-editor.cm-focused":{outline:"none"},"&.cm-focused .cm-cursor":{borderLeftColor:"var(--secondary-text-color)"},".cm-selectionBackground, ::selection":{backgroundColor:"rgba(var(--rgb-primary-color), 0.1)"},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{backgroundColor:"rgba(var(--rgb-primary-color), 0.2)"},".cm-activeLine":{backgroundColor:"rgba(var(--rgb-secondary-text-color), 0.1)"},".cm-scroller":{outline:"none"},".cm-content":{caretColor:"var(--secondary-text-color)",paddingTop:"16px",paddingBottom:"16px"},".cm-panels":{backgroundColor:"var(--primary-background-color)",color:"var(--primary-text-color)"},".cm-panels.top":{borderBottom:"1px solid var(--divider-color)"},".cm-panels.bottom":{borderTop:"1px solid var(--divider-color)"},".cm-button":{border:"1px solid var(--primary-color)",padding:"0px 16px",textTransform:"uppercase",margin:"4px",background:"none",color:"var(--primary-color)",fontFamily:"var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif))",fontSize:"var(--mdc-typography-button-font-size, 0.875rem)",height:"36px",fontWeight:"var(--mdc-typography-button-font-weight, 500)",borderRadius:"4px",letterSpacing:"var(--mdc-typography-button-letter-spacing, 0.0892857em)"},".cm-textfield":{padding:"4px 0px 5px",borderRadius:"0",fontSize:"16px",color:"var(--primary-text-color)",border:"0",background:"none",fontFamily:"Roboto",borderBottom:"1px solid var(--secondary-text-color)",margin:"4px 4px 0","& ::placeholder":{color:"var(--secondary-text-color)"},"&:focus":{outline:"none",borderBottom:"2px solid var(--primary-color)",paddingBottom:"4px"}},".cm-tooltip":{color:"var(--primary-text-color)",backgroundColor:"var(--code-editor-background-color, var(--card-background-color))",border:"1px solid var(--divider-color)",borderRadius:"var(--mdc-shape-medium, 4px)",boxShadow:"0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%)"},"& .cm-tooltip.cm-tooltip-autocomplete > ul > li":{padding:"4px 8px"},"& .cm-tooltip-autocomplete ul li[aria-selected]":{background:"var(--primary-color)",color:"var(--text-primary-color)"},".cm-completionIcon":{display:"none"},".cm-completionDetail":{fontFamily:"Roboto",color:"var(--secondary-text-color)"},"li[aria-selected] .cm-completionDetail":{color:"var(--text-primary-color)"},"& .cm-completionInfo.cm-completionInfo-right":{left:"calc(100% + 4px)"},"& .cm-tooltip.cm-completionInfo":{padding:"4px 8px",marginTop:"-5px"},".cm-selectionMatch":{backgroundColor:"rgba(var(--rgb-primary-color), 0.1)"},".cm-searchMatch":{backgroundColor:"rgba(var(--rgb-accent-color), .2)",outline:"1px solid rgba(var(--rgb-accent-color), .4)"},".cm-searchMatch.selected":{backgroundColor:"rgba(var(--rgb-accent-color), .4)",outline:"1px solid var(--accent-color)"},".cm-gutters":{backgroundColor:"var(--code-editor-gutter-color, var(--secondary-background-color, whitesmoke))",color:"var(--paper-dialog-color, var(--secondary-text-color))",border:"none",borderRight:"1px solid var(--secondary-text-color)",paddingRight:"1px"},"&.cm-focused .cm-gutters":{borderRight:"2px solid var(--primary-color)",paddingRight:"0"},".cm-gutterElement.lineNumber":{color:"inherit"}}),k=n.Qf.define([{tag:d.pJ.keyword,color:"var(--codemirror-keyword, #6262FF)"},{tag:[d.pJ.name,d.pJ.deleted,d.pJ.character,d.pJ.propertyName,d.pJ.macroName],color:"var(--codemirror-property, #905)"},{tag:[d.pJ.function(d.pJ.variableName),d.pJ.labelName],color:"var(--codemirror-variable, #07a)"},{tag:[d.pJ.color,d.pJ.constant(d.pJ.name),d.pJ.standard(d.pJ.name)],color:"var(--codemirror-qualifier, #690)"},{tag:[d.pJ.definition(d.pJ.name),d.pJ.separator],color:"var(--codemirror-def, #8DA6CE)"},{tag:[d.pJ.typeName,d.pJ.className,d.pJ.number,d.pJ.changed,d.pJ.annotation,d.pJ.modifier,d.pJ.self,d.pJ.namespace],color:"var(--codemirror-number, #ca7841)"},{tag:[d.pJ.operator,d.pJ.operatorKeyword,d.pJ.url,d.pJ.escape,d.pJ.regexp,d.pJ.link,d.pJ.special(d.pJ.string)],color:"var(--codemirror-operator, #cda869)"},{tag:d.pJ.comment,color:"var(--codemirror-comment, #777)"},{tag:d.pJ.meta,color:"var(--codemirror-meta, var(--primary-text-color))"},{tag:d.pJ.strong,fontWeight:"bold"},{tag:d.pJ.emphasis,fontStyle:"italic"},{tag:d.pJ.link,color:"var(--primary-color)",textDecoration:"underline"},{tag:d.pJ.heading,fontWeight:"bold"},{tag:d.pJ.atom,color:"var(--codemirror-atom, #F90)"},{tag:d.pJ.bool,color:"var(--codemirror-atom, #F90)"},{tag:d.pJ.special(d.pJ.variableName),color:"var(--codemirror-variable-2, #690)"},{tag:d.pJ.processingInstruction,color:"var(--secondary-text-color)"},{tag:d.pJ.string,color:"var(--codemirror-string, #07a)"},{tag:d.pJ.inserted,color:"var(--codemirror-string2, #07a)"},{tag:d.pJ.invalid,color:"var(--error-color)"}]),C=(0,n.nF)(k),w=n.rs.of(((r,o,t)=>{const e=r.doc.lineAt(o);if(!e.length||!e.text.trim().length)return null;let a=!0;const n=r.doc.lines,c=e.text.search(/\S|$/);let i=o,l=t,p=e;for(;p.number<n;){p=r.doc.line(p.number+1);const o=p.text.search(/\S|$/);if(p.length&&p.text.trim().length&&!(o>c))break;a&&(a=0===p.text.trim().length),l=p.to}return a||r.doc.lineAt(i).number===r.doc.lineAt(l).number?null:(i=e.to,{from:i,to:l})}));e()}catch(f){e(f)}}))}}]);
//# sourceMappingURL=2309.de1b73f332471c37.js.map