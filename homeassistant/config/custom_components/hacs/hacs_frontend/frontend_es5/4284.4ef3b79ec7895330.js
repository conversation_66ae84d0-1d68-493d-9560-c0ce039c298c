"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["4284"],{47899:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{Bt:function(){return c}});n(19083);var i=n(16485),o=n(88977),r=n(50177),u=t([i]);i=(u.then?(await u)():u)[0];const l=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],c=t=>t.first_weekday===r.FS.language?"weekInfo"in Intl.Locale.prototype?new Intl.Locale(t.language).weekInfo.firstDay%7:(0,o.L)(t.language)%7:l.includes(t.first_weekday)?l.indexOf(t.first_weekday):1;a()}catch(l){a(l)}}))},65417:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{WB:function(){return s},p6:function(){return c}});n(63434),n(96829),n(39527),n(67670);var i=n(16485),o=n(27486),r=n(50177),u=n(70691),l=t([i,u]);[i,u]=l.then?(await l)():l;(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{weekday:"long",month:"long",day:"numeric",timeZone:(0,u.f)(t.time_zone,e)})));const c=(t,e,n)=>m(e,n.time_zone).format(t),m=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",month:"long",day:"numeric",timeZone:(0,u.f)(t.time_zone,e)}))),s=((0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",month:"short",day:"numeric",timeZone:(0,u.f)(t.time_zone,e)}))),(t,e,n)=>{var a,i,o,u;const l=d(e,n.time_zone);if(e.date_format===r.t6.language||e.date_format===r.t6.system)return l.format(t);const c=l.formatToParts(t),m=null===(a=c.find((t=>"literal"===t.type)))||void 0===a?void 0:a.value,s=null===(i=c.find((t=>"day"===t.type)))||void 0===i?void 0:i.value,h=null===(o=c.find((t=>"month"===t.type)))||void 0===o?void 0:o.value,f=null===(u=c.find((t=>"year"===t.type)))||void 0===u?void 0:u.value,y=c.at(c.length-1);let v="literal"===(null==y?void 0:y.type)?null==y?void 0:y.value:"";"bg"===e.language&&e.date_format===r.t6.YMD&&(v="");return{[r.t6.DMY]:`${s}${m}${h}${m}${f}${v}`,[r.t6.MDY]:`${h}${m}${s}${m}${f}${v}`,[r.t6.YMD]:`${f}${m}${h}${m}${s}${v}`}[e.date_format]}),d=(0,o.Z)(((t,e)=>{const n=t.date_format===r.t6.system?void 0:t.language;return t.date_format===r.t6.language||(t.date_format,r.t6.system),new Intl.DateTimeFormat(n,{year:"numeric",month:"numeric",day:"numeric",timeZone:(0,u.f)(t.time_zone,e)})}));(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{day:"numeric",month:"short",timeZone:(0,u.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{month:"long",year:"numeric",timeZone:(0,u.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{month:"long",timeZone:(0,u.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",timeZone:(0,u.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{weekday:"long",timeZone:(0,u.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{weekday:"short",timeZone:(0,u.f)(t.time_zone,e)})));a()}catch(c){a(c)}}))},69027:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{o0:function(){return s}});var i=n(16485),o=n(27486),r=n(65417),u=n(823),l=n(70691),c=n(51873),m=t([i,r,u,l]);[i,r,u,l]=m.then?(await m)():m;const s=(t,e,n)=>d(e,n.time_zone).format(t),d=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",month:"long",day:"numeric",hour:(0,c.y)(t)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,c.y)(t)?"h12":"h23",timeZone:(0,l.f)(t.time_zone,e)})));(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",month:"short",day:"numeric",hour:(0,c.y)(t)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,c.y)(t)?"h12":"h23",timeZone:(0,l.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{month:"short",day:"numeric",hour:(0,c.y)(t)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,c.y)(t)?"h12":"h23",timeZone:(0,l.f)(t.time_zone,e)}))),(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{year:"numeric",month:"long",day:"numeric",hour:(0,c.y)(t)?"numeric":"2-digit",minute:"2-digit",second:"2-digit",hourCycle:(0,c.y)(t)?"h12":"h23",timeZone:(0,l.f)(t.time_zone,e)})));a()}catch(s){a(s)}}))},823:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{Vu:function(){return s},Zs:function(){return y},mr:function(){return c},xO:function(){return h}});var i=n(16485),o=n(27486),r=n(70691),u=n(51873),l=t([i,r]);[i,r]=l.then?(await l)():l;const c=(t,e,n)=>m(e,n.time_zone).format(t),m=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{hour:"numeric",minute:"2-digit",hourCycle:(0,u.y)(t)?"h12":"h23",timeZone:(0,r.f)(t.time_zone,e)}))),s=(t,e,n)=>d(e,n.time_zone).format(t),d=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{hour:(0,u.y)(t)?"numeric":"2-digit",minute:"2-digit",second:"2-digit",hourCycle:(0,u.y)(t)?"h12":"h23",timeZone:(0,r.f)(t.time_zone,e)}))),h=(t,e,n)=>f(e,n.time_zone).format(t),f=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat(t.language,{weekday:"long",hour:(0,u.y)(t)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,u.y)(t)?"h12":"h23",timeZone:(0,r.f)(t.time_zone,e)}))),y=(t,e,n)=>v(e,n.time_zone).format(t),v=(0,o.Z)(((t,e)=>new Intl.DateTimeFormat("en-GB",{hour:"numeric",minute:"2-digit",hour12:!1,timeZone:(0,r.f)(t.time_zone,e)})));a()}catch(c){a(c)}}))},52258:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{G:function(){return c}});var i=n(16485),o=n(27486),r=n(66045),u=t([i,r]);[i,r]=u.then?(await u)():u;const l=(0,o.Z)((t=>new Intl.RelativeTimeFormat(t.language,{numeric:"auto"}))),c=(t,e,n,a=!0)=>{const i=(0,r.W)(t,n,e);return a?l(e).format(i.value,i.unit):Intl.NumberFormat(e.language,{style:"unit",unit:i.unit,unitDisplay:"long"}).format(Math.abs(i.value))};a()}catch(l){a(l)}}))},70691:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{f:function(){return d}});var i,o,r,u=n(16485),l=n(50177),c=t([u]);u=(c.then?(await c)():c)[0];const m=null===(i=Intl.DateTimeFormat)||void 0===i||null===(o=(r=i.call(Intl)).resolvedOptions)||void 0===o?void 0:o.call(r).timeZone,s=null!=m?m:"UTC",d=(t,e)=>t===l.c_.local&&m?s:e;a()}catch(m){a(m)}}))},81928:function(t,e,n){n.d(e,{f:function(){return a}});const a=t=>t.charAt(0).toUpperCase()+t.slice(1)},66045:function(t,e,n){n.a(t,(async function(t,a){try{n.d(e,{W:function(){return h}});n(19423);var i=n(13809),o=n(29558),r=n(57829),u=n(47899),l=t([u]);u=(l.then?(await l)():l)[0];const m=1e3,s=60,d=60*s;function h(t,e=Date.now(),n,a={}){const l=Object.assign(Object.assign({},f),a||{}),c=(+t-+e)/m;if(Math.abs(c)<l.second)return{value:Math.round(c),unit:"second"};const h=c/s;if(Math.abs(h)<l.minute)return{value:Math.round(h),unit:"minute"};const y=c/d;if(Math.abs(y)<l.hour)return{value:Math.round(y),unit:"hour"};const v=new Date(t),g=new Date(e);v.setHours(0,0,0,0),g.setHours(0,0,0,0);const _=(0,i.j)(v,g);if(0===_)return{value:Math.round(y),unit:"hour"};if(Math.abs(_)<l.day)return{value:_,unit:"day"};const k=(0,u.Bt)(n),w=(0,o.z)(v,{weekStartsOn:k}),Z=(0,o.z)(g,{weekStartsOn:k}),p=(0,r.p)(w,Z);if(0===p)return{value:_,unit:"day"};if(Math.abs(p)<l.week)return{value:p,unit:"week"};const I=v.getFullYear()-g.getFullYear(),z=12*I+v.getMonth()-g.getMonth();return 0===z?{value:p,unit:"week"}:Math.abs(z)<l.month||0===I?{value:z,unit:"month"}:{value:Math.round(I),unit:"year"}}const f={second:45,minute:45,hour:22,day:5,week:4,month:11};a()}catch(c){a(c)}}))},86438:function(t,e,n){n.d(e,{Ft:function(){return a}});n(40251);const a="timestamp"},36407:function(t,e,n){n.a(t,(async function(t,e){try{var a=n(73577),i=n(72621),o=(n(19083),n(71695),n(47021),n(57243)),r=n(50778),u=n(65417),l=n(69027),c=n(823),m=n(52258),s=n(81928),d=t([u,l,c,m]);[u,l,c,m]=d.then?(await d)():d;let h,f,y,v,g=t=>t;const _={date:u.p6,datetime:l.o0,time:c.mr},k=["relative","total"];(0,a.Z)([(0,r.Mo)("hui-timestamp-display")],(function(t,e){class n extends e{constructor(...e){super(...e),t(this)}}return{F:n,d:[{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"ts",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"format",value:void 0},{kind:"field",decorators:[(0,r.Cb)({type:Boolean})],key:"capitalize",value(){return!1}},{kind:"field",decorators:[(0,r.SB)()],key:"_relative",value:void 0},{kind:"field",key:"_connected",value:void 0},{kind:"field",key:"_interval",value:void 0},{kind:"method",key:"connectedCallback",value:function(){(0,i.Z)(n,"connectedCallback",this,3)([]),this._connected=!0,this._startInterval()}},{kind:"method",key:"disconnectedCallback",value:function(){(0,i.Z)(n,"disconnectedCallback",this,3)([]),this._connected=!1,this._clearInterval()}},{kind:"method",key:"render",value:function(){if(!this.ts||!this.hass)return o.Ld;if(isNaN(this.ts.getTime()))return(0,o.dy)(h||(h=g`${0}`),this.hass.localize("ui.panel.lovelace.components.timestamp-display.invalid"));const t=this._format;return k.includes(t)?(0,o.dy)(f||(f=g` ${0} `),this._relative):t in _?(0,o.dy)(y||(y=g` ${0} `),_[t](this.ts,this.hass.locale,this.hass.config)):(0,o.dy)(v||(v=g`${0}`),this.hass.localize("ui.panel.lovelace.components.timestamp-display.invalid_format"))}},{kind:"method",key:"updated",value:function(t){(0,i.Z)(n,"updated",this,3)([t]),t.has("format")&&this._connected&&(k.includes("relative")?this._startInterval():this._clearInterval())}},{kind:"get",key:"_format",value:function(){return this.format||"relative"}},{kind:"method",key:"_startInterval",value:function(){this._clearInterval(),this._connected&&k.includes(this._format)&&(this._updateRelative(),this._interval=window.setInterval((()=>this._updateRelative()),1e3))}},{kind:"method",key:"_clearInterval",value:function(){this._interval&&(clearInterval(this._interval),this._interval=void 0)}},{kind:"method",key:"_updateRelative",value:function(){var t;this.ts&&null!==(t=this.hass)&&void 0!==t&&t.localize&&(this._relative="relative"===this._format?(0,m.G)(this.ts,this.hass.locale):(0,m.G)(new Date,this.hass.locale,this.ts,!1),this._relative=this.capitalize?(0,s.f)(this._relative):this._relative)}}]}}),o.oi);e()}catch(h){e(h)}}))}}]);
//# sourceMappingURL=4284.4ef3b79ec7895330.js.map