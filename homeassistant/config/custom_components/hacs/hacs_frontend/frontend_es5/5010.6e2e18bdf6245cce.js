"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["5010"],{81036:function(e,t,i){i.d(t,{U:function(){return n}});const n=e=>e.stopPropagation()},74064:function(e,t,i){var n=i(73577),a=i(72621),d=(i(71695),i(47021),i(65703)),l=i(46289),s=i(57243),r=i(50778);let o,c,u,h=e=>e;(0,n.Z)([(0,r.Mo)("ha-list-item")],(function(e,t){class i extends t{constructor(...t){super(...t),e(this)}}return{F:i,d:[{kind:"method",key:"renderRipple",value:function(){return this.noninteractive?"":(0,a.Z)(i,"renderRipple",this,3)([])}},{kind:"get",static:!0,key:"styles",value:function(){return[l.W,(0,s.iv)(o||(o=h`:host{padding-left:var(--mdc-list-side-padding-left,var(--mdc-list-side-padding,20px));padding-inline-start:var(--mdc-list-side-padding-left,var(--mdc-list-side-padding,20px));padding-right:var(--mdc-list-side-padding-right,var(--mdc-list-side-padding,20px));padding-inline-end:var(--mdc-list-side-padding-right,var(--mdc-list-side-padding,20px))}:host([graphic=avatar]:not([twoLine])),:host([graphic=icon]:not([twoLine])){height:48px}span.material-icons:first-of-type{margin-inline-start:0px!important;margin-inline-end:var(--mdc-list-item-graphic-margin,16px)!important;direction:var(--direction)!important}span.material-icons:last-of-type{margin-inline-start:auto!important;margin-inline-end:0px!important;direction:var(--direction)!important}.mdc-deprecated-list-item__meta{display:var(--mdc-list-item-meta-display);align-items:center;flex-shrink:0}:host([graphic=icon]:not([twoline])) .mdc-deprecated-list-item__graphic{margin-inline-end:var(--mdc-list-item-graphic-margin,20px)!important}:host([multiline-secondary]){height:auto}:host([multiline-secondary]) .mdc-deprecated-list-item__text{padding:8px 0}:host([multiline-secondary]) .mdc-deprecated-list-item__secondary-text{text-overflow:initial;white-space:normal;overflow:auto;display:inline-block;margin-top:10px}:host([multiline-secondary]) .mdc-deprecated-list-item__primary-text{margin-top:10px}:host([multiline-secondary]) .mdc-deprecated-list-item__secondary-text::before{display:none}:host([multiline-secondary]) .mdc-deprecated-list-item__primary-text::before{display:none}:host([disabled]){color:var(--disabled-text-color)}:host([noninteractive]){pointer-events:unset}`)),"rtl"===document.dir?(0,s.iv)(c||(c=h`span.material-icons:first-of-type,span.material-icons:last-of-type{direction:rtl!important;--direction:rtl}`)):(0,s.iv)(u||(u=h``))]}}]}}),d.K)},58130:function(e,t,i){var n=i(73577),a=i(72621),d=(i(71695),i(40251),i(47021),i(60930)),l=i(9714),s=i(57243),r=i(50778),o=i(56587),c=i(30137);i(59897);let u,h,p,v,g=e=>e;(0,n.Z)([(0,r.Mo)("ha-select")],(function(e,t){class i extends t{constructor(...t){super(...t),e(this)}}return{F:i,d:[{kind:"field",decorators:[(0,r.Cb)({type:Boolean})],key:"icon",value(){return!1}},{kind:"field",decorators:[(0,r.Cb)({type:Boolean,reflect:!0})],key:"clearable",value(){return!1}},{kind:"field",decorators:[(0,r.Cb)({attribute:"inline-arrow",type:Boolean})],key:"inlineArrow",value(){return!1}},{kind:"method",key:"render",value:function(){return(0,s.dy)(u||(u=g` ${0} ${0} `),(0,a.Z)(i,"render",this,3)([]),this.clearable&&!this.required&&!this.disabled&&this.value?(0,s.dy)(h||(h=g`<ha-icon-button label="clear" @click="${0}" .path="${0}"></ha-icon-button>`),this._clearValue,"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"):s.Ld)}},{kind:"method",key:"renderLeadingIcon",value:function(){return this.icon?(0,s.dy)(p||(p=g`<span class="mdc-select__icon"><slot name="icon"></slot></span>`)):s.Ld}},{kind:"method",key:"connectedCallback",value:function(){(0,a.Z)(i,"connectedCallback",this,3)([]),window.addEventListener("translations-updated",this._translationsUpdated)}},{kind:"method",key:"firstUpdated",value:async function(){var e;((0,a.Z)(i,"firstUpdated",this,3)([]),this.inlineArrow)&&(null===(e=this.shadowRoot)||void 0===e||null===(e=e.querySelector(".mdc-select__selected-text-container"))||void 0===e||e.classList.add("inline-arrow"))}},{kind:"method",key:"updated",value:function(e){if((0,a.Z)(i,"updated",this,3)([e]),e.has("inlineArrow")){var t;const e=null===(t=this.shadowRoot)||void 0===t?void 0:t.querySelector(".mdc-select__selected-text-container");this.inlineArrow?null==e||e.classList.add("inline-arrow"):null==e||e.classList.remove("inline-arrow")}}},{kind:"method",key:"disconnectedCallback",value:function(){(0,a.Z)(i,"disconnectedCallback",this,3)([]),window.removeEventListener("translations-updated",this._translationsUpdated)}},{kind:"method",key:"_clearValue",value:function(){!this.disabled&&this.value&&(this.valueSetDirectly=!0,this.select(-1),this.mdcFoundation.handleChange())}},{kind:"field",key:"_translationsUpdated",value(){return(0,o.D)((async()=>{await(0,c.y)(),this.layoutOptions()}),500)}},{kind:"field",static:!0,key:"styles",value(){return[l.W,(0,s.iv)(v||(v=g`:host([clearable]){position:relative}.mdc-select:not(.mdc-select--disabled) .mdc-select__icon{color:var(--secondary-text-color)}.mdc-select__anchor{width:var(--ha-select-min-width,200px)}.mdc-select--filled .mdc-select__anchor{height:var(--ha-select-height,56px)}.mdc-select--filled .mdc-floating-label{inset-inline-start:12px;inset-inline-end:initial;direction:var(--direction)}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label{inset-inline-start:48px;inset-inline-end:initial;direction:var(--direction)}.mdc-select .mdc-select__anchor{padding-inline-start:12px;padding-inline-end:0px;direction:var(--direction)}.mdc-select__anchor .mdc-floating-label--float-above{transform-origin:var(--float-start)}.mdc-select__selected-text-container{padding-inline-end:var(--select-selected-text-padding-end,0px)}:host([clearable]) .mdc-select__selected-text-container{padding-inline-end:var(--select-selected-text-padding-end,12px)}ha-icon-button{position:absolute;top:10px;right:28px;--mdc-icon-button-size:36px;--mdc-icon-size:20px;color:var(--secondary-text-color);inset-inline-start:initial;inset-inline-end:28px;direction:var(--direction)}.inline-arrow{flex-grow:0}`))]}}]}}),d.K)},26674:function(e,t,i){i.r(t),i.d(t,{HaSTTSelector:()=>x});var n=i("73577"),a=(i("71695"),i("47021"),i("57243")),d=i("50778"),l=i("72621"),s=(i("19083"),i("40251"),i("61006"),i("39527"),i("67670"),i("13334"),i("11297")),r=i("81036"),o=i("73525"),c=i("56587"),u=i("52829"),h=(i("74064"),i("58130"),i("79575"));let p,v,g,m,f=e=>e;const k="__NONE_OPTION__";(0,n.Z)([(0,d.Mo)("ha-stt-picker")],(function(e,t){class i extends t{constructor(...t){super(...t),e(this)}}return{F:i,d:[{kind:"field",decorators:[(0,d.Cb)()],key:"value",value:void 0},{kind:"field",decorators:[(0,d.Cb)()],key:"label",value:void 0},{kind:"field",decorators:[(0,d.Cb)()],key:"language",value:void 0},{kind:"field",decorators:[(0,d.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,d.Cb)({type:Boolean,reflect:!0})],key:"disabled",value(){return!1}},{kind:"field",decorators:[(0,d.Cb)({type:Boolean})],key:"required",value(){return!1}},{kind:"field",decorators:[(0,d.SB)()],key:"_engines",value:void 0},{kind:"method",key:"render",value:function(){if(!this._engines)return a.Ld;let e=this.value;if(!e&&this.required){for(const t of Object.values(this.hass.entities))if("cloud"===t.platform&&"stt"===(0,h.M)(t.entity_id)){e=t.entity_id;break}if(!e)for(const i of this._engines){var t;if(0!==(null==i||null===(t=i.supported_languages)||void 0===t?void 0:t.length)){e=i.engine_id;break}}}return e||(e=k),(0,a.dy)(p||(p=f` <ha-select .label="${0}" .value="${0}" .required="${0}" .disabled="${0}" @selected="${0}" @closed="${0}" fixedMenuPosition naturalMenuWidth> ${0} ${0} </ha-select> `),this.label||this.hass.localize("ui.components.stt-picker.stt"),e,this.required,this.disabled,this._changed,r.U,this.required?a.Ld:(0,a.dy)(v||(v=f`<ha-list-item .value="${0}"> ${0} </ha-list-item>`),k,this.hass.localize("ui.components.stt-picker.none")),this._engines.map((t=>{var i;if(t.deprecated&&t.engine_id!==e)return a.Ld;let n;if(t.engine_id.includes(".")){const e=this.hass.states[t.engine_id];n=e?(0,o.C)(e):t.engine_id}else n=t.name||t.engine_id;return(0,a.dy)(g||(g=f`<ha-list-item .value="${0}" .disabled="${0}"> ${0} </ha-list-item>`),t.engine_id,0===(null===(i=t.supported_languages)||void 0===i?void 0:i.length),n)})))}},{kind:"method",key:"willUpdate",value:function(e){(0,l.Z)(i,"willUpdate",this,3)([e]),this.hasUpdated?e.has("language")&&this._debouncedUpdateEngines():this._updateEngines()}},{kind:"field",key:"_debouncedUpdateEngines",value(){return(0,c.D)((()=>this._updateEngines()),500)}},{kind:"method",key:"_updateEngines",value:async function(){var e;if(this._engines=(await(0,u.m)(this.hass,this.language,this.hass.config.country||void 0)).providers,!this.value)return;const t=this._engines.find((e=>e.engine_id===this.value));(0,s.B)(this,"supported-languages-changed",{value:null==t?void 0:t.supported_languages}),t&&0!==(null===(e=t.supported_languages)||void 0===e?void 0:e.length)||(this.value=void 0,(0,s.B)(this,"value-changed",{value:this.value}))}},{kind:"get",static:!0,key:"styles",value:function(){return(0,a.iv)(m||(m=f`ha-select{width:100%}`))}},{kind:"method",key:"_changed",value:function(e){var t;const i=e.target;!this.hass||""===i.value||i.value===this.value||void 0===this.value&&i.value===k||(this.value=i.value===k?void 0:i.value,(0,s.B)(this,"value-changed",{value:this.value}),(0,s.B)(this,"supported-languages-changed",{value:null===(t=this._engines.find((e=>e.engine_id===this.value)))||void 0===t?void 0:t.supported_languages}))}}]}}),a.oi);let y,_,b=e=>e,x=(0,n.Z)([(0,d.Mo)("ha-selector-stt")],(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"field",decorators:[(0,d.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,d.Cb)({attribute:!1})],key:"selector",value:void 0},{kind:"field",decorators:[(0,d.Cb)()],key:"value",value:void 0},{kind:"field",decorators:[(0,d.Cb)()],key:"label",value:void 0},{kind:"field",decorators:[(0,d.Cb)()],key:"helper",value:void 0},{kind:"field",decorators:[(0,d.Cb)({type:Boolean})],key:"disabled",value(){return!1}},{kind:"field",decorators:[(0,d.Cb)({type:Boolean})],key:"required",value(){return!0}},{kind:"field",decorators:[(0,d.Cb)({attribute:!1})],key:"context",value:void 0},{kind:"method",key:"render",value:function(){var e,t;return(0,a.dy)(y||(y=b`<ha-stt-picker .hass="${0}" .value="${0}" .label="${0}" .helper="${0}" .language="${0}" .disabled="${0}" .required="${0}"></ha-stt-picker>`),this.hass,this.value,this.label,this.helper,(null===(e=this.selector.stt)||void 0===e?void 0:e.language)||(null===(t=this.context)||void 0===t?void 0:t.language),this.disabled,this.required)}},{kind:"field",static:!0,key:"styles",value(){return(0,a.iv)(_||(_=b`ha-stt-picker{width:100%}`))}}]}}),a.oi)},52829:function(e,t,i){i.d(t,{m:function(){return n}});const n=(e,t,i)=>e.callWS({type:"stt/engine/list",language:t,country:i})}}]);
//# sourceMappingURL=5010.6e2e18bdf6245cce.js.map