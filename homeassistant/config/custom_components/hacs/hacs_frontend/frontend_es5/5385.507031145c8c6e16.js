/*! For license information please see 5385.507031145c8c6e16.js.LICENSE.txt */
"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["5385"],{78611:function(e,t,i){i.d(t,{P:function(){return o}});i(71695),i(39527),i(41360),i(47021);const o=e=>(t,i)=>{if(t.constructor._observers){if(!t.constructor.hasOwnProperty("_observers")){const e=t.constructor._observers;t.constructor._observers=new Map,e.forEach(((e,i)=>t.constructor._observers.set(i,e)))}}else{t.constructor._observers=new Map;const e=t.updated;t.updated=function(t){e.call(this,t),t.forEach(((e,t)=>{const i=this.constructor._observers.get(t);void 0!==i&&i.call(this,this[t],e)}))}}t.constructor._observers.set(i,e)}},67840:function(e,t,i){i.d(t,{A:()=>b});var o=i("9065"),n=i("50778"),s=(i("19083"),i("71695"),i("92745"),i("40251"),i("61006"),i("47021"),i("1231"),i("57243")),a=i("35359"),r=i("13823"),l=i("79679"),d=i("88854");let c,h,p=e=>e;const u=(0,r.T)(s.oi);class g extends u{get open(){return this.isOpen}set open(e){e!==this.isOpen&&(this.isOpen=e,e?(this.setAttribute("open",""),this.show()):(this.removeAttribute("open"),this.close()))}constructor(){super(),this.quick=!1,this.returnValue="",this.noFocusTrap=!1,this.getOpenAnimation=()=>d.I,this.getCloseAnimation=()=>d.G,this.isOpen=!1,this.isOpening=!1,this.isConnectedPromise=this.getIsConnectedPromise(),this.isAtScrollTop=!1,this.isAtScrollBottom=!1,this.nextClickIsFromContent=!1,this.hasHeadline=!1,this.hasActions=!1,this.hasIcon=!1,this.escapePressedWithoutCancel=!1,this.treewalker=s.sk?null:document.createTreeWalker(this,NodeFilter.SHOW_ELEMENT),s.sk||this.addEventListener("submit",this.handleSubmit)}async show(){var e;this.isOpening=!0,await this.isConnectedPromise,await this.updateComplete;const t=this.dialog;if(t.open||!this.isOpening)return void(this.isOpening=!1);if(!this.dispatchEvent(new Event("open",{cancelable:!0})))return this.open=!1,void(this.isOpening=!1);t.showModal(),this.open=!0,this.scroller&&(this.scroller.scrollTop=0),null===(e=this.querySelector("[autofocus]"))||void 0===e||e.focus(),await this.animateDialog(this.getOpenAnimation()),this.dispatchEvent(new Event("opened")),this.isOpening=!1}async close(e=this.returnValue){if(this.isOpening=!1,!this.isConnected)return void(this.open=!1);await this.updateComplete;const t=this.dialog;if(!t.open||this.isOpening)return void(this.open=!1);const i=this.returnValue;this.returnValue=e;this.dispatchEvent(new Event("close",{cancelable:!0}))?(await this.animateDialog(this.getCloseAnimation()),t.close(e),this.open=!1,this.dispatchEvent(new Event("closed"))):this.returnValue=i}connectedCallback(){super.connectedCallback(),this.isConnectedPromiseResolve()}disconnectedCallback(){super.disconnectedCallback(),this.isConnectedPromise=this.getIsConnectedPromise()}render(){const e=this.open&&!(this.isAtScrollTop&&this.isAtScrollBottom),t={"has-headline":this.hasHeadline,"has-actions":this.hasActions,"has-icon":this.hasIcon,scrollable:e,"show-top-divider":e&&!this.isAtScrollTop,"show-bottom-divider":e&&!this.isAtScrollBottom},i=this.open&&!this.noFocusTrap,o=(0,s.dy)(c||(c=p` <div class="focus-trap" tabindex="0" aria-hidden="true" @focus="${0}"></div> `),this.handleFocusTrapFocus),{ariaLabel:n}=this;return(0,s.dy)(h||(h=p` <div class="scrim"></div> <dialog class="${0}" aria-label="${0}" aria-labelledby="${0}" role="${0}" @cancel="${0}" @click="${0}" @close="${0}" @keydown="${0}" .returnValue="${0}"> ${0} <div class="container" @click="${0}"> <div class="headline"> <div class="icon" aria-hidden="true"> <slot name="icon" @slotchange="${0}"></slot> </div> <h2 id="headline" aria-hidden="${0}"> <slot name="headline" @slotchange="${0}"></slot> </h2> <md-divider></md-divider> </div> <div class="scroller"> <div class="content"> <div class="top anchor"></div> <slot name="content"></slot> <div class="bottom anchor"></div> </div> </div> <div class="actions"> <md-divider></md-divider> <slot name="actions" @slotchange="${0}"></slot> </div> </div> ${0} </dialog> `),(0,a.$)(t),n||s.Ld,this.hasHeadline?"headline":s.Ld,"alert"===this.type?"alertdialog":s.Ld,this.handleCancel,this.handleDialogClick,this.handleClose,this.handleKeydown,this.returnValue||s.Ld,i?o:s.Ld,this.handleContentClick,this.handleIconChange,!this.hasHeadline||s.Ld,this.handleHeadlineChange,this.handleActionsChange,i?o:s.Ld)}firstUpdated(){this.intersectionObserver=new IntersectionObserver((e=>{for(const t of e)this.handleAnchorIntersection(t)}),{root:this.scroller}),this.intersectionObserver.observe(this.topAnchor),this.intersectionObserver.observe(this.bottomAnchor)}handleDialogClick(){if(this.nextClickIsFromContent)return void(this.nextClickIsFromContent=!1);!this.dispatchEvent(new Event("cancel",{cancelable:!0}))||this.close()}handleContentClick(){this.nextClickIsFromContent=!0}handleSubmit(e){var t;const i=e.target,{submitter:o}=e;"dialog"===i.method&&o&&this.close(null!==(t=o.getAttribute("value"))&&void 0!==t?t:this.returnValue)}handleCancel(e){if(e.target!==this.dialog)return;this.escapePressedWithoutCancel=!1;const t=!(0,l.e)(this,e);e.preventDefault(),t||this.close()}handleClose(){var e;this.escapePressedWithoutCancel&&(this.escapePressedWithoutCancel=!1,null===(e=this.dialog)||void 0===e||e.dispatchEvent(new Event("cancel",{cancelable:!0})))}handleKeydown(e){"Escape"===e.key&&(this.escapePressedWithoutCancel=!0,setTimeout((()=>{this.escapePressedWithoutCancel=!1})))}async animateDialog(e){var t;if(null===(t=this.cancelAnimations)||void 0===t||t.abort(),this.cancelAnimations=new AbortController,this.quick)return;const{dialog:i,scrim:o,container:n,headline:s,content:a,actions:r}=this;if(!(i&&o&&n&&s&&a&&r))return;const{container:l,dialog:d,scrim:c,headline:h,content:p,actions:u}=e,g=[[i,null!=d?d:[]],[o,null!=c?c:[]],[n,null!=l?l:[]],[s,null!=h?h:[]],[a,null!=p?p:[]],[r,null!=u?u:[]]],m=[];for(const[v,f]of g)for(const e of f){const t=v.animate(...e);this.cancelAnimations.signal.addEventListener("abort",(()=>{t.cancel()})),m.push(t)}await Promise.all(m.map((e=>e.finished.catch((()=>{})))))}handleHeadlineChange(e){const t=e.target;this.hasHeadline=t.assignedElements().length>0}handleActionsChange(e){const t=e.target;this.hasActions=t.assignedElements().length>0}handleIconChange(e){const t=e.target;this.hasIcon=t.assignedElements().length>0}handleAnchorIntersection(e){const{target:t,isIntersecting:i}=e;t===this.topAnchor&&(this.isAtScrollTop=i),t===this.bottomAnchor&&(this.isAtScrollBottom=i)}getIsConnectedPromise(){return new Promise((e=>{this.isConnectedPromiseResolve=e}))}handleFocusTrapFocus(e){const[t,i]=this.getFirstAndLastFocusableChildren();var o;if(!t||!i)return void(null===(o=this.dialog)||void 0===o||o.focus());const n=e.target===this.firstFocusTrap,s=!n,a=e.relatedTarget===t,r=e.relatedTarget===i,l=!a&&!r;if(s&&r||n&&l)return void t.focus();(n&&a||s&&l)&&i.focus()}getFirstAndLastFocusableChildren(){if(!this.treewalker)return[null,null];let e=null,t=null;for(this.treewalker.currentNode=this.treewalker.root;this.treewalker.nextNode();){const i=this.treewalker.currentNode;m(i)&&(e||(e=i),t=i)}return[e,t]}}function m(e){var t,i;const o=":not(:disabled,[disabled])";if(e.matches(":is(button,input,select,textarea,object,:is(a,area)[href],[tabindex],[contenteditable=true])"+o+':not([tabindex^="-"])'))return!0;return!!e.localName.includes("-")&&(!!e.matches(o)&&(null!==(t=null===(i=e.shadowRoot)||void 0===i?void 0:i.delegatesFocus)&&void 0!==t&&t))}(0,o.gn)([(0,n.Cb)({type:Boolean})],g.prototype,"open",null),(0,o.gn)([(0,n.Cb)({type:Boolean})],g.prototype,"quick",void 0),(0,o.gn)([(0,n.Cb)({attribute:!1})],g.prototype,"returnValue",void 0),(0,o.gn)([(0,n.Cb)()],g.prototype,"type",void 0),(0,o.gn)([(0,n.Cb)({type:Boolean,attribute:"no-focus-trap"})],g.prototype,"noFocusTrap",void 0),(0,o.gn)([(0,n.IO)("dialog")],g.prototype,"dialog",void 0),(0,o.gn)([(0,n.IO)(".scrim")],g.prototype,"scrim",void 0),(0,o.gn)([(0,n.IO)(".container")],g.prototype,"container",void 0),(0,o.gn)([(0,n.IO)(".headline")],g.prototype,"headline",void 0),(0,o.gn)([(0,n.IO)(".content")],g.prototype,"content",void 0),(0,o.gn)([(0,n.IO)(".actions")],g.prototype,"actions",void 0),(0,o.gn)([(0,n.SB)()],g.prototype,"isAtScrollTop",void 0),(0,o.gn)([(0,n.SB)()],g.prototype,"isAtScrollBottom",void 0),(0,o.gn)([(0,n.IO)(".scroller")],g.prototype,"scroller",void 0),(0,o.gn)([(0,n.IO)(".top.anchor")],g.prototype,"topAnchor",void 0),(0,o.gn)([(0,n.IO)(".bottom.anchor")],g.prototype,"bottomAnchor",void 0),(0,o.gn)([(0,n.IO)(".focus-trap")],g.prototype,"firstFocusTrap",void 0),(0,o.gn)([(0,n.SB)()],g.prototype,"hasHeadline",void 0),(0,o.gn)([(0,n.SB)()],g.prototype,"hasActions",void 0),(0,o.gn)([(0,n.SB)()],g.prototype,"hasIcon",void 0);let v;const f=(0,s.iv)(v||(v=(e=>e)`:host{border-start-start-radius:var(--md-dialog-container-shape-start-start,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-start-end-radius:var(--md-dialog-container-shape-start-end,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-end-end-radius:var(--md-dialog-container-shape-end-end,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-end-start-radius:var(--md-dialog-container-shape-end-start,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));display:contents;margin:auto;max-height:min(560px,100% - 48px);max-width:min(560px,100% - 48px);min-height:140px;min-width:280px;position:fixed;height:fit-content;width:fit-content}dialog{background:rgba(0,0,0,0);border:none;border-radius:inherit;flex-direction:column;height:inherit;margin:inherit;max-height:inherit;max-width:inherit;min-height:inherit;min-width:inherit;outline:0;overflow:visible;padding:0;width:inherit}dialog[open]{display:flex}::backdrop{background:0 0}.scrim{background:var(--md-sys-color-scrim,#000);display:none;inset:0;opacity:32%;pointer-events:none;position:fixed;z-index:1}:host([open]) .scrim{display:flex}h2{all:unset;align-self:stretch}.headline{align-items:center;color:var(--md-dialog-headline-color,var(--md-sys-color-on-surface,#1d1b20));display:flex;flex-direction:column;font-family:var(--md-dialog-headline-font, var(--md-sys-typescale-headline-small-font, var(--md-ref-typeface-brand, Roboto)));font-size:var(--md-dialog-headline-size, var(--md-sys-typescale-headline-small-size, 1.5rem));line-height:var(--md-dialog-headline-line-height, var(--md-sys-typescale-headline-small-line-height, 2rem));font-weight:var(--md-dialog-headline-weight,var(--md-sys-typescale-headline-small-weight,var(--md-ref-typeface-weight-regular,400)));position:relative}slot[name=headline]::slotted(*){align-items:center;align-self:stretch;box-sizing:border-box;display:flex;gap:8px;padding:24px 24px 0}.icon{display:flex}slot[name=icon]::slotted(*){color:var(--md-dialog-icon-color,var(--md-sys-color-secondary,#625b71));fill:currentColor;font-size:var(--md-dialog-icon-size, 24px);margin-top:24px;height:var(--md-dialog-icon-size,24px);width:var(--md-dialog-icon-size,24px)}.has-icon slot[name=headline]::slotted(*){justify-content:center;padding-top:16px}.scrollable slot[name=headline]::slotted(*){padding-bottom:16px}.scrollable.has-headline slot[name=content]::slotted(*){padding-top:8px}.container{border-radius:inherit;display:flex;flex-direction:column;flex-grow:1;overflow:hidden;position:relative;transform-origin:top}.container::before{background:var(--md-dialog-container-color,var(--md-sys-color-surface-container-high,#ece6f0));border-radius:inherit;content:"";inset:0;position:absolute}.scroller{display:flex;flex:1;flex-direction:column;overflow:hidden;z-index:1}.scrollable .scroller{overflow-y:scroll}.content{color:var(--md-dialog-supporting-text-color,var(--md-sys-color-on-surface-variant,#49454f));font-family:var(--md-dialog-supporting-text-font, var(--md-sys-typescale-body-medium-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-dialog-supporting-text-size, var(--md-sys-typescale-body-medium-size, .875rem));line-height:var(--md-dialog-supporting-text-line-height, var(--md-sys-typescale-body-medium-line-height, 1.25rem));flex:1;font-weight:var(--md-dialog-supporting-text-weight,var(--md-sys-typescale-body-medium-weight,var(--md-ref-typeface-weight-regular,400)));height:min-content;position:relative}slot[name=content]::slotted(*){box-sizing:border-box;padding:24px}.anchor{position:absolute}.top.anchor{top:0}.bottom.anchor{bottom:0}.actions{position:relative}slot[name=actions]::slotted(*){box-sizing:border-box;display:flex;gap:8px;justify-content:flex-end;padding:16px 24px 24px}.has-actions slot[name=content]::slotted(*){padding-bottom:8px}md-divider{display:none;position:absolute}.has-actions.show-bottom-divider .actions md-divider,.has-headline.show-top-divider .headline md-divider{display:flex}.headline md-divider{bottom:0}.actions md-divider{top:0}@media(forced-colors:active){dialog{outline:2px solid WindowText}}`));let b=class extends g{};b.styles=[f],b=(0,o.gn)([(0,n.Mo)("md-dialog")],b)},88854:function(e,t,i){i.d(t,{G:function(){return s},I:function(){return n}});var o=i(43921);const n={dialog:[[[{transform:"translateY(-50px)"},{transform:"translateY(0)"}],{duration:500,easing:o.Xt.EMPHASIZED}]],scrim:[[[{opacity:0},{opacity:.32}],{duration:500,easing:"linear"}]],container:[[[{opacity:0},{opacity:1}],{duration:50,easing:"linear",pseudoElement:"::before"}],[[{height:"35%"},{height:"100%"}],{duration:500,easing:o.Xt.EMPHASIZED,pseudoElement:"::before"}]],headline:[[[{opacity:0},{opacity:0,offset:.2},{opacity:1}],{duration:250,easing:"linear",fill:"forwards"}]],content:[[[{opacity:0},{opacity:0,offset:.2},{opacity:1}],{duration:250,easing:"linear",fill:"forwards"}]],actions:[[[{opacity:0},{opacity:0,offset:.5},{opacity:1}],{duration:300,easing:"linear",fill:"forwards"}]]},s={dialog:[[[{transform:"translateY(0)"},{transform:"translateY(-50px)"}],{duration:150,easing:o.Xt.EMPHASIZED_ACCELERATE}]],scrim:[[[{opacity:.32},{opacity:0}],{duration:150,easing:"linear"}]],container:[[[{height:"100%"},{height:"35%"}],{duration:150,easing:o.Xt.EMPHASIZED_ACCELERATE,pseudoElement:"::before"}],[[{opacity:"1"},{opacity:"0"}],{delay:100,duration:50,easing:"linear",pseudoElement:"::before"}]],headline:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]],content:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]],actions:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]]}},1231:function(e,t,i){i.d(t,{B:()=>d});var o=i("9065"),n=i("50778"),s=(i("71695"),i("47021"),i("57243"));class a extends s.oi{constructor(){super(...arguments),this.inset=!1,this.insetStart=!1,this.insetEnd=!1}}(0,o.gn)([(0,n.Cb)({type:Boolean,reflect:!0})],a.prototype,"inset",void 0),(0,o.gn)([(0,n.Cb)({type:Boolean,reflect:!0,attribute:"inset-start"})],a.prototype,"insetStart",void 0),(0,o.gn)([(0,n.Cb)({type:Boolean,reflect:!0,attribute:"inset-end"})],a.prototype,"insetEnd",void 0);let r;const l=(0,s.iv)(r||(r=(e=>e)`:host{box-sizing:border-box;color:var(--md-divider-color,var(--md-sys-color-outline-variant,#cac4d0));display:flex;height:var(--md-divider-thickness,1px);width:100%}:host([inset-start]),:host([inset]){padding-inline-start:16px}:host([inset-end]),:host([inset]){padding-inline-end:16px}:host::before{background:currentColor;content:"";height:100%;width:100%}@media(forced-colors:active){:host::before{background:CanvasText}}`));let d=class extends a{};d.styles=[l],d=(0,o.gn)([(0,n.Mo)("md-divider")],d)},79679:function(e,t,i){function o(e,t){!t.bubbles||e.shadowRoot&&!t.composed||t.stopPropagation();const i=Reflect.construct(t.constructor,[t.type,t]),o=e.dispatchEvent(i);return o||t.preventDefault(),o}i.d(t,{e:function(){return o}})},92903:function(e,t,i){i.d(t,{XM:function(){return o.XM},Xe:function(){return o.Xe},pX:function(){return o.pX}});var o=i(45779)}}]);
//# sourceMappingURL=5385.507031145c8c6e16.js.map