"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["5536"],{20378:function(e,t,n){n.d(t,{jz:()=>N});n("63721"),n("71695"),n("92745"),n("61495"),n("19134"),n("5740"),n("97003"),n("39527"),n("41360"),n("13334"),n("47021");var r,i,o,s=n("65054"),a=(n("92181"),n("99790"),n("36993"),[]),l=[],c=s.YM.__b,d=s.YM.__r,u=s.YM.diffed,h=s.YM.__c,f=s.YM.unmount;function g(){for(var e;e=a.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(v),e.__H.__h.forEach(y),e.__H.__h=[]}catch(t){e.__H.__h=[],s.YM.__e(t,e.__v)}}s.YM.__b=function(e){r=null,c&&c(e)},s.YM.__r=function(e){d&&d(e);var t=(r=e.__c).__H;t&&(i===r?(t.__h=[],r.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=l,e.__N=e.i=void 0}))):(t.__h.forEach(v),t.__h.forEach(y),t.__h=[])),i=r},s.YM.diffed=function(e){u&&u(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==a.push(t)&&o===s.YM.requestAnimationFrame||((o=s.YM.requestAnimationFrame)||m)(g)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==l&&(e.__=e.__V),e.i=void 0,e.__V=l}))),i=r=null},s.YM.__c=function(e,t){t.some((function(e){try{e.__h.forEach(v),e.__h=e.__h.filter((function(e){return!e.__||y(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],s.YM.__e(n,e.__v)}})),h&&h(e,t)},s.YM.unmount=function(e){f&&f(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{v(e)}catch(e){t=e}})),n.__H=void 0,t&&s.YM.__e(t,n.__v))};var p="function"==typeof requestAnimationFrame;function m(e){var t,n=function(){clearTimeout(r),p&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);p&&(t=requestAnimationFrame(n))}function v(e){var t=r,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),r=t}function y(e){var t=r;e.__c=e.__(),r=t}function b(e,t){for(var n in t)e[n]=t[n];return e}function w(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function E(e){this.props=e}(E.prototype=new s.wA).isPureReactComponent=!0,E.prototype.shouldComponentUpdate=function(e,t){return w(this.props,e)||w(this.state,t)};var A=s.YM.__b;s.YM.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),A&&A(e)};"undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref");var x=s.YM.__e;s.YM.__e=function(e,t,n,r){if(e.then)for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);x(e,t,n,r)};var D=s.YM.unmount;function T(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=b({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return T(e,t,n)}))),e}function S(e,t,n){return e&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return S(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.insertBefore(e.__e,e.__d),e.__c.__e=!0,e.__c.__P=n)),e}function k(){this.__u=0,this.t=null,this.__b=null}function C(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function _(){this.u=null,this.o=null}s.YM.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),D&&D(e)},(k.prototype=new s.wA).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var i=C(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=s;var a=function(){if(! --r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=S(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}},l=!0===t.__h;r.__u++||l||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(s,s)},k.prototype.componentWillUnmount=function(){this.t=[]},k.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=T(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__a&&(0,s.az)(s.HY,null,e.fallback);return i&&(i.__h=null),[(0,s.az)(s.HY,null,t.__a?null:e.children),i]};var R=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function M(e){return this.getChildContext=function(){return e.context},e.children}function I(e){var t=this,n=e.i;t.componentWillUnmount=function(){(0,s.sY)(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),(0,s.sY)((0,s.az)(M,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}function N(e,t){var n=(0,s.az)(I,{__v:e,i:t});return n.containerInfo=t,n}(_.prototype=new s.wA).__a=function(e){var t=this,n=C(t.__v),r=t.o.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),R(t,e,r)):i()};n?n(o):o()}},_.prototype.render=function(e){this.u=null,this.o=new Map;var t=(0,s.bR)(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},_.prototype.componentDidUpdate=_.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){R(e,n,t)}))};var O="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,z=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,H="undefined"!=typeof document,P=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(e)};s.wA.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(s.wA.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var L=s.YM.event;function B(){}function j(){return this.cancelBubble}function U(){return this.defaultPrevented}s.YM.event=function(e){return L&&(e=L(e)),e.persist=B,e.isPropagationStopped=j,e.isDefaultPrevented=U,e.nativeEvent=e};var W={configurable:!0,get:function(){return this.class}},V=s.YM.vnode;s.YM.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){var i=-1===t.indexOf("-");for(var o in r={},n){var a=n[o];H&&"children"===o&&"noscript"===t||"value"===o&&"defaultValue"in n&&null==a||("defaultValue"===o&&"value"in n&&null==n.value?o="value":"download"===o&&!0===a?a="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&!P(n.type)?o="oninput":/^onfocus$/i.test(o)?o="onfocusin":/^onblur$/i.test(o)?o="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(o)?o=o.toLowerCase():i&&z.test(o)?o=o.replace(/[A-Z0-9]/g,"-$&").toLowerCase():null===a&&(a=void 0),/^oninput$/i.test(o)&&(o=o.toLowerCase(),r[o]&&(o="oninputCapture")),r[o]=a)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=(0,s.bR)(n.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==t&&null!=r.defaultValue&&(r.value=(0,s.bR)(n.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),e.props=r,n.class!=n.className&&(W.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",W))}e.$$typeof=O,V&&V(e)};var F=s.YM.__r;s.YM.__r=function(e){F&&F(e),e.__c}},65054:function(e,t,n){n.d(t,{HY:function(){return w},Vf:function(){return b},YM:function(){return i},az:function(){return v},bR:function(){return N},kr:function(){return V},l$:function(){return s},sY:function(){return W},wA:function(){return T}});n(92745),n(61893),n(40251),n(19134),n(5740),n(97003),n(39527),n(41360),n(36993);var r,i,o,s,a,l,c,d,u,h={},f=[],g=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function p(e,t){for(var n in t)e[n]=t[n];return e}function m(e){var t=e.parentNode;t&&t.removeChild(e)}function v(e,t,n){var i,o,s,a={};for(s in t)"key"==s?i=t[s]:"ref"==s?o=t[s]:a[s]=t[s];if(arguments.length>2&&(a.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return y(e,a,i,o,null)}function y(e,t,n,r,s){var a={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==s?++o:s};return null==s&&null!=i.vnode&&i.vnode(a),a}function b(){return{current:null}}function w(e){return e.children}function E(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||g.test(t)?n:n+"px"}function A(e,t,n,r,i){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||E(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||E(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r||e.addEventListener(t,o?D:x,o):e.removeEventListener(t,o?D:x,o);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,n))}}function x(e){a=!0;try{return this.l[e.type+!1](i.event?i.event(e):e)}finally{a=!1}}function D(e){a=!0;try{return this.l[e.type+!0](i.event?i.event(e):e)}finally{a=!1}}function T(e,t){this.props=e,this.context=t}function S(e,t){if(null==t)return e.__?S(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?S(e):null}function k(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return k(e)}}function C(e){a?setTimeout(e):d(e)}function _(e){(!e.__d&&(e.__d=!0)&&l.push(e)&&!R.__r++||c!==i.debounceRendering)&&((c=i.debounceRendering)||C)(R)}function R(){var e,t,n,r,i,o,s,a;for(l.sort((function(e,t){return e.__v.__b-t.__v.__b}));e=l.shift();)e.__d&&(t=l.length,r=void 0,i=void 0,s=(o=(n=e).__v).__e,(a=n.__P)&&(r=[],(i=p({},o)).__v=o.__v+1,H(a,o,i,n.__n,void 0!==a.ownerSVGElement,null!=o.__h?[s]:null,r,null==s?S(o):s,o.__h),P(r,o),o.__e!=s&&k(o)),l.length>t&&l.sort((function(e,t){return e.__v.__b-t.__v.__b})));R.__r=0}function M(e,t,n,r,i,o,s,a,l,c){var d,u,g,p,m,v,b,E=r&&r.__k||f,A=E.length;for(n.__k=[],d=0;d<t.length;d++)if(null!=(p=n.__k[d]=null==(p=t[d])||"boolean"==typeof p?null:"string"==typeof p||"number"==typeof p||"bigint"==typeof p?y(null,p,null,null,p):Array.isArray(p)?y(w,{children:p},null,null,null):p.__b>0?y(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)){if(p.__=n,p.__b=n.__b+1,null===(g=E[d])||g&&p.key==g.key&&p.type===g.type)E[d]=void 0;else for(u=0;u<A;u++){if((g=E[u])&&p.key==g.key&&p.type===g.type){E[u]=void 0;break}g=null}H(e,p,g=g||h,i,o,s,a,l,c),m=p.__e,(u=p.ref)&&g.ref!=u&&(b||(b=[]),g.ref&&b.push(g.ref,null,p),b.push(u,p.__c||m,p)),null!=m?(null==v&&(v=m),"function"==typeof p.type&&p.__k===g.__k?p.__d=l=I(p,l,e):l=O(e,p,g,E,m,l),"function"==typeof n.type&&(n.__d=l)):l&&g.__e==l&&l.parentNode!=e&&(l=S(g))}for(n.__e=v,d=A;d--;)null!=E[d]&&("function"==typeof n.type&&null!=E[d].__e&&E[d].__e==n.__d&&(n.__d=z(r).nextSibling),j(E[d],E[d]));if(b)for(d=0;d<b.length;d++)B(b[d],b[++d],b[++d])}function I(e,t,n){for(var r,i=e.__k,o=0;i&&o<i.length;o++)(r=i[o])&&(r.__=e,t="function"==typeof r.type?I(r,t,n):O(n,r,r,i,r.__e,t));return t}function N(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some((function(e){N(e,t)})):t.push(e)),t}function O(e,t,n,r,i,o){var s,a,l;if(void 0!==t.__d)s=t.__d,t.__d=void 0;else if(null==n||i!=o||null==i.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(i),s=null;else{for(a=o,l=0;(a=a.nextSibling)&&l<r.length;l+=1)if(a==i)break e;e.insertBefore(i,o),s=o}return void 0!==s?s:i.nextSibling}function z(e){var t,n,r;if(null==e.type||"string"==typeof e.type)return e.__e;if(e.__k)for(t=e.__k.length-1;t>=0;t--)if((n=e.__k[t])&&(r=z(n)))return r;return null}function H(e,t,n,r,o,s,a,l,c){var d,u,h,f,g,m,v,y,b,E,A,x,D,S,k,C=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(c=n.__h,l=t.__e=n.__e,t.__h=null,s=[l]),(d=i.__b)&&d(t);try{e:if("function"==typeof C){if(y=t.props,b=(d=C.contextType)&&r[d.__c],E=d?b?b.props.value:d.__:r,n.__c?v=(u=t.__c=n.__c).__=u.__E:("prototype"in C&&C.prototype.render?t.__c=u=new C(y,E):(t.__c=u=new T(y,E),u.constructor=C,u.render=U),b&&b.sub(u),u.props=y,u.state||(u.state={}),u.context=E,u.__n=r,h=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=C.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=p({},u.__s)),p(u.__s,C.getDerivedStateFromProps(y,u.__s))),f=u.props,g=u.state,u.__v=t,h)null==C.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==C.getDerivedStateFromProps&&y!==f&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(y,E),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(y,u.__s,E)||t.__v===n.__v){for(t.__v!==n.__v&&(u.props=y,u.state=u.__s,u.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),A=0;A<u._sb.length;A++)u.__h.push(u._sb[A]);u._sb=[],u.__h.length&&a.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(y,u.__s,E),null!=u.componentDidUpdate&&u.__h.push((function(){u.componentDidUpdate(f,g,m)}))}if(u.context=E,u.props=y,u.__P=e,x=i.__r,D=0,"prototype"in C&&C.prototype.render){for(u.state=u.__s,u.__d=!1,x&&x(t),d=u.render(u.props,u.state,u.context),S=0;S<u._sb.length;S++)u.__h.push(u._sb[S]);u._sb=[]}else do{u.__d=!1,x&&x(t),d=u.render(u.props,u.state,u.context),u.state=u.__s}while(u.__d&&++D<25);u.state=u.__s,null!=u.getChildContext&&(r=p(p({},r),u.getChildContext())),h||null==u.getSnapshotBeforeUpdate||(m=u.getSnapshotBeforeUpdate(f,g)),k=null!=d&&d.type===w&&null==d.key?d.props.children:d,M(e,Array.isArray(k)?k:[k],t,n,r,o,s,a,l,c),u.base=t.__e,t.__h=null,u.__h.length&&a.push(u),v&&(u.__E=u.__=null),u.__e=!1}else null==s&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=L(n.__e,t,n,r,o,s,a,c);(d=i.diffed)&&d(t)}catch(e){t.__v=null,(c||null!=s)&&(t.__e=l,t.__h=!!c,s[s.indexOf(l)]=null),i.__e(e,t,n)}}function P(e,t){i.__c&&i.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){i.__e(e,t.__v)}}))}function L(e,t,n,i,o,s,a,l){var c,d,u,f=n.props,g=t.props,p=t.type,v=0;if("svg"===p&&(o=!0),null!=s)for(;v<s.length;v++)if((c=s[v])&&"setAttribute"in c==!!p&&(p?c.localName===p:3===c.nodeType)){e=c,s[v]=null;break}if(null==e){if(null===p)return document.createTextNode(g);e=o?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,g.is&&g),s=null,l=!1}if(null===p)f===g||l&&e.data===g||(e.data=g);else{if(s=s&&r.call(e.childNodes),d=(f=n.props||h).dangerouslySetInnerHTML,u=g.dangerouslySetInnerHTML,!l){if(null!=s)for(f={},v=0;v<e.attributes.length;v++)f[e.attributes[v].name]=e.attributes[v].value;(u||d)&&(u&&(d&&u.__html==d.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,n,r,i){var o;for(o in n)"children"===o||"key"===o||o in t||A(e,o,null,n[o],r);for(o in t)i&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||n[o]===t[o]||A(e,o,t[o],n[o],r)}(e,g,f,o,l),u)t.__k=[];else if(v=t.props.children,M(e,Array.isArray(v)?v:[v],t,n,i,o&&"foreignObject"!==p,s,a,s?s[0]:n.__k&&S(n,0),l),null!=s)for(v=s.length;v--;)null!=s[v]&&m(s[v]);l||("value"in g&&void 0!==(v=g.value)&&(v!==e.value||"progress"===p&&!v||"option"===p&&v!==f.value)&&A(e,"value",v,f.value,!1),"checked"in g&&void 0!==(v=g.checked)&&v!==e.checked&&A(e,"checked",v,f.checked,!1))}return e}function B(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){i.__e(e,n)}}function j(e,t,n){var r,o;if(i.unmount&&i.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||B(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){i.__e(e,t)}r.base=r.__P=null,e.__c=void 0}if(r=e.__k)for(o=0;o<r.length;o++)r[o]&&j(r[o],t,n||"function"!=typeof e.type);n||null==e.__e||m(e.__e),e.__=e.__e=e.__d=void 0}function U(e,t,n){return this.constructor(e,n)}function W(e,t,n){var o,s,a;i.__&&i.__(e,t),s=(o="function"==typeof n)?null:n&&n.__k||t.__k,a=[],H(t,e=(!o&&n||t).__k=v(w,null,[e]),s||h,h,void 0!==t.ownerSVGElement,!o&&n?[n]:s?null:t.firstChild?r.call(t.childNodes):null,a,!o&&n?n:s?s.__e:t.firstChild,o),P(a,e)}function V(e,t){var n={__c:t="__cC"+u++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,_(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}r=f.slice,i={__e:function(e,t,n,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),s=i.__d),s)return i.__E=i}catch(t){e=t}throw e}},o=0,s=function(e){return null!=e&&void 0===e.constructor},a=!1,T.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=p({},this.state),"function"==typeof e&&(e=e(p({},n),this.props)),e&&p(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),_(this))},T.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),_(this))},T.prototype.render=w,l=[],d="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,R.__r=0,u=0},88977:function(e,t,n){n.d(t,{L:()=>o});n("19134"),n("44495"),n("32114");const r={en:"US",hi:"IN",deva:"IN",te:"IN",mr:"IN",ta:"IN",gu:"IN",kn:"IN",or:"IN",ml:"IN",pa:"IN",bho:"IN",awa:"IN",as:"IN",mwr:"IN",mai:"IN",mag:"IN",bgc:"IN",hne:"IN",dcc:"IN",bn:"BD",beng:"BD",rkt:"BD",dz:"BT",tibt:"BT",tn:"BW",am:"ET",ethi:"ET",om:"ET",quc:"GT",id:"ID",jv:"ID",su:"ID",mad:"ID",ms_arab:"ID",he:"IL",hebr:"IL",jam:"JM",ja:"JP",jpan:"JP",km:"KH",khmr:"KH",ko:"KR",kore:"KR",lo:"LA",laoo:"LA",mh:"MH",my:"MM",mymr:"MM",mt:"MT",ne:"NP",fil:"PH",ceb:"PH",ilo:"PH",ur:"PK",pa_arab:"PK",lah:"PK",ps:"PK",sd:"PK",skr:"PK",gn:"PY",th:"TH",thai:"TH",tts:"TH",zh_hant:"TW",hant:"TW",sm:"WS",zu:"ZA",sn:"ZW",arq:"DZ",ar:"EG",arab:"EG",arz:"EG",fa:"IR",az_arab:"IR",dv:"MV",thaa:"MV"};const i={AG:0,ATG:0,28:0,AS:0,ASM:0,16:0,BD:0,BGD:0,50:0,BR:0,BRA:0,76:0,BS:0,BHS:0,44:0,BT:0,BTN:0,64:0,BW:0,BWA:0,72:0,BZ:0,BLZ:0,84:0,CA:0,CAN:0,124:0,CO:0,COL:0,170:0,DM:0,DMA:0,212:0,DO:0,DOM:0,214:0,ET:0,ETH:0,231:0,GT:0,GTM:0,320:0,GU:0,GUM:0,316:0,HK:0,HKG:0,344:0,HN:0,HND:0,340:0,ID:0,IDN:0,360:0,IL:0,ISR:0,376:0,IN:0,IND:0,356:0,JM:0,JAM:0,388:0,JP:0,JPN:0,392:0,KE:0,KEN:0,404:0,KH:0,KHM:0,116:0,KR:0,KOR:0,410:0,LA:0,LA0:0,418:0,MH:0,MHL:0,584:0,MM:0,MMR:0,104:0,MO:0,MAC:0,446:0,MT:0,MLT:0,470:0,MX:0,MEX:0,484:0,MZ:0,MOZ:0,508:0,NI:0,NIC:0,558:0,NP:0,NPL:0,524:0,PA:0,PAN:0,591:0,PE:0,PER:0,604:0,PH:0,PHL:0,608:0,PK:0,PAK:0,586:0,PR:0,PRI:0,630:0,PT:0,PRT:0,620:0,PY:0,PRY:0,600:0,SA:0,SAU:0,682:0,SG:0,SGP:0,702:0,SV:0,SLV:0,222:0,TH:0,THA:0,764:0,TT:0,TTO:0,780:0,TW:0,TWN:0,158:0,UM:0,UMI:0,581:0,US:0,USA:0,840:0,VE:0,VEN:0,862:0,VI:0,VIR:0,850:0,WS:0,WSM:0,882:0,YE:0,YEM:0,887:0,ZA:0,ZAF:0,710:0,ZW:0,ZWE:0,716:0,AE:6,ARE:6,784:6,AF:6,AFG:6,4:6,BH:6,BHR:6,48:6,DJ:6,DJI:6,262:6,DZ:6,DZA:6,12:6,EG:6,EGY:6,818:6,IQ:6,IRQ:6,368:6,IR:6,IRN:6,364:6,JO:6,JOR:6,400:6,KW:6,KWT:6,414:6,LY:6,LBY:6,434:6,OM:6,OMN:6,512:6,QA:6,QAT:6,634:6,SD:6,SDN:6,729:6,SY:6,SYR:6,760:6,MV:5,MDV:5,462:5};function o(e){return function(e,t,n){if(e){var r,i=e.toLowerCase().split(/[-_]/),o=i[0],s=o;if(i[1]&&4===i[1].length?(s+="_"+i[1],r=i[2]):r=i[1],r||(r=t[s]||t[o]),r)return function(e,t){var n=t["string"==typeof e?e.toUpperCase():e];return"number"==typeof n?n:1}(r.match(/^\d+$/)?Number(r):r,n)}return 1}(e,r,i)}},68107:function(e,t,n){var r=n(40810),i=n(73994),o=n(63983),s=n(71998),a=n(4576);r({target:"Iterator",proto:!0,real:!0},{every:function(e){s(this),o(e);var t=a(this),n=0;return!i(t,(function(t,r){if(!e(t,n++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},92181:function(e,t,n){n(68107)},95559:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{f:function(){return ze},rx:function(){return v}});var i=n(16485),o=(n(52247),n(71695),n(92745),n(84283),n(77439),n(19423),n(19134),n(5740),n(13334),n(47021),n(66952)),s=n(65054),a=(n(20378),e([i,o]));[i,o]=a.then?(await a)():a;const c=[],d={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},u=Object.assign(Object.assign({},d),{buttonHints:{prev:"Previous $0",next:"Next $0",today(e,t){return"day"===t?"Today":`This ${e}`}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(e){return`Show ${e} more event${1===e?"":"s"}`}});function h(e){let t=e.length>0?e[0].code:"en",n=c.concat(e),r={en:u};for(let i of n)r[i.code]=i;return{map:r,defaultCode:t}}function f(e,t){return"object"!=typeof e||Array.isArray(e)?g(e,t):m(e.code,[e.code],e)}function g(e,t){let n=[].concat(e||[]);return m(e,n,p(n,t)||u)}function p(e,t){for(let n=0;n<e.length;n+=1){let r=e[n].toLocaleLowerCase().split("-");for(let e=r.length;e>0;e-=1){let n=r.slice(0,e).join("-");if(t[n])return t[n]}}return null}function m(e,t,n){let r=(0,o.m)([d,n],["buttonText"]);delete r.code;let{week:i}=r;return delete r.week,{codeArg:e,codes:t,week:i,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}function v(e){return{id:(0,o.g)(),name:e.name,premiumReleaseDate:e.premiumReleaseDate?new Date(e.premiumReleaseDate):void 0,deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}function y(e,t){let n={},r={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(e){for(let s of e){const e=s.name,a=n[e];void 0===a?(n[e]=s.id,i(s.deps),o=s,r={premiumReleaseDate:w((t=r).premiumReleaseDate,o.premiumReleaseDate),reducers:t.reducers.concat(o.reducers),isLoadingFuncs:t.isLoadingFuncs.concat(o.isLoadingFuncs),contextInit:t.contextInit.concat(o.contextInit),eventRefiners:Object.assign(Object.assign({},t.eventRefiners),o.eventRefiners),eventDefMemberAdders:t.eventDefMemberAdders.concat(o.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},t.eventSourceRefiners),o.eventSourceRefiners),isDraggableTransformers:t.isDraggableTransformers.concat(o.isDraggableTransformers),eventDragMutationMassagers:t.eventDragMutationMassagers.concat(o.eventDragMutationMassagers),eventDefMutationAppliers:t.eventDefMutationAppliers.concat(o.eventDefMutationAppliers),dateSelectionTransformers:t.dateSelectionTransformers.concat(o.dateSelectionTransformers),datePointTransforms:t.datePointTransforms.concat(o.datePointTransforms),dateSpanTransforms:t.dateSpanTransforms.concat(o.dateSpanTransforms),views:Object.assign(Object.assign({},t.views),o.views),viewPropsTransformers:t.viewPropsTransformers.concat(o.viewPropsTransformers),isPropsValid:o.isPropsValid||t.isPropsValid,externalDefTransforms:t.externalDefTransforms.concat(o.externalDefTransforms),viewContainerAppends:t.viewContainerAppends.concat(o.viewContainerAppends),eventDropTransformers:t.eventDropTransformers.concat(o.eventDropTransformers),calendarInteractions:t.calendarInteractions.concat(o.calendarInteractions),componentInteractions:t.componentInteractions.concat(o.componentInteractions),themeClasses:Object.assign(Object.assign({},t.themeClasses),o.themeClasses),eventSourceDefs:t.eventSourceDefs.concat(o.eventSourceDefs),cmdFormatter:o.cmdFormatter||t.cmdFormatter,recurringTypes:t.recurringTypes.concat(o.recurringTypes),namedTimeZonedImpl:o.namedTimeZonedImpl||t.namedTimeZonedImpl,initialView:t.initialView||o.initialView,elementDraggingImpl:t.elementDraggingImpl||o.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},t.optionChangeHandlers),o.optionChangeHandlers),scrollGridImpl:o.scrollGridImpl||t.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},t.listenerRefiners),o.listenerRefiners),optionRefiners:Object.assign(Object.assign({},t.optionRefiners),o.optionRefiners),propSetHandlers:Object.assign(Object.assign({},t.propSetHandlers),o.propSetHandlers)}):a!==s.id&&console.warn(`Duplicate plugin '${e}'`)}var t,o}return e&&i(e),i(t),r}function b(){let e,t=[],n=[];return(r,i)=>(e&&(0,o.i)(r,t)&&(0,o.i)(i,n)||(e=y(r,i)),t=r,n=i,e)}function w(e,t){return void 0===e?t:void 0===t?e:new Date(Math.max(e.valueOf(),t.valueOf()))}class E extends o.T{}function A(e,t){let n,r={};for(n in e)x(n,r,e,t);for(n in t)x(n,r,e,t);return r}function x(e,t,n,r){if(t[e])return t[e];let i=D(e,t,n,r);return i&&(t[e]=i),i}function D(e,t,n,r){let i=n[e],o=r[e],s=e=>i&&null!==i[e]?i[e]:o&&null!==o[e]?o[e]:null,a=s("component"),l=s("superType"),c=null;if(l){if(l===e)throw new Error("Can't have a custom view type that references itself");c=x(l,t,n,r)}return!a&&c&&(a=c.component),a?{type:e,component:a,defaults:Object.assign(Object.assign({},c?c.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},c?c.overrides:{}),o?o.rawOptions:{})}:null}function T(e){return(0,o.a)(e,S)}function S(e){let t="function"==typeof e?{component:e}:e,{component:n}=t;return t.content?n=k(t):!n||n.prototype instanceof o.B||(n=k(Object.assign(Object.assign({},t),{content:n}))),{superType:t.type,component:n,rawOptions:t}}function k(e){return t=>(0,s.az)(o.V.Consumer,null,(n=>(0,s.az)(o.C,{elTag:"div",elClasses:(0,o.b)(n.viewSpec),renderProps:Object.assign(Object.assign({},t),{nextDayThreshold:n.options.nextDayThreshold}),generatorName:void 0,customGenerator:e.content,classNameGenerator:e.classNames,didMount:e.didMount,willUnmount:e.willUnmount})))}function C(e,t,n,r){let i=T(e),s=T(t.views),a=A(i,s);return(0,o.a)(a,(e=>_(e,s,t,n,r)))}function _(e,t,n,r,i){let s=e.overrides.duration||e.defaults.duration||r.duration||n.duration,a=null,l="",c="",d={};if(s&&(a=M(s),a)){let e=(0,o.c)(a);l=e.unit,1===e.value&&(c=l,d=t[l]?t[l].rawOptions:{})}let u=t=>{let n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[c]?n[c]:null},h=t=>{let n=t.buttonHints||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[c]?n[c]:null};return{type:e.type,component:e.component,duration:a,durationUnit:l,singleUnit:c,optionDefaults:e.defaults,optionOverrides:Object.assign(Object.assign({},d),e.overrides),buttonTextOverride:u(r)||u(n)||e.overrides.buttonText,buttonTextDefault:u(i)||e.defaults.buttonText||u(o.e)||e.type,buttonTitleOverride:h(r)||h(n)||e.overrides.buttonHint,buttonTitleDefault:h(i)||e.defaults.buttonHint||h(o.e)}}E.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},E.prototype.baseIconClass="fc-icon",E.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},E.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},E.prototype.iconOverrideOption="buttonIcons",E.prototype.iconOverrideCustomButtonOption="icon",E.prototype.iconOverridePrefix="fc-icon-";let R={};function M(e){let t=JSON.stringify(e),n=R[t];return void 0===n&&(n=(0,o.d)(e),R[t]=n),n}function I(e,t){if("CHANGE_VIEW_TYPE"===t.type)e=t.viewType;return e}function N(e,t){return"SET_OPTION"===t.type?Object.assign(Object.assign({},e),{[t.optionName]:t.rawOptionValue}):e}function O(e,t,n,r){let i;switch(t.type){case"CHANGE_VIEW_TYPE":return r.build(t.dateMarker||n);case"CHANGE_DATE":return r.build(t.dateMarker);case"PREV":if(i=r.buildPrev(e,n),i.isValid)return i;break;case"NEXT":if(i=r.buildNext(e,n),i.isValid)return i}return e}function z(e,t,n){let r=t?t.activeRange:null;return B({},Y(e,n),r,n)}function H(e,t,n,r){let i=n?n.activeRange:null;switch(t.type){case"ADD_EVENT_SOURCES":return B(e,t.sources,i,r);case"REMOVE_EVENT_SOURCE":return s=e,a=t.sourceId,(0,o.h)(s,(e=>e.sourceId!==a));case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?j(e,i,r):e;case"FETCH_EVENT_SOURCES":return W(e,t.sourceIds?(0,o.f)(t.sourceIds):G(e,r),i,t.isRefetch||!1,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return F(e,t.sourceId,t.fetchId,t.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}var s,a}function P(e,t,n){let r=t?t.activeRange:null;return W(e,G(e,n),r,!0,n)}function L(e){for(let t in e)if(e[t].isFetching)return!0;return!1}function B(e,t,n,r){let i={};for(let o of t)i[o.sourceId]=o;return n&&(i=j(i,n,r)),Object.assign(Object.assign({},e),i)}function j(e,t,n){return W(e,(0,o.h)(e,(e=>U(e,t,n))),t,!1,n)}function U(e,t,n){return Q(e,n)?!n.options.lazyFetching||!e.fetchRange||e.isFetching||t.start<e.fetchRange.start||t.end>e.fetchRange.end:!e.latestFetchId}function W(e,t,n,r,i){let o={};for(let s in e){let a=e[s];t[s]?o[s]=V(a,n,r,i):o[s]=a}return o}function V(e,t,n,r){let{options:i,calendarApi:s}=r,a=r.pluginHooks.eventSourceDefs[e.sourceDefId],l=(0,o.g)();return a.fetch({eventSource:e,range:t,isRefetch:n,context:r},(n=>{let{rawEvents:o}=n;i.eventSourceSuccess&&(o=i.eventSourceSuccess.call(s,o,n.response)||o),e.success&&(o=e.success.call(s,o,n.response)||o),r.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:l,fetchRange:t,rawEvents:o})}),(n=>{let o=!1;i.eventSourceFailure&&(i.eventSourceFailure.call(s,n),o=!0),e.failure&&(e.failure(n),o=!0),o||console.warn(n.message,n),r.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:l,fetchRange:t,error:n})})),Object.assign(Object.assign({},e),{isFetching:!0,latestFetchId:l})}function F(e,t,n,r){let i=e[t];return i&&n===i.latestFetchId?Object.assign(Object.assign({},e),{[t]:Object.assign(Object.assign({},i),{isFetching:!1,fetchRange:r})}):e}function G(e,t){return(0,o.h)(e,(e=>Q(e,t)))}function Y(e,t){let n=(0,o.j)(t),r=[].concat(e.eventSources||[]),i=[];e.initialEvents&&r.unshift(e.initialEvents),e.events&&r.unshift(e.events);for(let s of r){let e=(0,o.p)(s,t,n);e&&i.push(e)}return i}function Q(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function q(e,t){switch(t.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return t.selection;default:return e}}function Z(e,t){switch(t.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return t.eventInstanceId;default:return e}}function $(e,t){let n;switch(t.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return n=t.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function X(e,t){let n;switch(t.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return n=t.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function J(e,t,n,r,i){return{header:e.headerToolbar?K(e.headerToolbar,e,t,n,r,i):null,footer:e.footerToolbar?K(e.footerToolbar,e,t,n,r,i):null}}function K(e,t,n,r,i,o){let s={},a=[],l=!1;for(let c in e){let d=ee(e[c],t,n,r,i,o);s[c]=d.widgets,a.push(...d.viewsWithButtons),l=l||d.hasTitle}return{sectionWidgets:s,viewsWithButtons:a,hasTitle:l}}function ee(e,t,n,r,i,s){let a="rtl"===t.direction,l=t.customButtons||{},c=n.buttonText||{},d=t.buttonText||{},u=n.buttonHints||{},h=t.buttonHints||{},f=e?e.split(" "):[],g=[],p=!1;return{widgets:f.map((e=>e.split(",").map((e=>{if("title"===e)return p=!0,{buttonName:e};let n,f,m,v,y,b;if(n=l[e])m=e=>{n.click&&n.click.call(e.target,e,e.target)},(v=r.getCustomButtonIconClass(n))||(v=r.getIconClass(e,a))||(y=n.text),b=n.hint||n.text;else if(f=i[e]){g.push(e),m=()=>{s.changeView(e)},(y=f.buttonTextOverride)||(v=r.getIconClass(e,a))||(y=f.buttonTextDefault);let n=f.buttonTextOverride||f.buttonTextDefault;b=(0,o.k)(f.buttonTitleOverride||f.buttonTitleDefault||t.viewHint,[n,e],n)}else if(s[e])if(m=()=>{s[e]()},(y=c[e])||(v=r.getIconClass(e,a))||(y=d[e]),"prevYear"===e||"nextYear"===e){let t="prevYear"===e?"prev":"next";b=(0,o.k)(u[t]||h[t],[d.year||"year","year"],d[e])}else b=t=>(0,o.k)(u[e]||h[e],[d[t]||t,t],d[e]);return{buttonName:e,buttonClick:m,buttonIcon:v,buttonText:y,buttonHint:b}})))),viewsWithButtons:g,hasTitle:p}}class te{constructor(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}const ne=v({name:"array-event-source",eventSourceDefs:[{ignoreRange:!0,parseMeta(e){return Array.isArray(e.events)?e.events:null},fetch(e,t){t({rawEvents:e.eventSource.meta})}}]});const re=v({name:"func-event-source",eventSourceDefs:[{parseMeta(e){return"function"==typeof e.events?e.events:null},fetch(e,t,n){const{dateEnv:r}=e.context,i=e.eventSource.meta;(0,o.u)(i.bind(null,(0,o.l)(e.range,r)),(e=>t({rawEvents:e})),n)}}]});const ie=v({name:"json-event-source",eventSourceRefiners:{method:String,extraParams:o.n,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta(e){return!e.url||"json"!==e.format&&e.format?null:{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}},fetch(e,t,n){const{meta:r}=e.eventSource,i=oe(r,e.range,e.context);(0,o.r)(r.method,r.url,i).then((([e,n])=>{t({rawEvents:e,response:n})}),n)}}]});function oe(e,t,n){let r,i,o,s,{dateEnv:a,options:l}=n,c={};return r=e.startParam,null==r&&(r=l.startParam),i=e.endParam,null==i&&(i=l.endParam),o=e.timeZoneParam,null==o&&(o=l.timeZoneParam),s="function"==typeof e.extraParams?e.extraParams():e.extraParams||{},Object.assign(c,s),c[r]=a.formatIso(t.start),c[i]=a.formatIso(t.end),"local"!==a.timeZone&&(c[o]=a.timeZone),c}const se={daysOfWeek:o.n,startTime:o.d,endTime:o.d,duration:o.d,startRecur:o.n,endRecur:o.n};const ae=v({name:"simple-recurring-event",recurringTypes:[{parse(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){let n,r={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null};return e.duration&&(n=e.duration),!n&&e.startTime&&e.endTime&&(n=(0,o.s)(e.endTime,e.startTime)),{allDayGuess:Boolean(!e.startTime&&!e.endTime),duration:n,typeData:r}}return null},expand(e,t,n){let r=(0,o.o)(t,{start:e.startRecur,end:e.endRecur});return r?le(e.daysOfWeek,e.startTime,r,n):[]}}],eventRefiners:se});function le(e,t,n,r){let i=e?(0,o.f)(e):null,s=(0,o.q)(n.start),a=n.end,l=[];for(;s<a;){let e;i&&!i[s.getUTCDay()]||(e=t?r.add(s,t):s,l.push(e)),s=(0,o.t)(s,1)}return l}function ce(e,t){let n=(0,o.v)(t.getCurrentData().eventSources);if(1===n.length&&1===e.length&&Array.isArray(n[0]._raw)&&Array.isArray(e[0]))return void t.dispatch({type:"RESET_RAW_EVENTS",sourceId:n[0].sourceId,rawEvents:e[0]});let r=[];for(let i of e){let e=!1;for(let t=0;t<n.length;t+=1)if(n[t]._raw===i){n.splice(t,1),e=!0;break}e||r.push(i)}for(let i of n)t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:i.sourceId});for(let i of r)t.calendarApi.addEventSource(i)}function de(e,t){t.emitter.trigger("datesSet",Object.assign(Object.assign({},(0,o.l)(e.activeRange,t.dateEnv)),{view:t.viewApi}))}function ue(e,t){let{emitter:n}=t;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",(0,o.w)(e,t))}const he=[ne,re,ie,ae,v({name:"change-handler",optionChangeHandlers:{events(e,t){ce([e],t)},eventSources:ce}}),v({name:"misc",isLoadingFuncs:[e=>L(e.eventSources)],propSetHandlers:{dateProfile:de,eventStore:ue}})];class fe{constructor(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new o.D(this.drain.bind(this))}request(e,t){this.queue.push(e),this.delayedRunner.request(t)}pause(e){this.delayedRunner.pause(e)}resume(e,t){this.delayedRunner.resume(e,t)}drain(){let{queue:e}=this;for(;e.length;){let t,n=[];for(;t=e.shift();)this.runTask(t),n.push(t);this.drained(n)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function ge(e,t,n){let r;return r=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(r.start,r.end,(0,o.x)(t.titleFormat||pe(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}function pe(e){let{currentRangeUnit:t}=e;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};let n=(0,o.y)(e.currentRange.start,e.currentRange.end);return null!==n&&n>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}class me{constructor(e){this.computeCurrentViewData=(0,o.z)(this._computeCurrentViewData),this.organizeRawLocales=(0,o.z)(h),this.buildLocale=(0,o.z)(f),this.buildPluginHooks=b(),this.buildDateEnv=(0,o.z)(ve),this.buildTheme=(0,o.z)(ye),this.parseToolbars=(0,o.z)(J),this.buildViewSpecs=(0,o.z)(C),this.buildDateProfileGenerator=(0,o.A)(be),this.buildViewApi=(0,o.z)(we),this.buildViewUiProps=(0,o.A)(xe),this.buildEventUiBySource=(0,o.z)(Ee,o.E),this.buildEventUiBases=(0,o.z)(Ae),this.parseContextBusinessHours=(0,o.A)(Te),this.buildTitle=(0,o.z)(ge),this.emitter=new o.F,this.actionRunner=new fe(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=e=>{this.actionRunner.request(e)},this.props=e,this.actionRunner.pause();let t={},n=this.computeOptionsData(e.optionOverrides,t,e.calendarApi),r=n.calendarOptions.initialView||n.pluginHooks.initialView,i=this.computeCurrentViewData(r,n,e.optionOverrides,t);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(i.options);let s=(0,o.G)(n.calendarOptions,n.dateEnv),a=i.dateProfileGenerator.build(s);(0,o.H)(a.activeRange,s)||(s=a.currentRange.start);let l={dateEnv:n.dateEnv,options:n.calendarOptions,pluginHooks:n.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData};for(let o of n.pluginHooks.contextInit)o(l);let c=z(n.calendarOptions,a,l),d={dynamicOptionOverrides:t,currentViewType:r,currentDate:s,dateProfile:a,businessHours:this.parseContextBusinessHours(l),eventSources:c,eventUiBases:{},eventStore:(0,o.I)(),renderableEventStore:(0,o.I)(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(l).selectionConfig},u=Object.assign(Object.assign({},l),d);for(let o of n.pluginHooks.reducers)Object.assign(d,o(null,null,u));De(d,l)&&this.emitter.trigger("loading",!0),this.state=d,this.updateData(),this.actionRunner.resume()}resetOptions(e,t){let{props:n}=this;void 0===t?n.optionOverrides=e:(n.optionOverrides=Object.assign(Object.assign({},n.optionOverrides||{}),e),this.optionsForRefining.push(...t)),(void 0===t||t.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){let{props:t,state:n,emitter:r}=this,i=N(n.dynamicOptionOverrides,e),s=this.computeOptionsData(t.optionOverrides,i,t.calendarApi),a=I(n.currentViewType,e),l=this.computeCurrentViewData(a,s,t.optionOverrides,i);t.calendarApi.currentDataManager=this,r.setThisContext(t.calendarApi),r.setOptions(l.options);let c={dateEnv:s.dateEnv,options:s.calendarOptions,pluginHooks:s.pluginHooks,calendarApi:t.calendarApi,dispatch:this.dispatch,emitter:r,getCurrentData:this.getCurrentData},{currentDate:d,dateProfile:u}=n;this.data&&this.data.dateProfileGenerator!==l.dateProfileGenerator&&(u=l.dateProfileGenerator.build(d)),d=(0,o.J)(d,e),u=O(u,e,d,l.dateProfileGenerator),"PREV"!==e.type&&"NEXT"!==e.type&&(0,o.H)(u.currentRange,d)||(d=u.currentRange.start);let h=H(n.eventSources,e,u,c),f=(0,o.K)(n.eventStore,e,h,u,c),g=L(h)&&!l.options.progressiveEventRendering&&n.renderableEventStore||f,{eventUiSingleBase:p,selectionConfig:m}=this.buildViewUiProps(c),v=this.buildEventUiBySource(h),y={dynamicOptionOverrides:i,currentViewType:a,currentDate:d,dateProfile:u,eventSources:h,eventStore:f,renderableEventStore:g,selectionConfig:m,eventUiBases:this.buildEventUiBases(g.defs,p,v),businessHours:this.parseContextBusinessHours(c),dateSelection:q(n.dateSelection,e),eventSelection:Z(n.eventSelection,e),eventDrag:$(n.eventDrag,e),eventResize:X(n.eventResize,e)},b=Object.assign(Object.assign({},c),y);for(let o of s.pluginHooks.reducers)Object.assign(y,o(n,e,b));let w=De(n,c),E=De(y,c);!w&&E?r.trigger("loading",!0):w&&!E&&r.trigger("loading",!1),this.state=y,t.onAction&&t.onAction(e)}updateData(){let{props:e,state:t}=this,n=this.data,r=this.computeOptionsData(e.optionOverrides,t.dynamicOptionOverrides,e.calendarApi),i=this.computeCurrentViewData(t.currentViewType,r,e.optionOverrides,t.dynamicOptionOverrides),s=this.data=Object.assign(Object.assign(Object.assign({viewTitle:this.buildTitle(t.dateProfile,i.options,r.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},r),i),t),a=r.pluginHooks.optionChangeHandlers,l=n&&n.calendarOptions,c=r.calendarOptions;if(l&&l!==c){l.timeZone!==c.timeZone&&(t.eventSources=s.eventSources=P(s.eventSources,t.dateProfile,s),t.eventStore=s.eventStore=(0,o.L)(s.eventStore,n.dateEnv,s.dateEnv),t.renderableEventStore=s.renderableEventStore=(0,o.L)(s.renderableEventStore,n.dateEnv,s.dateEnv));for(let e in a)-1===this.optionsForHandling.indexOf(e)&&l[e]===c[e]||a[e](c[e],s)}this.optionsForHandling=[],e.onData&&e.onData(s)}computeOptionsData(e,t,n){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&t===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:r,pluginHooks:i,localeDefaults:o,availableLocaleData:s,extra:a}=this.processRawCalendarOptions(e,t);Se(a);let l=this.buildDateEnv(r.timeZone,r.locale,r.weekNumberCalculation,r.firstDay,r.weekText,i,s,r.defaultRangeSeparator),c=this.buildViewSpecs(i.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,o),d=this.buildTheme(r,i),u=this.parseToolbars(r,this.stableOptionOverrides,d,c,n);return this.stableCalendarOptionsData={calendarOptions:r,pluginHooks:i,dateEnv:l,viewSpecs:c,theme:d,toolbarConfig:u,localeDefaults:o,availableRawLocales:s.map}}processRawCalendarOptions(e,t){let{locales:n,locale:r}=(0,o.M)([o.e,e,t]),i=this.organizeRawLocales(n),s=i.map,a=this.buildLocale(r||i.defaultCode,s).options,l=this.buildPluginHooks(e.plugins||[],he),c=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},o.N),o.O),o.P),l.listenerRefiners),l.optionRefiners),d={},u=(0,o.M)([o.e,a,e,t]),h={},f=this.currentCalendarOptionsInput,g=this.currentCalendarOptionsRefined,p=!1;for(let m in u)-1===this.optionsForRefining.indexOf(m)&&(u[m]===f[m]||o.Q[m]&&m in f&&o.Q[m](f[m],u[m]))?h[m]=g[m]:c[m]?(h[m]=c[m](u[m]),p=!0):d[m]=f[m];return p&&(this.currentCalendarOptionsInput=u,this.currentCalendarOptionsRefined=h,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=t),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:l,availableLocaleData:i,localeDefaults:a,extra:d}}_computeCurrentViewData(e,t,n,r){let i=t.viewSpecs[e];if(!i)throw new Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:o,extra:s}=this.processRawViewOptions(i,t.pluginHooks,t.localeDefaults,n,r);return Se(s),{viewSpec:i,options:o,dateProfileGenerator:this.buildDateProfileGenerator({dateProfileGeneratorClass:i.optionDefaults.dateProfileGeneratorClass,duration:i.duration,durationUnit:i.durationUnit,usesMinMaxTime:i.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:o.slotMinTime,slotMaxTime:o.slotMaxTime,showNonCurrentDates:o.showNonCurrentDates,dayCount:o.dayCount,dateAlignment:o.dateAlignment,dateIncrement:o.dateIncrement,hiddenDays:o.hiddenDays,weekends:o.weekends,nowInput:o.now,validRangeInput:o.validRange,visibleRangeInput:o.visibleRange,fixedWeekCount:o.fixedWeekCount}),viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}}processRawViewOptions(e,t,n,r,i){let s=(0,o.M)([o.e,e.optionDefaults,n,r,e.optionOverrides,i]),a=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},o.N),o.O),o.P),o.R),t.listenerRefiners),t.optionRefiners),l={},c=this.currentViewOptionsInput,d=this.currentViewOptionsRefined,u=!1,h={};for(let f in s)s[f]===c[f]||o.Q[f]&&o.Q[f](s[f],c[f])?l[f]=d[f]:(s[f]===this.currentCalendarOptionsInput[f]||o.Q[f]&&o.Q[f](s[f],this.currentCalendarOptionsInput[f])?f in this.currentCalendarOptionsRefined&&(l[f]=this.currentCalendarOptionsRefined[f]):a[f]?l[f]=a[f](s[f]):h[f]=s[f],u=!0);return u&&(this.currentViewOptionsInput=s,this.currentViewOptionsRefined=l),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:h}}}function ve(e,t,n,r,i,s,a,l){let c=f(t||a.defaultCode,a.map);return new o.S({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:s.namedTimeZonedImpl,locale:c,weekNumberCalculation:n,firstDay:r,weekText:i,cmdFormatter:s.cmdFormatter,defaultSeparator:l})}function ye(e,t){return new(t.themeClasses[e.themeSystem]||E)(e)}function be(e){return new(e.dateProfileGeneratorClass||o.U)(e)}function we(e,t,n){return new te(e,t,n)}function Ee(e){return(0,o.a)(e,(e=>e.ui))}function Ae(e,t,n){let r={"":t};for(let i in e){let t=e[i];t.sourceId&&n[t.sourceId]&&(r[i]=n[t.sourceId])}return r}function xe(e){let{options:t}=e;return{eventUiSingleBase:(0,o.W)({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"==typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:(0,o.W)({constraint:t.selectConstraint,overlap:"boolean"==typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function De(e,t){for(let n of t.pluginHooks.isLoadingFuncs)if(n(e))return!0;return!1}function Te(e){return(0,o.X)(e.options.businessHours,e)}function Se(e,t){for(let n in e)console.warn(`Unknown option '${n}'`+(t?` for view '${t}'`:""))}class ke extends o.B{render(){let e=this.props.widgetGroups.map((e=>this.renderWidgetGroup(e)));return(0,s.az)("div",{className:"fc-toolbar-chunk"},...e)}renderWidgetGroup(e){let{props:t}=this,{theme:n}=this.context,r=[],i=!0;for(let o of e){let{buttonName:e,buttonClick:a,buttonText:l,buttonIcon:c,buttonHint:d}=o;if("title"===e)i=!1,r.push((0,s.az)("h2",{className:"fc-toolbar-title",id:t.titleId},t.title));else{let i=e===t.activeButton,o=!t.isTodayEnabled&&"today"===e||!t.isPrevEnabled&&"prev"===e||!t.isNextEnabled&&"next"===e,u=[`fc-${e}-button`,n.getClass("button")];i&&u.push(n.getClass("buttonActive")),r.push((0,s.az)("button",{type:"button",title:"function"==typeof d?d(t.navUnit):d,disabled:o,"aria-pressed":i,className:u.join(" "),onClick:a},l||(c?(0,s.az)("span",{className:c,role:"img"}):"")))}}if(r.length>1){let e=i&&n.getClass("buttonGroup")||"";return(0,s.az)("div",{className:e},...r)}return r[0]}}class Ce extends o.B{render(){let e,t,{model:n,extraClassName:r}=this.props,i=!1,o=n.sectionWidgets,a=o.center;o.left?(i=!0,e=o.left):e=o.start,o.right?(i=!0,t=o.right):t=o.end;let l=[r||"","fc-toolbar",i?"fc-toolbar-ltr":""];return(0,s.az)("div",{className:l.join(" ")},this.renderSection("start",e||[]),this.renderSection("center",a||[]),this.renderSection("end",t||[]))}renderSection(e,t){let{props:n}=this;return(0,s.az)(ke,{key:e,widgetGroups:t,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})}}class _e extends o.B{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,(0,o.Y)(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:t}=this,{aspectRatio:n}=e,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],i="",o="";return n?null!==t.availableWidth?i=t.availableWidth/n:o=1/n*100+"%":i=e.height||"",(0,s.az)("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:r.join(" "),style:{height:i,paddingBottom:o}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class Re extends o.Z{constructor(e){super(e),this.handleSegClick=(e,t)=>{let{component:n}=this,{context:r}=n,i=(0,o._)(t);if(i&&n.isValidSegDownEl(e.target)){let s=(0,o.$)(e.target,".fc-event-forced-url"),a=s?s.querySelector("a[href]").href:"";r.emitter.trigger("eventClick",{el:t,event:new o.a0(n.context,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:r.viewApi}),a&&!e.defaultPrevented&&(window.location.href=a)}},this.destroy=(0,o.a1)(e.el,"click",".fc-event",this.handleSegClick)}}class Me extends o.Z{constructor(e){super(e),this.handleEventElRemove=e=>{e===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(e,t)=>{(0,o._)(t)&&(this.currentSegEl=t,this.triggerEvent("eventMouseEnter",e,t))},this.handleSegLeave=(e,t)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",e,t))},this.removeHoverListeners=(0,o.a2)(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,t,n){let{component:r}=this,{context:i}=r,s=(0,o._)(n);t&&!r.isValidSegDownEl(t.target)||i.emitter.trigger(e,{el:n,event:new o.a0(i,s.eventRange.def,s.eventRange.instance),jsEvent:t,view:i.viewApi})}}class Ie extends o.a3{constructor(){super(...arguments),this.buildViewContext=(0,o.z)(o.a4),this.buildViewPropTransformers=(0,o.z)(Oe),this.buildToolbarProps=(0,o.z)(Ne),this.headerRef=(0,s.Vf)(),this.footerRef=(0,s.Vf)(),this.interactionsStore={},this.state={viewLabelId:(0,o.a5)()},this.registerInteractiveComponent=(e,t)=>{let n=(0,o.a6)(e,t),r=[Re,Me].concat(this.props.pluginHooks.componentInteractions).map((e=>new e(n)));this.interactionsStore[e.uid]=r,o.a7[e.uid]=n},this.unregisterInteractiveComponent=e=>{let t=this.interactionsStore[e.uid];if(t){for(let e of t)e.destroy();delete this.interactionsStore[e.uid]}delete o.a7[e.uid]},this.resizeRunner=new o.D((()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})})),this.handleWindowResize=e=>{let{options:t}=this.props;t.handleWindowResize&&e.target===window&&this.resizeRunner.request(t.windowResizeDelay)}}render(){let e,{props:t}=this,{toolbarConfig:n,options:r}=t,i=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,(0,o.a8)(t.options.now,t.dateEnv),t.viewTitle),a=!1,l="";t.isHeightAuto||t.forPrint?l="":null!=r.height?a=!0:null!=r.contentHeight?l=r.contentHeight:e=Math.max(r.aspectRatio,.5);let c=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),d=n.header&&n.header.hasTitle?this.state.viewLabelId:void 0;return(0,s.az)(o.V.Provider,{value:c},n.header&&(0,s.az)(Ce,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:d},i)),(0,s.az)(_e,{liquid:a,height:l,aspectRatio:e,labeledById:d},this.renderView(t),this.buildAppendContent()),n.footer&&(0,s.az)(Ce,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},i)))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map((t=>new t(e))),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:t}=e.pluginHooks;for(let n in t)t[n](e[n],e)}componentDidUpdate(e){let{props:t}=this,{propSetHandlers:n}=t.pluginHooks;for(let r in n)t[r]!==e[r]&&n[r](t[r],t)}componentWillUnmount(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(let e of this.calendarInteractions)e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this,t=e.pluginHooks.viewContainerAppends.map((t=>t(e)));return(0,s.az)(s.HY,{},...t)}renderView(e){let{pluginHooks:t}=e,{viewSpec:n}=e,r={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},i=this.buildViewPropTransformers(t.viewPropsTransformers);for(let s of i)Object.assign(r,s.transform(r,e));let o=n.component;return(0,s.az)(o,Object.assign({},r))}}function Ne(e,t,n,r,i,s){let a=n.build(i,void 0,!1),l=n.buildPrev(t,r,!1),c=n.buildNext(t,r,!1);return{title:s,activeButton:e.type,navUnit:e.singleUnit,isTodayEnabled:a.isValid&&!(0,o.H)(t.currentRange,i),isPrevEnabled:l.isValid,isNextEnabled:c.isValid}}function Oe(e){return e.map((e=>new e))}class ze extends o.a9{constructor(e,t={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=e=>{switch(e.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=e=>{this.currentData=e,this.renderRunner.request(e.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:e}=this;(0,o.aa)((()=>{(0,s.sY)((0,s.az)(o.ab,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},((t,n,r,i)=>(this.setClassNames(t),this.setHeight(n),(0,s.az)(o.ac.Provider,{value:this.customContentRenderId},(0,s.az)(Ie,Object.assign({isHeightAuto:r,forPrint:i},e)))))),this.el)}))}else this.isRendered&&(this.isRendered=!1,(0,s.sY)(null,this.el),this.setClassNames([]),this.setHeight(""))},(0,o.ad)(e),this.el=e,this.renderRunner=new o.D(this.handleRenderRequest),new me({optionOverrides:t,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){(0,o.aa)((()=>{super.updateSize()}))}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,t){this.currentDataManager.resetOptions(e,t)}setClassNames(e){if(!(0,o.i)(e,this.currentClassNames)){let{classList:t}=this.el;for(let e of this.currentClassNames)t.remove(e);for(let n of e)t.add(n);this.currentClassNames=e}}setHeight(e){(0,o.ae)(this.el,"height",e)}}r()}catch(l){r(l)}}))},66952:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{$:function(){return E},A:function(){return Fe},B:function(){return Xt},C:function(){return an},D:function(){return b},E:function(){return Rt},F:function(){return hr},G:function(){return xn},H:function(){return yn},I:function(){return Wn},J:function(){return An},K:function(){return rr},L:function(){return cr},M:function(){return Et},N:function(){return ft},O:function(){return pt},P:function(){return mt},Q:function(){return vt},R:function(){return wt},S:function(){return Wt},T:function(){return Vt},U:function(){return Tn},V:function(){return qt},W:function(){return $n},X:function(){return mr},Y:function(){return en},Z:function(){return si},_:function(){return Ir},a:function(){return kt},a0:function(){return Sr},a1:function(){return I},a2:function(){return N},a3:function(){return $t},a4:function(){return Zt},a5:function(){return _},a6:function(){return ai},a7:function(){return ci},a8:function(){return Dn},a9:function(){return di},aA:function(){return En},aE:function(){return hi},aF:function(){return ui},aG:function(){return fi},aH:function(){return gi},aI:function(){return pi},aO:function(){return w},aP:function(){return T},aQ:function(){return A},aR:function(){return k},aV:function(){return Un},aW:function(){return jn},aY:function(){return vi},aa:function(){return Ft},ab:function(){return oi},ac:function(){return sn},ad:function(){return h},ae:function(){return S},aj:function(){return Sn},ak:function(){return Hn},al:function(){return On},ar:function(){return W},as:function(){return V},at:function(){return F},au:function(){return G},av:function(){return J},aw:function(){return U},ax:function(){return j},b:function(){return un},b$:function(){return Lo},b0:function(){return xi},b2:function(){return z},b3:function(){return Ri},b5:function(){return Ni},b6:function(){return Mi},b9:function(){return vn},bA:function(){return ji},bB:function(){return Wi},bC:function(){return Ui},bD:function(){return Yi},bE:function(){return Vi},bF:function(){return Fi},bG:function(){return li},bH:function(){return Qi},bI:function(){return qi},bJ:function(){return $i},bK:function(){return oo},bO:function(){return ao},bP:function(){return Rr},bQ:function(){return jr},bR:function(){return zr},bS:function(){return Ur},bT:function(){return Vr},bU:function(){return Fr},bV:function(){return lo},bW:function(){return co},bX:function(){return Ar},bZ:function(){return ho},b_:function(){return fo},ba:function(){return Oi},bb:function(){return Hi},bc:function(){return Pi},bd:function(){return Li},be:function(){return Bi},bf:function(){return qr},bg:function(){return ve},bl:function(){return ye},bo:function(){return le},bp:function(){return se},bs:function(){return ue},bt:function(){return he},bu:function(){return Ue},bv:function(){return Be},bw:function(){return Le},bx:function(){return je},c:function(){return fe},ca:function(){return zo},cb:function(){return Po},cc:function(){return Ho},cf:function(){return Do},ch:function(){return ro},cj:function(){return Uo},ck:function(){return Vo},cl:function(){return Go},cm:function(){return Yo},cn:function(){return jo},co:function(){return $o},cp:function(){return qo},cq:function(){return Xo},cr:function(){return ns},cs:function(){return os},ct:function(){return dn},cu:function(){return yr},cv:function(){return Er},cw:function(){return u},d:function(){return re},e:function(){return gt},f:function(){return Ct},g:function(){return B},h:function(){return St},i:function(){return ge},j:function(){return tr},k:function(){return X},l:function(){return $r},m:function(){return Tt},n:function(){return xt},o:function(){return pn},p:function(){return er},q:function(){return Te},r:function(){return ti},s:function(){return ae},t:function(){return me},u:function(){return Kr},v:function(){return _t},w:function(){return Cr},x:function(){return ht},y:function(){return De},z:function(){return Ve}});var i=n(16485),o=(n(52247),n(71695),n(92745),n(52805),n(61495),n(61893),n(84283),n(19423),n(23669),n(40251),n(19134),n(5740),n(44495),n(97003),n(32114),n(81804),n(39527),n(99790),n(41360),n(13334),n(34595),n(47021),n(71513),n(75656),n(50100),n(18084),n(65054)),s=n(20378),a=e([i]);i=(a.then?(await a)():a)[0];const c=[],d=new Map;function u(e){c.push(e),d.forEach((t=>{p(t,e)}))}function h(e){e.isConnected&&e.getRootNode&&f(e.getRootNode())}function f(e){let t=d.get(e);if(!t||!t.isConnected){if(t=e.querySelector("style[data-fullcalendar]"),!t){t=document.createElement("style"),t.setAttribute("data-fullcalendar","");const n=v();n&&(t.nonce=n);const r=e===document?document.head:e,i=e===document?r.querySelector("script,link[rel=stylesheet],link[as=style],style"):r.firstChild;r.insertBefore(t,i)}d.set(e,t),g(t)}}function g(e){for(const t of c)p(e,t)}function p(e,t){const{sheet:n}=e,r=n.cssRules.length;t.split("}").forEach(((e,t)=>{(e=e.trim())&&n.insertRule(e+"}",r+t)}))}let m;function v(){return void 0===m&&(m=y()),m}function y(){const e=document.querySelector('meta[name="csp-nonce"]');if(e&&e.hasAttribute("content"))return e.getAttribute("content");const t=document.querySelector("script[nonce]");return t&&t.nonce||""}"undefined"!=typeof document&&f(document);u(':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}');class b{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:t}=this;t[e]=(t[e]||0)+1,this.clearTimeout()}resume(e="",t){let{pauseDepths:n}=this;if(e in n){if(t)delete n[e];else{n[e]-=1,n[e]<=0&&delete n[e]}this.tryDrain()}}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}function w(e){e.parentNode&&e.parentNode.removeChild(e)}function E(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{if(A(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function A(e,t){return(e.matches||e.matchesSelector||e.msMatchesSelector).call(e,t)}function x(e,t){let n=e instanceof HTMLElement?[e]:e,r=[];for(let i=0;i<n.length;i+=1){let e=n[i].querySelectorAll(t);for(let t=0;t<e.length;t+=1)r.push(e[t])}return r}const D=/(top|left|right|bottom|width|height)$/i;function T(e,t){for(let n in t)S(e,n,t[n])}function S(e,t,n){null==n?e.style[t]="":"number"==typeof n&&D.test(t)?e.style[t]=`${n}px`:e.style[t]=n}function k(e){var t,n;return null!==(n=null===(t=e.composedPath)||void 0===t?void 0:t.call(e)[0])&&void 0!==n?n:e.target}let C=0;function _(){return C+=1,"fc-dom-"+C}function R(e){e.preventDefault()}function M(e,t){return n=>{let r=E(n.target,e);r&&t.call(r,n,r)}}function I(e,t,n,r){let i=M(n,r);return e.addEventListener(t,i),()=>{e.removeEventListener(t,i)}}function N(e,t,n,r){let i;return I(e,"mouseover",t,((e,t)=>{if(t!==i){i=t,n(e,t);let o=e=>{i=null,r(e,t),t.removeEventListener("mouseleave",o)};t.addEventListener("mouseleave",o)}}))}const O=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];function z(e,t){let n=r=>{t(r),O.forEach((t=>{e.removeEventListener(t,n)}))};O.forEach((t=>{e.addEventListener(t,n)}))}function H(e){return Object.assign({onClick:e},P(e))}function P(e){return{tabIndex:0,onKeyDown(t){"Enter"!==t.key&&" "!==t.key||(e(t),t.preventDefault())}}}let L=0;function B(){return L+=1,String(L)}function j(){document.body.classList.add("fc-not-allowed")}function U(){document.body.classList.remove("fc-not-allowed")}function W(e){e.style.userSelect="none",e.style.webkitUserSelect="none",e.addEventListener("selectstart",R)}function V(e){e.style.userSelect="",e.style.webkitUserSelect="",e.removeEventListener("selectstart",R)}function F(e){e.addEventListener("contextmenu",R)}function G(e){e.removeEventListener("contextmenu",R)}function Y(e){let t,n,r=[],i=[];for("string"==typeof e?i=e.split(/\s*,\s*/):"function"==typeof e?i=[e]:Array.isArray(e)&&(i=e),t=0;t<i.length;t+=1)n=i[t],"string"==typeof n?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r}function Q(e,t,n){let r,i;for(r=0;r<n.length;r+=1)if(i=q(e,t,n[r]),i)return i;return 0}function q(e,t,n){return n.func?n.func(e,t):Z(e[n.field],t[n.field])*(n.order||1)}function Z(e,t){return e||t?null==t?-1:null==e?1:"string"==typeof e||"string"==typeof t?String(e).localeCompare(String(t)):e-t:0}function $(e,t){let n=String(e);return"000".substr(0,t-n.length)+n}function X(e,t,n){return"function"==typeof e?e(...t):"string"==typeof e?t.reduce(((e,t,n)=>e.replace("$"+n,t||"")),e):n}function J(e,t){return e-t}function K(e){return e%1==0}function ee(e){let t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}const te=["years","months","days","milliseconds"],ne=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function re(e,t){return"string"==typeof e?ie(e):"object"==typeof e&&e?oe(e):"number"==typeof e?oe({[t||"milliseconds"]:e}):null}function ie(e){let t=ne.exec(e);if(t){let e=t[1]?-1:1;return{years:0,months:0,days:e*(t[2]?parseInt(t[2],10):0),milliseconds:e*(60*(t[3]?parseInt(t[3],10):0)*60*1e3+60*(t[4]?parseInt(t[4],10):0)*1e3+1e3*(t[5]?parseInt(t[5],10):0)+(t[6]?parseInt(t[6],10):0))}}return null}function oe(e){let t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:60*(e.hours||e.hour||0)*60*1e3+60*(e.minutes||e.minute||0)*1e3+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function se(e,t){return{years:e.years+t.years,months:e.months+t.months,days:e.days+t.days,milliseconds:e.milliseconds+t.milliseconds}}function ae(e,t){return{years:e.years-t.years,months:e.months-t.months,days:e.days-t.days,milliseconds:e.milliseconds-t.milliseconds}}function le(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}}function ce(e){return de(e)/30}function de(e){return ue(e)/864e5}function ue(e){return 31536e6*e.years+2592e6*e.months+864e5*e.days+e.milliseconds}function he(e,t){let n=null;for(let r=0;r<te.length;r+=1){let i=te[r];if(t[i]){let r=e[i]/t[i];if(!K(r)||null!==n&&n!==r)return null;n=r}else if(e[i])return null}return n}function fe(e){let t=e.milliseconds;if(t){if(t%1e3!=0)return{unit:"millisecond",value:t};if(t%6e4!=0)return{unit:"second",value:t/1e3};if(t%36e5!=0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7==0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function ge(e,t,n){if(e===t)return!0;let r,i=e.length;if(i!==t.length)return!1;for(r=0;r<i;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}const pe=["sun","mon","tue","wed","thu","fri","sat"];function me(e,t){let n=Oe(e);return n[2]+=t,ze(n)}function ve(e,t){let n=Oe(e);return n[6]+=t,ze(n)}function ye(e,t){return(t.valueOf()-e.valueOf())/864e5}function be(e,t){return(t.valueOf()-e.valueOf())/36e5}function we(e,t){return(t.valueOf()-e.valueOf())/6e4}function Ee(e,t){return(t.valueOf()-e.valueOf())/1e3}function Ae(e,t){let n=Te(e),r=Te(t);return{years:0,months:0,days:Math.round(ye(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}function xe(e,t){let n=De(e,t);return null!==n&&n%7==0?n/7:null}function De(e,t){return Pe(e)===Pe(t)?Math.round(ye(e,t)):null}function Te(e){return ze([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function Se(e){return ze([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()])}function ke(e){return ze([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()])}function Ce(e){return ze([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()])}function _e(e,t,n){let r=e.getUTCFullYear(),i=Re(e,r,t,n);if(i<1)return Re(e,r-1,t,n);let o=Re(e,r+1,t,n);return o>=1?Math.min(i,o):i}function Re(e,t,n,r){let i=ze([t,0,1+Me(t,n,r)]),o=Te(e),s=Math.round(ye(i,o));return Math.floor(s/7)+1}function Me(e,t,n){let r=7+t-n;return-((7+ze([e,0,r]).getUTCDay()-t)%7)+r-1}function Ie(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function Ne(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function Oe(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function ze(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC(...e))}function He(e){return!isNaN(e.valueOf())}function Pe(e){return 1e3*e.getUTCHours()*60*60+1e3*e.getUTCMinutes()*60+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function Le(e,t,n=!1){let r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",We(t,!0)))),r}function Be(e){return e.toISOString().replace(/T.*$/,"")}function je(e){return e.toISOString().match(/^\d{4}-\d{2}/)[0]}function Ue(e){return $(e.getUTCHours(),2)+":"+$(e.getUTCMinutes(),2)+":"+$(e.getUTCSeconds(),2)}function We(e,t=!1){let n=e<0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),o=Math.round(r%60);return t?`${n+$(i,2)}:${$(o,2)}`:`GMT${n}${i}${o?`:${$(o,2)}`:""}`}function Ve(e,t,n){let r,i;return function(...o){if(r){if(!ge(r,o)){n&&n(i);let r=e.apply(this,o);t&&t(r,i)||(i=r)}}else i=e.apply(this,o);return r=o,i}}function Fe(e,t,n){let r,i;return o=>{if(r){if(!Rt(r,o)){n&&n(i);let r=e.call(this,o);t&&t(r,i)||(i=r)}}else i=e.call(this,o);return r=o,i}}const Ge={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},Ye={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Qe=/\s*([ap])\.?m\.?/i,qe=/,/g,Ze=/\s+/g,$e=/\u200e/g,Xe=/UTC|GMT/;class Je{constructor(e){let t={},n={},r=0;for(let i in e)i in Ge?(n[i]=e[i],r=Math.max(Ge[i],r)):(t[i]=e[i],i in Ye&&(r=Math.max(Ye[i],r)));this.standardDateProps=t,this.extendedSettings=n,this.severity=r,this.buildFormattingFunc=Ve(Ke)}format(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)}formatRange(e,t,n,r){let{standardDateProps:i,extendedSettings:o}=this,s=ot(e.marker,t.marker,n.calendarSystem);if(!s)return this.format(e,n);let a=s;!(a>1)||"numeric"!==i.year&&"2-digit"!==i.year||"numeric"!==i.month&&"2-digit"!==i.month||"numeric"!==i.day&&"2-digit"!==i.day||(a=1);let l=this.format(e,n),c=this.format(t,n);if(l===c)return l;let d=Ke(st(i,a),o,n),u=d(e),h=d(t),f=at(l,u,c,h),g=o.separator||r||n.defaultSeparator||"";return f?f.before+u+g+h+f.after:l+g+c}getLargestUnit(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function Ke(e,t,n){let r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?e=>We(e.timeZoneOffset):0===r&&t.week?e=>it(n.computeWeekNumber(e.marker),n.weekText,n.weekTextLong,n.locale,t.week):et(e,t,n)}function et(e,t,n){e=Object.assign({},e),t=Object.assign({},t),tt(e,t),e.timeZone="UTC";let r,i=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){let t=Object.assign({},e);delete t.minute,r=new Intl.DateTimeFormat(n.locale.codes,t)}return o=>{let s,{marker:a}=o;return s=r&&!a.getUTCMinutes()?r:i,nt(s.format(a),o,e,t,n)}}function tt(e,t){e.timeZoneName&&(e.hour||(e.hour="2-digit"),e.minute||(e.minute="2-digit")),"long"===e.timeZoneName&&(e.timeZoneName="short"),t.omitZeroMinute&&(e.second||e.millisecond)&&delete t.omitZeroMinute}function nt(e,t,n,r,i){return e=e.replace($e,""),"short"===n.timeZoneName&&(e=rt(e,"UTC"===i.timeZone||null==t.timeZoneOffset?"UTC":We(t.timeZoneOffset))),r.omitCommas&&(e=e.replace(qe,"").trim()),r.omitZeroMinute&&(e=e.replace(":00","")),!1===r.meridiem?e=e.replace(Qe,"").trim():"narrow"===r.meridiem?e=e.replace(Qe,((e,t)=>t.toLocaleLowerCase())):"short"===r.meridiem?e=e.replace(Qe,((e,t)=>`${t.toLocaleLowerCase()}m`)):"lowercase"===r.meridiem&&(e=e.replace(Qe,(e=>e.toLocaleLowerCase()))),e=(e=e.replace(Ze," ")).trim()}function rt(e,t){let n=!1;return e=e.replace(Xe,(()=>(n=!0,t))),n||(e+=` ${t}`),e}function it(e,t,n,r,i){let o=[];return"long"===i?o.push(n):"short"!==i&&"narrow"!==i||o.push(t),"long"!==i&&"short"!==i||o.push(" "),o.push(r.simpleNumberFormat.format(e)),"rtl"===r.options.direction&&o.reverse(),o.join("")}function ot(e,t,n){return n.getMarkerYear(e)!==n.getMarkerYear(t)?5:n.getMarkerMonth(e)!==n.getMarkerMonth(t)?4:n.getMarkerDay(e)!==n.getMarkerDay(t)?2:Pe(e)!==Pe(t)?1:0}function st(e,t){let n={};for(let r in e)(!(r in Ye)||Ye[r]<=t)&&(n[r]=e[r]);return n}function at(e,t,n,r){let i=0;for(;i<e.length;){let o=e.indexOf(t,i);if(-1===o)break;let s=e.substr(0,o);i=o+t.length;let a=e.substr(i),l=0;for(;l<n.length;){let e=n.indexOf(r,l);if(-1===e)break;let t=n.substr(0,e);l=e+r.length;let i=n.substr(l);if(s===t&&a===i)return{before:s,after:a}}}return null}function lt(e,t){let n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function ct(e,t,n,r){let i=lt(e,n.calendarSystem);return{date:i,start:i,end:t?lt(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}class dt{constructor(e){this.cmdStr=e}format(e,t,n){return t.cmdFormatter(this.cmdStr,ct(e,null,t,n))}formatRange(e,t,n,r){return n.cmdFormatter(this.cmdStr,ct(e,t,n,r))}}class ut{constructor(e){this.func=e}format(e,t,n){return this.func(ct(e,null,t,n))}formatRange(e,t,n,r){return this.func(ct(e,t,n,r))}}function ht(e){return"object"==typeof e&&e?new Je(e):"string"==typeof e?new dt(e):"function"==typeof e?new ut(e):null}const ft={navLinkDayClick:xt,navLinkWeekClick:xt,duration:re,bootstrapFontAwesome:xt,buttonIcons:xt,customButtons:xt,defaultAllDayEventDuration:re,defaultTimedEventDuration:re,nextDayThreshold:re,scrollTime:re,scrollTimeReset:Boolean,slotMinTime:re,slotMaxTime:re,dayPopoverFormat:ht,slotDuration:re,snapDuration:re,headerToolbar:xt,footerToolbar:xt,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:ht,dayHeaderClassNames:xt,dayHeaderContent:xt,dayHeaderDidMount:xt,dayHeaderWillUnmount:xt,dayCellClassNames:xt,dayCellContent:xt,dayCellDidMount:xt,dayCellWillUnmount:xt,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:xt,weekNumbers:Boolean,weekNumberClassNames:xt,weekNumberContent:xt,weekNumberDidMount:xt,weekNumberWillUnmount:xt,editable:Boolean,viewClassNames:xt,viewDidMount:xt,viewWillUnmount:xt,nowIndicator:Boolean,nowIndicatorClassNames:xt,nowIndicatorContent:xt,nowIndicatorDidMount:xt,nowIndicatorWillUnmount:xt,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:xt,locale:xt,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:xt,eventOrder:Y,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:xt,contentHeight:xt,direction:String,weekNumberFormat:ht,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:xt,initialDate:xt,now:xt,eventDataTransform:xt,stickyHeaderDates:xt,stickyFooterScrollbar:xt,viewHeight:xt,defaultAllDay:Boolean,eventSourceFailure:xt,eventSourceSuccess:xt,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:xt,eventConstraint:xt,eventAllow:xt,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:xt,eventContent:xt,eventDidMount:xt,eventWillUnmount:xt,selectConstraint:xt,selectOverlap:xt,selectAllow:xt,droppable:Boolean,unselectCancel:String,slotLabelFormat:xt,slotLaneClassNames:xt,slotLaneContent:xt,slotLaneDidMount:xt,slotLaneWillUnmount:xt,slotLabelClassNames:xt,slotLabelContent:xt,slotLabelDidMount:xt,slotLabelWillUnmount:xt,dayMaxEvents:xt,dayMaxEventRows:xt,dayMinWidth:Number,slotLabelInterval:re,allDayText:String,allDayClassNames:xt,allDayContent:xt,allDayDidMount:xt,allDayWillUnmount:xt,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:ht,rerenderDelay:Number,moreLinkText:xt,moreLinkHint:xt,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:xt,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:re,hiddenDays:xt,fixedWeekCount:Boolean,validRange:xt,visibleRange:xt,titleFormat:xt,eventInteractive:Boolean,noEventsText:String,viewHint:xt,navLinkHint:xt,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:xt,moreLinkClassNames:xt,moreLinkContent:xt,moreLinkDidMount:xt,moreLinkWillUnmount:xt,monthStartFormat:ht,handleCustomRendering:xt,customRenderingMetaMap:xt,customRenderingReplaces:Boolean},gt={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},pt={datesSet:xt,eventsSet:xt,eventAdd:xt,eventChange:xt,eventRemove:xt,windowResize:xt,eventClick:xt,eventMouseEnter:xt,eventMouseLeave:xt,select:xt,unselect:xt,loading:xt,_unmount:xt,_beforeprint:xt,_afterprint:xt,_noEventDrop:xt,_noEventResize:xt,_resize:xt,_scrollRequest:xt},mt={buttonText:xt,buttonHints:xt,views:xt,plugins:xt,initialEvents:xt,events:xt,eventSources:xt},vt={headerToolbar:yt,footerToolbar:yt,buttonText:yt,buttonHints:yt,buttonIcons:yt,dateIncrement:yt,plugins:bt,events:bt,eventSources:bt,resources:bt};function yt(e,t){return"object"==typeof e&&"object"==typeof t&&e&&t?Rt(e,t):e===t}function bt(e,t){return Array.isArray(e)&&Array.isArray(t)?ge(e,t):e===t}const wt={type:String,component:xt,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:xt,usesMinMaxTime:Boolean,classNames:xt,content:xt,didMount:xt,willUnmount:xt};function Et(e){return Tt(e,vt)}function At(e,t){let n={},r={};for(let i in t)i in e&&(n[i]=t[i](e[i]));for(let i in e)i in t||(r[i]=e[i]);return{refined:n,extra:r}}function xt(e){return e}const{hasOwnProperty:Dt}=Object.prototype;function Tt(e,t){let n={};if(t)for(let r in t)if(t[r]===yt){let t=[];for(let i=e.length-1;i>=0;i-=1){let o=e[i][r];if("object"==typeof o&&o)t.unshift(o);else if(void 0!==o){n[r]=o;break}}t.length&&(n[r]=Tt(t))}for(let r=e.length-1;r>=0;r-=1){let t=e[r];for(let e in t)e in n||(n[e]=t[e])}return n}function St(e,t){let n={};for(let r in e)t(e[r],r)&&(n[r]=e[r]);return n}function kt(e,t){let n={};for(let r in e)n[r]=t(e[r],r);return n}function Ct(e){let t={};for(let n of e)t[n]=!0;return t}function _t(e){let t=[];for(let n in e)t.push(e[n]);return t}function Rt(e,t){if(e===t)return!0;for(let n in e)if(Dt.call(e,n)&&!(n in t))return!1;for(let n in t)if(Dt.call(t,n)&&e[n]!==t[n])return!1;return!0}const Mt=/^on[A-Z]/;function It(e,t){const n=Nt(e,t);for(let r of n)if(!Mt.test(r))return!1;return!0}function Nt(e,t){let n=[];for(let r in e)Dt.call(e,r)&&(r in t||n.push(r));for(let r in t)Dt.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}function Ot(e,t,n={}){if(e===t)return!0;for(let r in t)if(!(r in e)||!zt(e[r],t[r],n[r]))return!1;for(let r in e)if(!(r in t))return!1;return!0}function zt(e,t,n){return e===t||!0===n||!!n&&n(e,t)}function Ht(e,t=0,n,r=1){let i=[];null==n&&(n=Object.keys(e).length);for(let o=t;o<n;o+=r){let t=e[o];void 0!==t&&i.push(t)}return i}let Pt={};function Lt(e,t){Pt[e]=t}class Bt{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return ze(e)}markerToArray(e){return Oe(e)}}Lt("gregory",Bt);const jt=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function Ut(e){let t=jt.exec(e);if(t){let e=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number(`0.${t[12]}`):0));if(He(e)){let n=null;return t[13]&&(n=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:e,isTimeUnspecified:!t[6],timeZoneOffset:n}}}return null}class Wt{constructor(e){let t=this.timeZone=e.timeZone,n="local"!==t&&"UTC"!==t;var r;e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(t)),this.canComputeOffset=Boolean(!n||this.namedTimeZoneImpl),this.calendarSystem=(r=e.calendarSystem,new Pt[r]),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.weekTextLong=(null!=e.weekTextLong?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let t=this.createMarkerMeta(e);return null===t?null:t.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker((new Date).valueOf()):ze(Ie(new Date))}createMarkerMeta(e){if("string"==typeof e)return this.parse(e);let t=null;return"number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(t=this.timestampToMarker(e))):Array.isArray(e)&&(t=ze(e)),null!==t&&He(t)?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null}parse(e){let t=Ut(e);if(null===t)return null;let{marker:n}=t,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-60*t.timeZoneOffset*1e3):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)}subtract(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)}addYears(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)}addMonths(e,t){let n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)}diffWholeYears(e,t){let{calendarSystem:n}=this;return Pe(e)===Pe(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null}diffWholeMonths(e,t){let{calendarSystem:n}=this;return Pe(e)===Pe(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+12*(n.getMarkerYear(t)-n.getMarkerYear(e)):null}greatestWholeUnit(e,t){let n=this.diffWholeYears(e,t);return null!==n?{unit:"year",value:n}:(n=this.diffWholeMonths(e,t),null!==n?{unit:"month",value:n}:(n=xe(e,t),null!==n?{unit:"week",value:n}:(n=De(e,t),null!==n?{unit:"day",value:n}:(n=be(e,t),K(n)?{unit:"hour",value:n}:(n=we(e,t),K(n)?{unit:"minute",value:n}:(n=Ee(e,t),K(n)?{unit:"second",value:n}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}))))))}countDurationsBetween(e,t,n){let r;return n.years&&(r=this.diffWholeYears(e,t),null!==r)?r/(de(n)/365):n.months&&(r=this.diffWholeMonths(e,t),null!==r)?r/ce(n):n.days&&(r=De(e,t),null!==r)?r/de(n):(t.valueOf()-e.valueOf())/ue(n)}startOf(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?Te(e):"hour"===t?Se(e):"minute"===t?ke(e):"second"===t?Ce(e):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):_e(e,this.weekDow,this.weekDoy)}format(e,t,n={}){return t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,t,n,r={}){return r.isEndExclusive&&(t=ve(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)}formatIso(e,t={}){let n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),Le(e,n,t.omitTime)}timestampToMarker(e){return"local"===this.timeZone?ze(Ie(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?ze(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)}offsetForMarker(e){return"local"===this.timeZone?-Ne(Oe(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(Oe(e)):null}toDate(e,t){return"local"===this.timeZone?Ne(Oe(e)):"UTC"===this.timeZone?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-1e3*this.namedTimeZoneImpl.offsetForArray(Oe(e))*60):new Date(e.valueOf()-(t||0))}}class Vt{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let t,n;if("object"==typeof e&&e){for(n in t=Object.assign({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})}applyIconOverridePrefix(e){let t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,t){let n;return n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e],n?`${this.baseIconClass} ${n}`:""}getCustomButtonIconClass(e){let t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption],t)?`${this.baseIconClass} ${this.applyIconOverridePrefix(t)}`:""}}function Ft(e){e();let t=o.YM.debounceRendering,n=[];for(o.YM.debounceRendering=function(e){n.push(e)},o.sY(o.az(Gt,{}),document.createElement("div"));n.length;)n.shift()();o.YM.debounceRendering=t}Vt.prototype.classes={},Vt.prototype.iconClasses={},Vt.prototype.baseIconClass="",Vt.prototype.iconOverridePrefix="";class Gt extends o.wA{render(){return o.az("div",{})}componentDidMount(){this.setState({})}}function Yt(e){let t=o.kr(e),n=t.Provider;return t.Provider=function(){let e=!this.getChildContext,t=n.apply(this,arguments);if(e){let e=[];this.shouldComponentUpdate=t=>{this.props.value!==t.value&&e.forEach((e=>{e.context=t.value,e.forceUpdate()}))},this.sub=t=>{e.push(t);let n=t.componentWillUnmount;t.componentWillUnmount=()=>{e.splice(e.indexOf(t),1),n&&n.call(t)}}}return t},t}class Qt{constructor(e,t,n,r){this.execFunc=e,this.emitter=t,this.scrollTime=n,this.scrollTimeReset=r,this.handleScrollRequest=e=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},e),this.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}const qt=Yt({});function Zt(e,t,n,r,i,o,s,a,l,c,d,u,h){return{dateEnv:i,options:n,pluginHooks:s,emitter:c,dispatch:a,getCurrentData:l,calendarApi:d,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:o,isRtl:"rtl"===n.direction,addResizeHandler(e){c.on("_resize",e)},removeResizeHandler(e){c.off("_resize",e)},createScrollResponder(e){return new Qt(e,c,re(n.scrollTime),n.scrollTimeReset)},registerInteractiveComponent:u,unregisterInteractiveComponent:h}}class $t extends o.wA{shouldComponentUpdate(e,t){return this.debug&&console.log(Nt(e,this.props),Nt(t,this.state)),!Ot(this.props,e,this.propEquality)||!Ot(this.state,t,this.stateEquality)}safeSetState(e){Ot(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}$t.addPropsEquality=Jt,$t.addStateEquality=Kt,$t.contextType=qt,$t.prototype.propEquality={},$t.prototype.stateEquality={};class Xt extends $t{}function Jt(e){let t=Object.create(this.prototype.propEquality);Object.assign(t,e),this.prototype.propEquality=t}function Kt(e){let t=Object.create(this.prototype.stateEquality);Object.assign(t,e),this.prototype.stateEquality=t}function en(e,t){"function"==typeof e?e(t):e&&(e.current=t)}Xt.contextType=qt;class tn extends Xt{constructor(){super(...arguments),this.id=B(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{const{options:t}=this.context,{generatorName:n}=this.props;t.customRenderingReplaces&&nn(n,t)||this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&en(this.props.elRef,e)}}render(){const{props:e,context:t}=this,{options:n}=t,{customGenerator:r,defaultGenerator:i,renderProps:s}=e,a=rn(e,[],this.handleEl);let l,c,d=!1,u=[];if(null!=r){const e="function"==typeof r?r(s,o.az):r;if(!0===e)d=!0;else{const t=e&&"object"==typeof e;t&&"html"in e?a.dangerouslySetInnerHTML={__html:e.html}:t&&"domNodes"in e?u=Array.prototype.slice.call(e.domNodes):(t?(0,o.l$)(e):"function"!=typeof e)?l=e:c=e}}else d=!nn(e.generatorName,n);return d&&i&&(l=i(s)),this.queuedDomNodes=u,this.currentGeneratorMeta=c,(0,o.az)(e.elTag,a,l)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var t;const{props:n,context:r}=this,{handleCustomRendering:i,customRenderingMetaMap:o}=r.options;if(i){const r=null!==(t=this.currentGeneratorMeta)&&void 0!==t?t:null==o?void 0:o[n.generatorName];r&&i(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:r},n),{elClasses:(n.elClasses||[]).filter(on)}))}}applyQueueudDomNodes(){const{queuedDomNodes:e,currentDomNodes:t}=this,n=this.base;if(!ge(e,t)){t.forEach(w);for(let t of e)n.appendChild(t);this.currentDomNodes=e}}}function nn(e,t){var n;return Boolean(t.handleCustomRendering&&e&&(null===(n=t.customRenderingMetaMap)||void 0===n?void 0:n[e]))}function rn(e,t,n){const r=Object.assign(Object.assign({},e.elAttrs),{ref:n});return(e.elClasses||t)&&(r.className=(e.elClasses||[]).concat(t||[]).concat(r.className||[]).filter(Boolean).join(" ")),e.elStyle&&(r.style=e.elStyle),r}function on(e){return Boolean(e)}tn.addPropsEquality({elClasses:ge,elStyle:Rt,elAttrs:It,renderProps:Rt});const sn=Yt(0);class an extends o.wA{constructor(){super(...arguments),this.InnerContent=ln.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(en(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){const{props:e}=this,t=cn(e.classNameGenerator,e.renderProps);if(e.children){const n=rn(e,t,this.handleEl),r=e.children(this.InnerContent,e.renderProps,n);return e.elTag?(0,o.az)(e.elTag,n,r):r}return(0,o.az)(tn,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(t),renderId:this.context}))}componentDidMount(){var e,t;this.el?null===(t=(e=this.props).didMount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,t;null===(t=(e=this.props).willUnmount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}function ln(e,t){const n=e.props;return(0,o.az)(tn,Object.assign({renderProps:n.renderProps,generatorName:n.generatorName,customGenerator:n.customGenerator,defaultGenerator:n.defaultGenerator,renderId:e.context},t))}function cn(e,t){const n="function"==typeof e?e(t):e||[];return"string"==typeof n?[n]:n}an.contextType=sn;class dn extends Xt{render(){let{props:e,context:t}=this,{options:n}=t,r={view:t.viewApi};return(0,o.az)(an,Object.assign({},e,{elTag:e.elTag||"div",elClasses:[...un(e.viewSpec),...e.elClasses||[]],renderProps:r,classNameGenerator:n.viewClassNames,generatorName:void 0,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount}),(()=>e.children))}}function un(e){return[`fc-${e.type}-view`,"fc-view"]}function hn(e,t){let n=null,r=null;return e.start&&(n=t.createMarker(e.start)),e.end&&(r=t.createMarker(e.end)),n||r?n&&r&&r<n?null:{start:n,end:r}:null}function fn(e,t){let n,r,i=[],{start:o}=t;for(e.sort(gn),n=0;n<e.length;n+=1)r=e[n],r.start>o&&i.push({start:o,end:r.start}),r.end>o&&(o=r.end);return o<t.end&&i.push({start:o,end:t.end}),i}function gn(e,t){return e.start.valueOf()-t.start.valueOf()}function pn(e,t){let{start:n,end:r}=e,i=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(i={start:n,end:r}),i}function mn(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function vn(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function yn(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function bn(e){let t=Math.floor(ye(e.start,e.end))||1,n=Te(e.start);return{start:n,end:me(n,t)}}function wn(e,t=re(0)){let n=null,r=null;if(e.end){r=Te(e.end);let n=e.end.valueOf()-r.valueOf();n&&n>=ue(t)&&(r=me(r,1))}return e.start&&(n=Te(e.start),r&&r<=n&&(r=me(n,1))),{start:n,end:r}}function En(e,t,n,r){return"year"===r?re(n.diffWholeYears(e,t),"year"):"month"===r?re(n.diffWholeMonths(e,t),"month"):Ae(e,t)}function An(e,t){return"CHANGE_DATE"===t.type?t.dateMarker:e}function xn(e,t){let n=e.initialDate;return null!=n?t.createMarker(n):Dn(e.now,t)}function Dn(e,t){return"function"==typeof e&&(e=e()),null==e?t.createNowMarker():t.createMarker(e)}class Tn{constructor(e){this.props=e,this.nowDate=Dn(e.nowInput,e.dateEnv),this.initHiddenDays()}buildPrev(e,t,n){let{dateEnv:r}=this.props,i=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,-1,n)}buildNext(e,t,n){let{dateEnv:r}=this.props,i=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,1,n)}build(e,t,n=!0){let r,i,o,s,a,l,{props:c}=this;var d,u;return r=this.buildValidRange(),r=this.trimHiddenDays(r),n&&(d=e,e=null!=(u=r).start&&d<u.start?u.start:null!=u.end&&d>=u.end?new Date(u.end.valueOf()-1):d),i=this.buildCurrentRangeInfo(e,t),o=/^(year|month|week|day)$/.test(i.unit),s=this.buildRenderRange(this.trimHiddenDays(i.range),i.unit,o),s=this.trimHiddenDays(s),a=s,c.showNonCurrentDates||(a=pn(a,i.range)),a=this.adjustActiveRange(a),a=pn(a,r),l=mn(i.range,r),yn(s,e)||(e=s.start),{currentDate:e,validRange:r,currentRange:i.range,currentRangeUnit:i.unit,isRangeAllDay:o,activeRange:a,renderRange:s,slotMinTime:c.slotMinTime,slotMaxTime:c.slotMaxTime,isValid:l,dateIncrement:this.buildDateIncrement(i.duration)}}buildValidRange(){let e=this.props.validRangeInput,t="function"==typeof e?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(t)||{start:null,end:null}}buildCurrentRangeInfo(e,t){let n,{props:r}=this,i=null,o=null,s=null;return r.duration?(i=r.duration,o=r.durationUnit,s=this.buildRangeFromDuration(e,t,i,o)):(n=this.props.dayCount)?(o="day",s=this.buildRangeFromDayCount(e,t,n)):(s=this.buildCustomVisibleRange(e))?o=r.dateEnv.greatestWholeUnit(s.start,s.end).unit:(i=this.getFallbackDuration(),o=fe(i).unit,s=this.buildRangeFromDuration(e,t,i,o)),{duration:i,unit:o,range:s}}getFallbackDuration(){return re({day:1})}adjustActiveRange(e){let{dateEnv:t,usesMinMaxTime:n,slotMinTime:r,slotMaxTime:i}=this.props,{start:o,end:s}=e;return n&&(de(r)<0&&(o=Te(o),o=t.add(o,r)),de(i)>1&&(s=Te(s),s=me(s,-1),s=t.add(s,i))),{start:o,end:s}}buildRangeFromDuration(e,t,n,r){let i,o,s,{dateEnv:a,dateAlignment:l}=this.props;if(!l){let{dateIncrement:e}=this.props;l=e&&ue(e)<ue(n)?fe(e).unit:r}function c(){i=a.startOf(e,l),o=a.add(i,n),s={start:i,end:o}}return de(n)<=1&&this.isHiddenDay(i)&&(i=this.skipHiddenDays(i,t),i=Te(i)),c(),this.trimHiddenDays(s)||(e=this.skipHiddenDays(e,t),c()),s}buildRangeFromDayCount(e,t,n){let r,{dateEnv:i,dateAlignment:o}=this.props,s=0,a=e;o&&(a=i.startOf(a,o)),a=Te(a),a=this.skipHiddenDays(a,t),r=a;do{r=me(r,1),this.isHiddenDay(r)||(s+=1)}while(s<n);return{start:a,end:r}}buildCustomVisibleRange(e){let{props:t}=this,n=t.visibleRangeInput,r="function"==typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,i=this.refineRange(r);return!i||null!=i.start&&null!=i.end?i:null}buildRenderRange(e,t,n){return e}buildDateIncrement(e){let t,{dateIncrement:n}=this.props;return n||((t=this.props.dateAlignment)?re(1,t):e||re({days:1}))}refineRange(e){if(e){let t=hn(e,this.props.dateEnv);return t&&(t=wn(t)),t}return null}initHiddenDays(){let e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n}trimHiddenDays(e){let{start:t,end:n}=e;return t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n?{start:t,end:n}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,t=1,n=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=me(e,t);return e}}function Sn(e,t,n,r){return{instanceId:B(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}function kn(e,t,n,r){for(let i=0;i<r.length;i+=1){let o=r[i].parse(e,n);if(o){let{allDay:n}=e;return null==n&&(n=t,null==n&&(n=o.allDayGuess,null==n&&(n=!1))),{allDay:n,duration:o.duration,typeData:o.typeData,typeId:i}}}return null}function Cn(e,t,n){let{dateEnv:r,pluginHooks:i,options:o}=n,{defs:s,instances:a}=e;a=St(a,(e=>!s[e.defId].recurringDef));for(let l in s){let e=s[l];if(e.recurringDef){let{duration:n}=e.recurringDef;n||(n=e.allDay?o.defaultAllDayEventDuration:o.defaultTimedEventDuration);let s=_n(e,n,t,r,i.recurringTypes);for(let e of s){let t=Sn(l,{start:e,end:r.add(e,n)});a[t.instanceId]=t}}}return{defs:s,instances:a}}function _n(e,t,n,r,i){let o=i[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(o=o.map(Te)),o}const Rn={id:String,groupId:String,title:String,url:String,interactive:Boolean},Mn={start:xt,end:xt,date:xt,allDay:Boolean},In=Object.assign(Object.assign(Object.assign({},Rn),Mn),{extendedProps:xt});function Nn(e,t,n,r,i=zn(n),o,s){let{refined:a,extra:l}=On(e,n,i),c=Ln(t,n),d=kn(a,c,n.dateEnv,n.pluginHooks.recurringTypes);if(d){let e=Hn(a,l,t?t.sourceId:"",d.allDay,Boolean(d.duration),n,o);return e.recurringDef={typeId:d.typeId,typeData:d.typeData,duration:d.duration},{def:e,instance:null}}let u=Pn(a,c,n,r);if(u){let e=Hn(a,l,t?t.sourceId:"",u.allDay,u.hasEnd,n,o),r=Sn(e.defId,u.range,u.forcedStartTzo,u.forcedEndTzo);return s&&e.publicId&&s[e.publicId]&&(r.instanceId=s[e.publicId]),{def:e,instance:r}}return null}function On(e,t,n=zn(t)){return At(e,n)}function zn(e){return Object.assign(Object.assign(Object.assign({},qn),In),e.pluginHooks.eventRefiners)}function Hn(e,t,n,r,i,o,s){let a={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:(s&&e.id?s[e.id]:"")||B(),sourceId:n,allDay:r,hasEnd:i,interactive:e.interactive,ui:$n(e,o),extendedProps:Object.assign(Object.assign({},e.extendedProps||{}),t)};for(let l of o.pluginHooks.eventDefMemberAdders)Object.assign(a,l(e));return Object.freeze(a.ui.classNames),Object.freeze(a.extendedProps),a}function Pn(e,t,n,r){let i,o,{allDay:s}=e,a=null,l=!1,c=null,d=null!=e.start?e.start:e.date;if(i=n.dateEnv.createMarkerMeta(d),i)a=i.marker;else if(!r)return null;return null!=e.end&&(o=n.dateEnv.createMarkerMeta(e.end)),null==s&&(s=null!=t?t:(!i||i.isTimeUnspecified)&&(!o||o.isTimeUnspecified)),s&&a&&(a=Te(a)),o&&(c=o.marker,s&&(c=Te(c)),a&&c<=a&&(c=null)),c?l=!0:r||(l=n.options.forceEventDuration||!1,c=n.dateEnv.add(a,s?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration)),{allDay:s,hasEnd:l,range:{start:a,end:c},forcedStartTzo:i?i.forcedTzo:null,forcedEndTzo:o?o.forcedTzo:null}}function Ln(e,t){let n=null;return e&&(n=e.defaultAllDay),null==n&&(n=t.options.defaultAllDay),n}function Bn(e,t,n,r,i,o){let s={defs:{},instances:{}},a=zn(n);for(let l of e){let e=Nn(l,t,n,r,a,i,o);e&&jn(e,s)}return s}function jn(e,t={defs:{},instances:{}}){return t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function Un(e,t){let n=e.instances[t];if(n){let t=e.defs[n.defId],r=Fn(e,(e=>{return n=t,r=e,Boolean(n.groupId&&n.groupId===r.groupId);var n,r}));return r.defs[t.defId]=t,r.instances[n.instanceId]=n,r}return{defs:{},instances:{}}}function Wn(){return{defs:{},instances:{}}}function Vn(e,t){return{defs:Object.assign(Object.assign({},e.defs),t.defs),instances:Object.assign(Object.assign({},e.instances),t.instances)}}function Fn(e,t){let n=St(e.defs,t),r=St(e.instances,(e=>n[e.defId]));return{defs:n,instances:r}}function Gn(e,t){let{defs:n,instances:r}=e,i={},o={};for(let s in n)t.defs[s]||(i[s]=n[s]);for(let s in r)!t.instances[s]&&i[r[s].defId]&&(o[s]=r[s]);return{defs:i,instances:o}}function Yn(e,t){return Array.isArray(e)?Bn(e,null,t,!0):"object"==typeof e&&e?Bn([e],null,t,!0):null!=e?String(e):null}function Qn(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}const qn={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:xt,overlap:xt,allow:xt,className:Qn,classNames:Qn,color:String,backgroundColor:String,borderColor:String,textColor:String},Zn={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function $n(e,t){let n=Yn(e.constraint,t);return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=n?[n]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function Xn(e){return e.reduce(Jn,Zn)}function Jn(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}const Kn={id:String,defaultAllDay:Boolean,url:String,format:String,events:xt,eventDataTransform:xt,success:xt,failure:xt};function er(e,t,n=tr(t)){let r;if("string"==typeof e?r={url:e}:"function"==typeof e||Array.isArray(e)?r={events:e}:"object"==typeof e&&e&&(r=e),r){let{refined:i,extra:o}=At(r,n),s=nr(i,t);if(s)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:B(),sourceDefId:s.sourceDefId,meta:s.meta,ui:$n(i,t),extendedProps:o}}return null}function tr(e){return Object.assign(Object.assign(Object.assign({},qn),Kn),e.pluginHooks.eventSourceRefiners)}function nr(e,t){let n=t.pluginHooks.eventSourceDefs;for(let r=n.length-1;r>=0;r-=1){let t=n[r].parseMeta(e);if(t)return{sourceDefId:r,meta:t}}return null}function rr(e,t,n,r,i){switch(t.type){case"RECEIVE_EVENTS":return ir(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,i);case"RESET_RAW_EVENTS":return or(e,n[t.sourceId],t.rawEvents,r.activeRange,i);case"ADD_EVENTS":return lr(e,t.eventStore,r?r.activeRange:null,i);case"RESET_EVENTS":return t.eventStore;case"MERGE_EVENTS":return Vn(e,t.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?Cn(e,r.activeRange,i):e;case"REMOVE_EVENTS":return Gn(e,t.eventStore);case"REMOVE_EVENT_SOURCE":return dr(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return Fn(e,(e=>!e.sourceId));case"REMOVE_ALL_EVENTS":return{defs:{},instances:{}};default:return e}}function ir(e,t,n,r,i,o){if(t&&n===t.latestFetchId){let n=Bn(sr(i,t,o),t,o);return r&&(n=Cn(n,r,o)),Vn(dr(e,t.sourceId),n)}return e}function or(e,t,n,r,i){const{defIdMap:o,instanceIdMap:s}=ur(e);return Cn(Bn(sr(n,t,i),t,i,!1,o,s),r,i)}function sr(e,t,n){let r=n.options.eventDataTransform,i=t?t.eventDataTransform:null;return i&&(e=ar(e,i)),r&&(e=ar(e,r)),e}function ar(e,t){let n;if(t){n=[];for(let r of e){let e=t(r);e?n.push(e):null==e&&n.push(r)}}else n=e;return n}function lr(e,t,n,r){return n&&(t=Cn(t,n,r)),Vn(e,t)}function cr(e,t,n){let{defs:r}=e,i=kt(e.instances,(e=>r[e.defId].allDay?e:Object.assign(Object.assign({},e),{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo})));return{defs:r,instances:i}}function dr(e,t){return Fn(e,(e=>e.sourceId!==t))}function ur(e){const{defs:t,instances:n}=e,r={},i={};for(let o in t){const e=t[o],{publicId:n}=e;n&&(r[n]=o)}for(let o in n){const e=t[n[o].defId],{publicId:r}=e;r&&(i[r]=o)}return{defIdMap:r,instanceIdMap:i}}class hr{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,t){fr(this.handlers,e,t)}off(e,t){gr(this.handlers,e,t)}trigger(e,...t){let n=this.handlers[e]||[],r=this.options&&this.options[e],i=[].concat(r||[],n);for(let o of i)o.apply(this.thisContext,t)}hasHandlers(e){return Boolean(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}function fr(e,t,n){(e[t]||(e[t]=[])).push(n)}function gr(e,t,n){n?e[t]&&(e[t]=e[t].filter((e=>e!==n))):delete e[t]}const pr={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function mr(e,t){return Bn(vr(e),null,t)}function vr(e){let t;return t=!0===e?[{}]:Array.isArray(e)?e.filter((e=>e.daysOfWeek)):"object"==typeof e&&e?[e]:[],t=t.map((e=>Object.assign(Object.assign({},pr),e))),t}function yr(e,t,n){n.emitter.trigger("select",Object.assign(Object.assign({},wr(e,n)),{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function br(e,t){t.emitter.trigger("unselect",{jsEvent:e?e.origEvent:null,view:t.viewApi||t.calendarApi.view})}function wr(e,t){let n={};for(let o of t.pluginHooks.dateSpanTransforms)Object.assign(n,o(e,t));var r,i;return Object.assign(n,(r=e,i=t.dateEnv,Object.assign(Object.assign({},Xr(r.range,i,r.allDay)),{allDay:r.allDay}))),n}function Er(e,t,n){let{dateEnv:r,options:i}=n,o=t;return e?(o=Te(o),o=r.add(o,i.defaultAllDayEventDuration)):o=r.add(o,i.defaultTimedEventDuration),o}function Ar(e,t,n,r){let i=Nr(e.defs,t),o={defs:{},instances:{}};for(let s in e.defs){let t=e.defs[s];o.defs[s]=xr(t,i[s],n,r)}for(let s in e.instances){let t=e.instances[s],a=o.defs[t.defId];o.instances[s]=Dr(t,a,i[t.defId],n,r)}return o}function xr(e,t,n,r){let i=n.standardProps||{};null==i.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);let o=Object.assign(Object.assign(Object.assign({},e),i),{ui:Object.assign(Object.assign({},e.ui),i.ui)});n.extendedProps&&(o.extendedProps=Object.assign(Object.assign({},o.extendedProps),n.extendedProps));for(let s of r.pluginHooks.eventDefMutationAppliers)s(o,n,r);return!o.hasEnd&&r.options.forceEventDuration&&(o.hasEnd=!0),o}function Dr(e,t,n,r,i){let{dateEnv:o}=i,s=r.standardProps&&!0===r.standardProps.allDay,a=r.standardProps&&!1===r.standardProps.hasEnd,l=Object.assign({},e);return s&&(l.range=bn(l.range)),r.datesDelta&&n.startEditable&&(l.range={start:o.add(l.range.start,r.datesDelta),end:o.add(l.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(l.range={start:o.add(l.range.start,r.startDelta),end:l.range.end}),r.endDelta&&n.durationEditable&&(l.range={start:l.range.start,end:o.add(l.range.end,r.endDelta)}),a&&(l.range={start:l.range.start,end:Er(t.allDay,l.range.start,i)}),t.allDay&&(l.range={start:Te(l.range.start),end:Te(l.range.end)}),l.range.end<l.range.start&&(l.range.end=Er(t.allDay,l.range.start,i)),l}class Tr{constructor(e,t){this.context=e,this.internalEventSource=t}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class Sr{constructor(e,t,n){this._context=e,this._def=t,this._instance=n||null}setProp(e,t){if(e in Mn)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if("id"===e)t=Rn[e](t),this.mutate({standardProps:{publicId:t}});else if(e in Rn)t=Rn[e](t),this.mutate({standardProps:{[e]:t}});else if(e in qn){let n=qn[e](t);n="color"===e?{backgroundColor:t,borderColor:t}:"editable"===e?{startEditable:t,durationEditable:t}:{[e]:t},this.mutate({standardProps:{ui:n}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,t){this.mutate({extendedProps:{[e]:t}})}setStart(e,t={}){let{dateEnv:n}=this._context,r=n.createMarker(e);if(r&&this._instance){let e=En(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:e}):this.mutate({startDelta:e})}}setEnd(e,t={}){let n,{dateEnv:r}=this._context;if((null==e||(n=r.createMarker(e),n))&&this._instance)if(n){let e=En(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:e})}else this.mutate({standardProps:{hasEnd:!1}})}setDates(e,t,n={}){let r,{dateEnv:i}=this._context,o={allDay:n.allDay},s=i.createMarker(e);var a,l;if(s&&((null==t||(r=i.createMarker(t),r))&&this._instance)){let e=this._instance.range;!0===n.allDay&&(e=bn(e));let t=En(e.start,s,i,n.granularity);if(r){let s=En(e.end,r,i,n.granularity);l=s,(a=t).years===l.years&&a.months===l.months&&a.days===l.days&&a.milliseconds===l.milliseconds?this.mutate({datesDelta:t,standardProps:o}):this.mutate({startDelta:t,endDelta:s,standardProps:o})}else o.hasEnd=!1,this.mutate({datesDelta:t,standardProps:o})}}moveStart(e){let t=re(e);t&&this.mutate({startDelta:t})}moveEnd(e){let t=re(e);t&&this.mutate({endDelta:t})}moveDates(e){let t=re(e);t&&this.mutate({datesDelta:t})}setAllDay(e,t={}){let n={allDay:e},{maintainDuration:r}=t;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})}formatRange(e){let{dateEnv:t}=this._context,n=this._instance,r=ht(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})}mutate(e){let t=this._instance;if(t){let n=this._def,r=this._context,{eventStore:i}=r.getCurrentData(),o=Un(i,t.instanceId);o=Ar(o,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,r);let s=new Sr(r,n,t);this._def=o.defs[n.defId],this._instance=o.instances[t.instanceId],r.dispatch({type:"MERGE_EVENTS",eventStore:o}),r.emitter.trigger("eventChange",{oldEvent:s,event:this,relatedEvents:Cr(o,r,t),revert(){r.dispatch({type:"RESET_EVENTS",eventStore:i})}})}}remove(){let e=this._context,t=kr(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})}get source(){let{sourceId:e}=this._def;return e?new Tr(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let t=this._def,{ui:n}=t,{startStr:r,endStr:i}=this,o={allDay:t.allDay};return t.title&&(o.title=t.title),r&&(o.start=r),i&&(o.end=i),t.publicId&&(o.id=t.publicId),t.groupId&&(o.groupId=t.groupId),t.url&&(o.url=t.url),n.display&&"auto"!==n.display&&(o.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?o.color=n.backgroundColor:(n.backgroundColor&&(o.backgroundColor=n.backgroundColor),n.borderColor&&(o.borderColor=n.borderColor)),n.textColor&&(o.textColor=n.textColor),n.classNames.length&&(o.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?Object.assign(o,t.extendedProps):o.extendedProps=t.extendedProps),o}toJSON(){return this.toPlainObject()}}function kr(e){let t=e._def,n=e._instance;return{defs:{[t.defId]:t},instances:n?{[n.instanceId]:n}:{}}}function Cr(e,t,n){let{defs:r,instances:i}=e,o=[],s=n?n.instanceId:"";for(let a in i){let e=i[a],n=r[e.defId];e.instanceId!==s&&o.push(new Sr(t,n,e))}return o}function _r(e,t,n,r){let i={},o={},s={},a=[],l=[],c=Nr(e.defs,t);for(let d in e.defs){let t=e.defs[d];"inverse-background"===c[t.defId].display&&(t.groupId?(i[t.groupId]=[],s[t.groupId]||(s[t.groupId]=t)):o[d]=[])}for(let d in e.instances){let t=e.instances[d],s=e.defs[t.defId],u=c[s.defId],h=t.range,f=!s.allDay&&r?wn(h,r):h,g=pn(f,n);g&&("inverse-background"===u.display?s.groupId?i[s.groupId].push(g):o[t.defId].push(g):"none"!==u.display&&("background"===u.display?a:l).push({def:s,ui:u,instance:t,range:g,isStart:f.start&&f.start.valueOf()===g.start.valueOf(),isEnd:f.end&&f.end.valueOf()===g.end.valueOf()}))}for(let d in i){let e=fn(i[d],n);for(let t of e){let e=s[d],n=c[e.defId];a.push({def:e,ui:n,instance:null,range:t,isStart:!1,isEnd:!1})}}for(let d in o){let t=fn(o[d],n);for(let n of t)a.push({def:e.defs[d],ui:c[d],instance:null,range:n,isStart:!1,isEnd:!1})}return{bg:a,fg:l}}function Rr(e){return"background"===e.ui.display||"inverse-background"===e.ui.display}function Mr(e,t){e.fcSeg=t}function Ir(e){return e.fcSeg||e.parentNode.fcSeg||null}function Nr(e,t){return kt(e,(e=>Or(e,t)))}function Or(e,t){let n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),Xn(n)}function zr(e,t){let n=e.map(Hr);return n.sort(((e,n)=>Q(e,n,t))),n.map((e=>e._seg))}function Hr(e){let{eventRange:t}=e,n=t.def,r=t.instance?t.instance.range:t.range,i=r.start?r.start.valueOf():0,o=r.end?r.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},n.extendedProps),n),{id:n.publicId,start:i,end:o,duration:o-i,allDay:Number(n.allDay),_seg:e})}function Pr(e,t){let{pluginHooks:n}=t,r=n.isDraggableTransformers,{def:i,ui:o}=e.eventRange,s=o.startEditable;for(let a of r)s=a(s,i,o,t);return s}function Lr(e,t){return e.isStart&&e.eventRange.ui.durationEditable&&t.options.eventResizableFromStart}function Br(e,t){return e.isEnd&&e.eventRange.ui.durationEditable}function jr(e,t,n,r,i,o,s){let{dateEnv:a,options:l}=n,{displayEventTime:c,displayEventEnd:d}=l,u=e.eventRange.def,h=e.eventRange.instance;null==c&&(c=!1!==r),null==d&&(d=!1!==i);let f=h.range.start,g=h.range.end,p=o||e.start||e.eventRange.range.start,m=s||e.end||e.eventRange.range.end,v=Te(f).valueOf()===Te(p).valueOf(),y=Te(ve(g,-1)).valueOf()===Te(ve(m,-1)).valueOf();return c&&!u.allDay&&(v||y)?(p=v?f:p,m=y?g:m,d&&u.hasEnd?a.formatRange(p,m,t,{forcedStartTzo:o?null:h.forcedStartTzo,forcedEndTzo:s?null:h.forcedEndTzo}):a.format(p,t,{forcedTzo:o?null:h.forcedStartTzo})):""}function Ur(e,t,n){let r=e.eventRange.range;return{isPast:r.end<=(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&yn(t,r.start)}}function Wr(e){let t=["fc-event"];return e.isMirror&&t.push("fc-event-mirror"),e.isDraggable&&t.push("fc-event-draggable"),(e.isStartResizable||e.isEndResizable)&&t.push("fc-event-resizable"),e.isDragging&&t.push("fc-event-dragging"),e.isResizing&&t.push("fc-event-resizing"),e.isSelected&&t.push("fc-event-selected"),e.isStart&&t.push("fc-event-start"),e.isEnd&&t.push("fc-event-end"),e.isPast&&t.push("fc-event-past"),e.isToday&&t.push("fc-event-today"),e.isFuture&&t.push("fc-event-future"),t}function Vr(e){return e.instance?e.instance.instanceId:`${e.def.defId}:${e.range.start.toISOString()}`}function Fr(e,t){let{def:n,instance:r}=e.eventRange,{url:i}=n;if(i)return{href:i};let{emitter:o,options:s}=t,{eventInteractive:a}=s;return null==a&&(a=n.interactive,null==a&&(a=Boolean(o.hasHandlers("eventClick")))),a?P((e=>{o.trigger("eventClick",{el:e.target,event:new Sr(t,n,r),jsEvent:e,view:t.viewApi})})):{}}const Gr={start:xt,end:xt,allDay:Boolean};function Yr(e,t,n){let r=Qr(e,t),{range:i}=r;if(!i.start)return null;if(!i.end){if(null==n)return null;i.end=t.add(i.start,n)}return r}function Qr(e,t){let{refined:n,extra:r}=At(e,Gr),i=n.start?t.createMarkerMeta(n.start):null,o=n.end?t.createMarkerMeta(n.end):null,{allDay:s}=n;return null==s&&(s=i&&i.isTimeUnspecified&&(!o||o.isTimeUnspecified)),Object.assign({range:{start:i?i.marker:null,end:o?o.marker:null},allDay:s},r)}function qr(e,t){return n=e.range,r=t.range,(null===n.start?null:n.start.valueOf())===(null===r.start?null:r.start.valueOf())&&(null===n.end?null:n.end.valueOf())===(null===r.end?null:r.end.valueOf())&&e.allDay===t.allDay&&Zr(e,t);var n,r}function Zr(e,t){for(let n in t)if("range"!==n&&"allDay"!==n&&e[n]!==t[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}function $r(e,t,n){return Object.assign(Object.assign({},Xr(e,t,n)),{timeZone:t.timeZone})}function Xr(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}function Jr(e,t,n){let r=On({editable:!1},n),i=Hn(r.refined,r.extra,"",e.allDay,!0,n);return{def:i,ui:Or(i,t),instance:Sn(i.defId,e.range),range:e.range,isStart:!0,isEnd:!0}}function Kr(e,t,n){let r=!1,i=function(e){r||(r=!0,t(e))},o=function(e){r||(r=!0,n(e))},s=e(i,o);s&&"function"==typeof s.then&&s.then(i,o)}class ei extends Error{constructor(e,t){super(e),this.response=t}}function ti(e,t,n){const r={method:e=e.toUpperCase()};return"GET"===e?t+=(-1===t.indexOf("?")?"?":"&")+new URLSearchParams(n):(r.body=new URLSearchParams(n),r.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(t,r).then((e=>{if(e.ok)return e.json().then((t=>[t,e]),(()=>{throw new ei("Failure parsing JSON",e)}));throw new ei("Request failed",e)}))}let ni;function ri(){return null==ni&&(ni=ii()),ni}function ii(){if("undefined"==typeof document)return!0;let e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.innerHTML="<table><tr><td><div></div></td></tr></table>",e.querySelector("table").style.height="100px",e.querySelector("div").style.height="100%",document.body.appendChild(e);let t=e.querySelector("div").offsetHeight>0;return document.body.removeChild(e),t}class oi extends Xt{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{Ft((()=>{this.setState({forPrint:!0})}))},this.handleAfterPrint=()=>{Ft((()=>{this.setState({forPrint:!1})}))}}render(){let{props:e}=this,{options:t}=e,{forPrint:n}=this.state,r=n||"auto"===t.height||"auto"===t.contentHeight,i=r||null==t.height?"":t.height,o=["fc",n?"fc-media-print":"fc-media-screen",`fc-direction-${t.direction}`,e.theme.getClass("root")];return ri()||o.push("fc-liquid-hack"),e.children(o,i,r,n)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}class si{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}function ai(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter,isHitComboAllowed:t.isHitComboAllowed||null}}function li(e){return{[e.component.uid]:e}}const ci={};class di{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,t){let{currentDataManager:n}=this;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn(`Unknown listener name '${e}'`)}off(e,t){this.currentDataManager.emitter.off(e,t)}trigger(e,...t){this.currentDataManager.emitter.trigger(e,...t)}changeView(e,t){this.batchRendering((()=>{if(this.unselect(),t)if(t.start&&t.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{let{dateEnv:n}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:n.createMarker(t)})}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})}))}zoomTo(e,t){let n;t=t||"day",n=this.getCurrentData().viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let t,n,{viewSpecs:r,toolbarConfig:i}=this.getCurrentData(),o=[].concat(i.header?i.header.viewsWithButtons:[],i.footer?i.footer.viewsWithButtons:[]);for(let s in r)o.push(s);for(t=0;t<o.length;t+=1)if(n=r[o[t]],n&&n.singleUnit===e)return n;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:Dn(e.calendarOptions.now,e.dateEnv)})}gotoDate(e){let t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})}incrementDate(e){let t=this.getCurrentData(),n=re(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,t){let{dateEnv:n}=this.getCurrentData();return n.format(n.createMarker(e),ht(t))}formatRange(e,t,n){let{dateEnv:r}=this.getCurrentData();return r.formatRange(r.createMarker(e),r.createMarker(t),ht(n),n)}formatIso(e,t){let{dateEnv:n}=this.getCurrentData();return n.formatIso(n.createMarker(e),{omitTime:t})}select(e,t){let n;n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};let r=this.getCurrentData(),i=Yr(n,r.dateEnv,re({days:1}));i&&(this.dispatch({type:"SELECT_DATES",selection:i}),yr(i,null,r))}unselect(e){let t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),br(e,t))}addEvent(e,t){if(e instanceof Sr){let t=e._def,n=e._instance;return this.getCurrentData().eventStore.defs[t.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:jn({def:t,instance:n})}),this.triggerEventAdd(e)),e}let n,r=this.getCurrentData();if(t instanceof Tr)n=t.internalEventSource;else if("boolean"==typeof t)t&&([n]=_t(r.eventSources));else if(null!=t){let e=this.getEventSourceById(t);if(!e)return console.warn(`Could not find an event source with ID "${t}"`),null;n=e.internalEventSource}let i=Nn(e,n,r,!1);if(i){let e=new Sr(r,i.def,i.def.recurringDef?null:i.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:jn(i)}),this.triggerEventAdd(e),e}return null}triggerEventAdd(e){let{emitter:t}=this.getCurrentData();t.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:kr(e)})}})}getEventById(e){let t=this.getCurrentData(),{defs:n,instances:r}=t.eventStore;e=String(e);for(let i in n){let o=n[i];if(o.publicId===e){if(o.recurringDef)return new Sr(t,o,null);for(let e in r){let n=r[e];if(n.defId===o.defId)return new Sr(t,o,n)}}}return null}getEvents(){let e=this.getCurrentData();return Cr(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),t=e.eventSources,n=[];for(let r in t)n.push(new Tr(e,t[r]));return n}getEventSourceById(e){let t=this.getCurrentData(),n=t.eventSources;e=String(e);for(let r in n)if(n[r].publicId===e)return new Tr(t,n[r]);return null}addEventSource(e){let t=this.getCurrentData();if(e instanceof Tr)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let n=er(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new Tr(t,n)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let t=re(e);t&&this.trigger("_scrollRequest",{time:t})}}function ui(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom}function hi(e,t){let n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}function fi(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}}function gi(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}}function pi(e,t){return{left:e.left-t.left,top:e.top-t.top}}const mi={defs:{},instances:{}};class vi{constructor(){this.getKeysForEventDefs=Ve(this._getKeysForEventDefs),this.splitDateSelection=Ve(this._splitDateSpan),this.splitEventStore=Ve(this._splitEventStore),this.splitIndividualUi=Ve(this._splitIndividualUi),this.splitEventDrag=Ve(this._splitInteraction),this.splitEventResize=Ve(this._splitInteraction),this.eventUiBuilders={}}splitProps(e){let t=this.getKeyInfo(e),n=this.getKeysForEventDefs(e.eventStore),r=this.splitDateSelection(e.dateSelection),i=this.splitIndividualUi(e.eventUiBases,n),o=this.splitEventStore(e.eventStore,n),s=this.splitEventDrag(e.eventDrag),a=this.splitEventResize(e.eventResize),l={};this.eventUiBuilders=kt(t,((e,t)=>this.eventUiBuilders[t]||Ve(yi)));for(let c in t){let n=t[c],d=o[c]||mi,u=this.eventUiBuilders[c];l[c]={businessHours:n.businessHours||e.businessHours,dateSelection:r[c]||null,eventStore:d,eventUiBases:u(e.eventUiBases[""],n.ui,i[c]),eventSelection:d.instances[e.eventSelection]?e.eventSelection:"",eventDrag:s[c]||null,eventResize:a[c]||null}}return l}_splitDateSpan(e){let t={};if(e){let n=this.getKeysForDateSpan(e);for(let r of n)t[r]=e}return t}_getKeysForEventDefs(e){return kt(e.defs,(e=>this.getKeysForEventDef(e)))}_splitEventStore(e,t){let{defs:n,instances:r}=e,i={};for(let o in n)for(let e of t[o])i[e]||(i[e]={defs:{},instances:{}}),i[e].defs[o]=n[o];for(let o in r){let e=r[o];for(let n of t[e.defId])i[n]&&(i[n].instances[o]=e)}return i}_splitIndividualUi(e,t){let n={};for(let r in e)if(r)for(let i of t[r])n[i]||(n[i]={}),n[i][r]=e[r];return n}_splitInteraction(e){let t={};if(e){let n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),i=this._splitEventStore(e.mutatedEvents,r),o=r=>{t[r]||(t[r]={affectedEvents:n[r]||mi,mutatedEvents:i[r]||mi,isEvent:e.isEvent})};for(let e in n)o(e);for(let e in i)o(e)}return t}}function yi(e,t,n){let r=[];e&&r.push(e),t&&r.push(t);let i={"":Xn(r)};return n&&Object.assign(i,n),i}function bi(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:Boolean(r&&!yn(r.activeRange,e)),isOther:Boolean(r&&!yn(r.currentRange,e)),isToday:Boolean(t&&yn(t,e)),isPast:Boolean(n?e<n:!!t&&e<t.start),isFuture:Boolean(n?e>n:!!t&&e>=t.end)}}function wi(e,t){let n=["fc-day",`fc-day-${pe[e.dow]}`];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}const Ei=ht({year:"numeric",month:"long",day:"numeric"}),Ai=ht({week:"long"});function xi(e,t,n="day",r=!0){const{dateEnv:i,options:o,calendarApi:s}=e;let a=i.format(t,"week"===n?Ai:Ei);if(o.navLinks){let e=i.toDate(t);const l=e=>{let r="day"===n?o.navLinkDayClick:"week"===n?o.navLinkWeekClick:null;"function"==typeof r?r.call(s,i.toDate(t),e):("string"==typeof r&&(n=r),s.zoomTo(t,n))};return Object.assign({title:X(o.navLinkHint,[a,e],a),"data-navlink":""},r?H(l):{onClick:l})}return{"aria-label":a}}let Di,Ti=null;function Si(){let e=document.createElement("div");T(e,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),e.innerHTML="<div></div>",document.body.appendChild(e);let t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left;return w(e),t}function ki(){let e=document.createElement("div");e.style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e);let t=Ci(e);return document.body.removeChild(e),t}function Ci(e){return{x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth}}function _i(e,t=!1){let n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,i=parseInt(n.borderRightWidth,10)||0,o=parseInt(n.borderTopWidth,10)||0,s=parseInt(n.borderBottomWidth,10)||0,a=Ci(e),l=a.y-r-i,c={borderLeft:r,borderRight:i,borderTop:o,borderBottom:s,scrollbarBottom:a.x-o-s,scrollbarLeft:0,scrollbarRight:0};return null===Ti&&(Ti=Si()),Ti&&"rtl"===n.direction?c.scrollbarLeft=l:c.scrollbarRight=l,t&&(c.paddingLeft=parseInt(n.paddingLeft,10)||0,c.paddingRight=parseInt(n.paddingRight,10)||0,c.paddingTop=parseInt(n.paddingTop,10)||0,c.paddingBottom=parseInt(n.paddingBottom,10)||0),c}function Ri(e,t=!1,n){let r=n?e.getBoundingClientRect():Mi(e),i=_i(e,t),o={left:r.left+i.borderLeft+i.scrollbarLeft,right:r.right-i.borderRight-i.scrollbarRight,top:r.top+i.borderTop,bottom:r.bottom-i.borderBottom-i.scrollbarBottom};return t&&(o.left+=i.paddingLeft,o.right-=i.paddingRight,o.top+=i.paddingTop,o.bottom-=i.paddingBottom),o}function Mi(e){let t=e.getBoundingClientRect();return{left:t.left+window.scrollX,top:t.top+window.scrollY,right:t.right+window.scrollX,bottom:t.bottom+window.scrollY}}function Ii(e){let t=Ni(e),n=e.getBoundingClientRect();for(let r of t){let e=hi(n,r.getBoundingClientRect());if(!e)return null;n=e}return n}function Ni(e){let t=[];for(;e instanceof HTMLElement;){let n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}class Oi{constructor(e,t,n,r){this.els=t;let i=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(i.left),r&&this.buildElVerticals(i.top)}buildElHorizontals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n}buildElVerticals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n}leftToIndex(e){let t,{lefts:n,rights:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}topToIndex(e){let t,{tops:n,bottoms:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return zi(this.tops||[],e.tops||[])&&zi(this.bottoms||[],e.bottoms||[])&&zi(this.lefts||[],e.lefts||[])&&zi(this.rights||[],e.rights||[])}}function zi(e,t){const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(Math.round(e[r])!==Math.round(t[r]))return!1;return!0}class Hi{getMaxScrollTop(){return this.getScrollHeight()-this.getClientHeight()}getMaxScrollLeft(){return this.getScrollWidth()-this.getClientWidth()}canScrollVertically(){return this.getMaxScrollTop()>0}canScrollHorizontally(){return this.getMaxScrollLeft()>0}canScrollUp(){return this.getScrollTop()>0}canScrollDown(){return this.getScrollTop()<this.getMaxScrollTop()}canScrollLeft(){return this.getScrollLeft()>0}canScrollRight(){return this.getScrollLeft()<this.getMaxScrollLeft()}}class Pi extends Hi{constructor(e){super(),this.el=e}getScrollTop(){return this.el.scrollTop}getScrollLeft(){return this.el.scrollLeft}setScrollTop(e){this.el.scrollTop=e}setScrollLeft(e){this.el.scrollLeft=e}getScrollWidth(){return this.el.scrollWidth}getScrollHeight(){return this.el.scrollHeight}getClientHeight(){return this.el.clientHeight}getClientWidth(){return this.el.clientWidth}}class Li extends Hi{getScrollTop(){return window.scrollY}getScrollLeft(){return window.scrollX}setScrollTop(e){window.scroll(window.scrollX,e)}setScrollLeft(e){window.scroll(e,window.scrollY)}getScrollWidth(){return document.documentElement.scrollWidth}getScrollHeight(){return document.documentElement.scrollHeight}getClientHeight(){return document.documentElement.clientHeight}getClientWidth(){return document.documentElement.clientWidth}}class Bi extends Xt{constructor(){super(...arguments),this.uid=B()}prepareHits(){}queryHit(e,t,n,r){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!E(e,".fc-event-mirror")}isValidDateDownEl(e){return!(E(e,".fc-event:not(.fc-bg-event)")||E(e,".fc-more-link")||E(e,"a[data-navlink]")||E(e,".fc-popover"))}}class ji{constructor(e=e=>e.thickness||1){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let t=[];for(let n of e)this.insertEntry(n,t);return t}insertEntry(e,t){let n=this.findInsertion(e);this.isInsertionValid(n,e)?this.insertEntryAt(e,n):this.handleInvalidInsertion(n,e,t)}isInsertionValid(e,t){return(-1===this.maxCoord||e.levelCoord+this.getEntryThickness(t)<=this.maxCoord)&&(-1===this.maxStackCnt||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,t,n){if(this.allowReslicing&&e.touchingEntry){const r=Object.assign(Object.assign({},t),{span:Fi(t.span,e.touchingEntry.span)});n.push(r),this.splitEntry(t,e.touchingEntry,n)}else n.push(t)}splitEntry(e,t,n){let r=e.span,i=t.span;r.start<i.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:r.start,end:i.start}},n),r.end>i.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.end,end:r.end}},n)}insertEntryAt(e,t){let{entriesByLevel:n,levelCoords:r}=this;-1===t.lateral?(Gi(r,t.level,t.levelCoord),Gi(n,t.level,[e])):Gi(n[t.level],t.lateral,e),this.stackCnts[Wi(e)]=t.stackCnt}findInsertion(e){let{levelCoords:t,entriesByLevel:n,strictOrder:r,stackCnts:i}=this,o=t.length,s=0,a=-1,l=-1,c=null,d=0;for(let f=0;f<o;f+=1){const o=t[f];if(!r&&o>=s+this.getEntryThickness(e))break;let u,h=n[f],g=Yi(h,e.span.start,Ui),p=g[0]+g[1];for(;(u=h[p])&&u.span.start<e.span.end;){let e=o+this.getEntryThickness(u);e>s&&(s=e,c=u,a=f,l=p),e===s&&(d=Math.max(d,i[Wi(u)]+1)),p+=1}}let u=0;if(c)for(u=a+1;u<o&&t[u]<s;)u+=1;let h=-1;return u<o&&t[u]===s&&(h=Yi(n[u],e.span.end,Ui)[0]),{touchingLevel:a,touchingLateral:l,touchingEntry:c,stackCnt:d,levelCoord:s,level:u,lateral:h}}toRects(){let{entriesByLevel:e,levelCoords:t}=this,n=e.length,r=[];for(let i=0;i<n;i+=1){let n=e[i],o=t[i];for(let e of n)r.push(Object.assign(Object.assign({},e),{thickness:this.getEntryThickness(e),levelCoord:o}))}return r}}function Ui(e){return e.span.end}function Wi(e){return e.index+":"+e.span.start}function Vi(e){let t=[];for(let i of e){let e=[],o={span:i.span,entries:[i]};for(let i of t)Fi(i.span,o.span)?o={entries:i.entries.concat(o.entries),span:(n=i.span,r=o.span,{start:Math.min(n.start,r.start),end:Math.max(n.end,r.end)})}:e.push(i);e.push(o),t=e}var n,r;return t}function Fi(e,t){let n=Math.max(e.start,t.start),r=Math.min(e.end,t.end);return n<r?{start:n,end:r}:null}function Gi(e,t,n){e.splice(t,0,n)}function Yi(e,t,n){let r=0,i=e.length;if(!i||t<n(e[r]))return[0,0];if(t>n(e[i-1]))return[i,0];for(;r<i;){let o=Math.floor(r+(i-r)/2),s=n(e[o]);if(t<s)i=o;else{if(!(t>s))return[o,1];r=o+1}}return[r,0]}class Qi{constructor(e,t){this.emitter=new hr}destroy(){}setMirrorIsVisible(e){}setMirrorNeedsRevert(e){}setAutoScrollEnabled(e){}}const qi={},Zi={startTime:re,duration:re,create:Boolean,sourceId:String};function $i(e){let{refined:t,extra:n}=At(e,Zi);return{startTime:t.startTime||null,duration:t.duration||null,create:null==t.create||t.create,sourceId:t.sourceId,leftoverProps:n}}function Xi(e,t){return ht(!e||t>10?{weekday:"short"}:t>1?{weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}:{weekday:"long"})}const Ji="fc-col-header-cell";function Ki(e){return e.text}class eo extends Xt{render(){let{dateEnv:e,options:t,theme:n,viewApi:r}=this.context,{props:i}=this,{date:s,dateProfile:a}=i,l=bi(s,i.todayRange,null,a),c=[Ji].concat(wi(l,n)),d=e.format(s,i.dayHeaderFormat),u=!l.isDisabled&&i.colCnt>1?xi(this.context,s):{},h=Object.assign(Object.assign(Object.assign({date:e.toDate(s),view:r},i.extraRenderProps),{text:d}),l);return(0,o.az)(an,{elTag:"th",elClasses:c,elAttrs:Object.assign({role:"columnheader",colSpan:i.colSpan,"data-date":l.isDisabled?void 0:Be(s)},i.extraDataAttrs),renderProps:h,generatorName:"dayHeaderContent",customGenerator:t.dayHeaderContent,defaultGenerator:Ki,classNameGenerator:t.dayHeaderClassNames,didMount:t.dayHeaderDidMount,willUnmount:t.dayHeaderWillUnmount},(e=>(0,o.az)("div",{className:"fc-scrollgrid-sync-inner"},!l.isDisabled&&(0,o.az)(e,{elTag:"a",elAttrs:u,elClasses:["fc-col-header-cell-cushion",i.isSticky&&"fc-sticky"]}))))}}const to=ht({weekday:"long"});class no extends Xt{render(){let{props:e}=this,{dateEnv:t,theme:n,viewApi:r,options:i}=this.context,s=me(new Date(2592e5),e.dow),a={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},l=t.format(s,e.dayHeaderFormat),c=Object.assign(Object.assign(Object.assign(Object.assign({date:s},a),{view:r}),e.extraRenderProps),{text:l});return(0,o.az)(an,{elTag:"th",elClasses:[Ji,...wi(a,n),...e.extraClassNames||[]],elAttrs:Object.assign({role:"columnheader",colSpan:e.colSpan},e.extraDataAttrs),renderProps:c,generatorName:"dayHeaderContent",customGenerator:i.dayHeaderContent,defaultGenerator:Ki,classNameGenerator:i.dayHeaderClassNames,didMount:i.dayHeaderDidMount,willUnmount:i.dayHeaderWillUnmount},(n=>(0,o.az)("div",{className:"fc-scrollgrid-sync-inner"},(0,o.az)(n,{elTag:"a",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"],elAttrs:{"aria-label":t.format(s,to)}}))))}}class ro extends o.wA{constructor(e,t){super(e,t),this.initialNowDate=Dn(t.options.now,t.dateEnv),this.initialNowQueriedMs=(new Date).valueOf(),this.state=this.computeTiming().currentState}render(){let{props:e,state:t}=this;return e.children(t.nowDate,t.todayRange)}componentDidMount(){this.setTimeout()}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout()}computeTiming(){let{props:e,context:t}=this,n=ve(this.initialNowDate,(new Date).valueOf()-this.initialNowQueriedMs),r=t.dateEnv.startOf(n,e.unit),i=t.dateEnv.add(r,re(1,e.unit)),o=i.valueOf()-n.valueOf();return o=Math.min(864e5,o),{currentState:{nowDate:r,todayRange:io(r)},nextState:{nowDate:i,todayRange:io(i)},waitMs:o}}setTimeout(){let{nextState:e,waitMs:t}=this.computeTiming();this.timeoutId=setTimeout((()=>{this.setState(e,(()=>{this.setTimeout()}))}),t)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}function io(e){let t=Te(e);return{start:t,end:me(t,1)}}ro.contextType=qt;class oo extends Xt{constructor(){super(...arguments),this.createDayHeaderFormatter=Ve(so)}render(){let{context:e}=this,{dates:t,dateProfile:n,datesRepDistinctDays:r,renderIntro:i}=this.props,s=this.createDayHeaderFormatter(e.options.dayHeaderFormat,r,t.length);return(0,o.az)(ro,{unit:"day"},((e,a)=>(0,o.az)("tr",{role:"row"},i&&i("day"),t.map((e=>r?(0,o.az)(eo,{key:e.toISOString(),date:e,dateProfile:n,todayRange:a,colCnt:t.length,dayHeaderFormat:s}):(0,o.az)(no,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:s}))))))}}function so(e,t,n){return e||Xi(t,n)}class ao{constructor(e,t){let n=e.start,{end:r}=e,i=[],o=[],s=-1;for(;n<r;)t.isHiddenDay(n)?i.push(s+.5):(s+=1,i.push(s),o.push(n)),n=me(n,1);this.dates=o,this.indices=i,this.cnt=o.length}sliceRange(e){let t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(me(e.end,-1)),r=Math.max(0,t),i=Math.min(this.cnt-1,n);return r=Math.ceil(r),i=Math.floor(i),r<=i?{firstIndex:r,lastIndex:i,isStart:t===r,isEnd:n===i}:null}getDateDayIndex(e){let{indices:t}=this,n=Math.floor(ye(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]}}class lo{constructor(e,t){let n,r,i,{dates:o}=e;if(t){for(r=o[0].getUTCDay(),n=1;n<o.length&&o[n].getUTCDay()!==r;n+=1);i=Math.ceil(o.length/n)}else i=1,n=o.length;this.rowCnt=i,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}buildCells(){let e=[];for(let t=0;t<this.rowCnt;t+=1){let n=[];for(let e=0;e<this.colCnt;e+=1)n.push(this.buildCell(t,e));e.push(n)}return e}buildCell(e,t){let n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}}buildHeaderDates(){let e=[];for(let t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e}sliceRange(e){let{colCnt:t}=this,n=this.daySeries.sliceRange(e),r=[];if(n){let{firstIndex:e,lastIndex:i}=n,o=e;for(;o<=i;){let s=Math.floor(o/t),a=Math.min((s+1)*t,i+1);r.push({row:s,firstCol:o%t,lastCol:(a-1)%t,isStart:n.isStart&&o===e,isEnd:n.isEnd&&a-1===i}),o=a}}return r}}class co{constructor(){this.sliceBusinessHours=Ve(this._sliceBusinessHours),this.sliceDateSelection=Ve(this._sliceDateSpan),this.sliceEventStore=Ve(this._sliceEventStore),this.sliceEventDrag=Ve(this._sliceInteraction),this.sliceEventResize=Ve(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,t,n,r,...i){let{eventUiBases:o}=e,s=this.sliceEventStore(e.eventStore,o,t,n,...i);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,t,n,o,r,...i),businessHourSegs:this.sliceBusinessHours(e.businessHours,t,n,r,...i),fgEventSegs:s.fg,bgEventSegs:s.bg,eventDrag:this.sliceEventDrag(e.eventDrag,o,t,n,...i),eventResize:this.sliceEventResize(e.eventResize,o,t,n,...i),eventSelection:e.eventSelection}}sliceNowDate(e,t,n,r,...i){return this._sliceDateSpan({range:{start:e,end:ve(e,1)},allDay:!1},t,n,{},r,...i)}_sliceBusinessHours(e,t,n,r,...i){return e?this._sliceEventStore(Cn(e,uo(t,Boolean(n)),r),{},t,n,...i).bg:[]}_sliceEventStore(e,t,n,r,...i){if(e){let o=_r(e,t,uo(n,Boolean(r)),r);return{bg:this.sliceEventRanges(o.bg,i),fg:this.sliceEventRanges(o.fg,i)}}return{bg:[],fg:[]}}_sliceInteraction(e,t,n,r,...i){if(!e)return null;let o=_r(e.mutatedEvents,t,uo(n,Boolean(r)),r);return{segs:this.sliceEventRanges(o.fg,i),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,t,n,r,i,...o){if(!e)return[];let s=uo(t,Boolean(n)),a=pn(e.range,s);if(a){let t=Jr(e=Object.assign(Object.assign({},e),{range:a}),r,i),n=this.sliceRange(e.range,...o);for(let e of n)e.eventRange=t;return n}return[]}sliceEventRanges(e,t){let n=[];for(let r of e)n.push(...this.sliceEventRange(r,t));return n}sliceEventRange(e,t){let n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:me(n.start,1)});let r=this.sliceRange(n,...t);for(let i of r)i.eventRange=e,i.isStart=e.isStart&&i.isStart,i.isEnd=e.isEnd&&i.isEnd;return r}}function uo(e,t){let n=e.activeRange;return t?n:{start:ve(n.start,e.slotMinTime.milliseconds),end:ve(n.end,e.slotMaxTime.milliseconds-864e5)}}function ho(e,t,n){let{instances:r}=e.mutatedEvents;for(let i in r)if(!vn(t.validRange,r[i].range))return!1;return go({eventDrag:e},n)}function fo(e,t,n){return!!vn(t.validRange,e.range)&&go({dateSelection:e},n)}function go(e,t){let n=t.getCurrentData(),r=Object.assign({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginHooks.isPropsValid||po)(r,t)}function po(e,t,n={},r){return!(e.eventDrag&&!mo(e,t,n,r))&&!(e.dateSelection&&!vo(e,t,n,r))}function mo(e,t,n,r){let i=t.getCurrentData(),o=e.eventDrag,s=o.mutatedEvents,a=s.defs,l=s.instances,c=Nr(a,o.isEvent?e.eventUiBases:{"":i.selectionConfig});r&&(c=kt(c,r));let d=(u=e.eventStore,h=o.affectedEvents.instances,{defs:u.defs,instances:St(u.instances,(e=>!h[e.instanceId]))});var u,h;let f=d.defs,g=d.instances,p=Nr(f,e.eventUiBases);for(let m in l){let r=l[m],s=r.range,u=c[r.defId],h=a[r.defId];if(!yo(u.constraints,s,d,e.businessHours,t))return!1;let{eventOverlap:v}=t.options,y="function"==typeof v?v:null;for(let e in g){let n=g[e];if(mn(s,n.range)){if(!1===p[n.defId].overlap&&o.isEvent)return!1;if(!1===u.overlap)return!1;if(y&&!y(new Sr(t,f[n.defId],n),new Sr(t,h,r)))return!1}}let b=i.eventStore;for(let e of u.allows){let i,o=Object.assign(Object.assign({},n),{range:r.range,allDay:h.allDay}),s=b.defs[h.defId],a=b.instances[m];if(i=s?new Sr(t,s,a):new Sr(t,h),!e(wr(o,t),i))return!1}}return!0}function vo(e,t,n,r){let i=e.eventStore,o=i.defs,s=i.instances,a=e.dateSelection,l=a.range,{selectionConfig:c}=t.getCurrentData();if(r&&(c=r(c)),!yo(c.constraints,l,i,e.businessHours,t))return!1;let{selectOverlap:d}=t.options,u="function"==typeof d?d:null;for(let h in s){let e=s[h];if(mn(l,e.range)){if(!1===c.overlap)return!1;if(u&&!u(new Sr(t,o[e.defId],e),null))return!1}}for(let h of c.allows){if(!h(wr(Object.assign(Object.assign({},n),a),t),null))return!1}return!0}function yo(e,t,n,r,i){for(let o of e)if(!Eo(bo(o,t,n,r,i),t))return!1;return!0}function bo(e,t,n,r,i){return"businessHours"===e?wo(Cn(r,t,i)):"string"==typeof e?wo(Fn(n,(t=>t.groupId===e))):"object"==typeof e&&e?wo(Cn(e,t,i)):[]}function wo(e){let{instances:t}=e,n=[];for(let r in t)n.push(t[r].range);return n}function Eo(e,t){for(let n of e)if(vn(n,t))return!0;return!1}const Ao=/^(visible|hidden)$/;class xo extends Xt{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,en(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:t,liquidIsAbsolute:n}=e,r=t&&n,i=["fc-scroller"];return t&&(n?i.push("fc-scroller-liquid-absolute"):i.push("fc-scroller-liquid")),(0,o.az)("div",{ref:this.handleEl,className:i.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(Ao.test(this.props.overflowX))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:n}=e;for(let r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().width>t)return!0}return!1}needsYScrolling(){if(Ao.test(this.props.overflowY))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:n}=e;for(let r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().height>t)return!0}return!1}getXScrollbarWidth(){return Ao.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return Ao.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class Do{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(e,t)=>{let{depths:n,currentMap:r}=this,i=!1,o=!1;null!==e?(i=t in r,r[t]=e,n[t]=(n[t]||0)+1,o=!0):(n[t]-=1,n[t]||(delete r[t],delete this.callbackMap[t],i=!0)),this.masterCallback&&(i&&this.masterCallback(null,String(t)),o&&this.masterCallback(e,String(t)))}}createRef(e){let t=this.callbackMap[e];return t||(t=this.callbackMap[e]=t=>{this.handleValue(t,String(e))}),t}collect(e,t,n){return Ht(this.currentMap,e,t,n)}getAll(){return _t(this.currentMap)}}function To(e){let t=x(e,".fc-scrollgrid-shrink"),n=0;for(let r of t)n=Math.max(n,ee(r));return Math.ceil(n)}function So(e,t){return e.liquid&&t.liquid}function ko(e,t){return null!=t.maxHeight||So(e,t)}function Co(e,t,n,r){let{expandRows:i}=n;return"function"==typeof t.content?t.content(n):(0,o.az)("table",{role:"presentation",className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:i?n.clientHeight:""}},n.tableColGroupNode,(0,o.az)(r?"thead":"tbody",{role:"presentation"},"function"==typeof t.rowContent?t.rowContent(n):t.rowContent))}function _o(e,t){return ge(e,t,Rt)}function Ro(e,t){let n=[];for(let r of e){let e=r.span||1;for(let i=0;i<e;i+=1)n.push((0,o.az)("col",{style:{width:"shrink"===r.width?Mo(t):r.width||"",minWidth:r.minWidth||""}}))}return(0,o.az)("colgroup",{},...n)}function Mo(e){return null==e?4:e}function Io(e){for(let t of e)if("shrink"===t.width)return!0;return!1}function No(e,t){let n=["fc-scrollgrid",t.theme.getClass("table")];return e&&n.push("fc-scrollgrid-liquid"),n}function Oo(e,t){let n=["fc-scrollgrid-section",`fc-scrollgrid-section-${e.type}`,e.className];return t&&e.liquid&&null==e.maxHeight&&n.push("fc-scrollgrid-section-liquid"),e.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}function zo(e){return(0,o.az)("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})}function Ho(e){let{stickyHeaderDates:t}=e;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t}function Po(e){let{stickyFooterScrollbar:t}=e;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t}class Lo extends Xt{constructor(){super(...arguments),this.processCols=Ve((e=>e),_o),this.renderMicroColGroup=Ve(Ro),this.scrollerRefs=new Do,this.scrollerElRefs=new Do(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){let{props:e,state:t,context:n}=this,r=e.sections||[],i=this.processCols(e.cols),s=this.renderMicroColGroup(i,t.shrinkWidth),a=No(e.liquid,n);e.collapsibleWidth&&a.push("fc-scrollgrid-collapsible");let l,c=r.length,d=0,u=[],h=[],f=[];for(;d<c&&"header"===(l=r[d]).type;)u.push(this.renderSection(l,s,!0)),d+=1;for(;d<c&&"body"===(l=r[d]).type;)h.push(this.renderSection(l,s,!1)),d+=1;for(;d<c&&"footer"===(l=r[d]).type;)f.push(this.renderSection(l,s,!0)),d+=1;let g=!ri();const p={role:"rowgroup"};return(0,o.az)("table",{role:"grid",className:a.join(" "),style:{height:e.height}},Boolean(!g&&u.length)&&(0,o.az)("thead",p,...u),Boolean(!g&&h.length)&&(0,o.az)("tbody",p,...h),Boolean(!g&&f.length)&&(0,o.az)("tfoot",p,...f),g&&(0,o.az)("tbody",p,...u,...h,...f))}renderSection(e,t,n){return"outerContent"in e?(0,o.az)(o.HY,{key:e.key},e.outerContent):(0,o.az)("tr",{key:e.key,role:"presentation",className:Oo(e,this.props.liquid).join(" ")},this.renderChunkTd(e,t,e.chunk,n))}renderChunkTd(e,t,n,r){if("outerContent"in n)return n.outerContent;let{props:i}=this,{forceYScrollbars:s,scrollerClientWidths:a,scrollerClientHeights:l}=this.state,c=ko(i,e),d=So(i,e),u=i.liquid?s?"scroll":c?"auto":"hidden":"visible",h=e.key,f=Co(e,n,{tableColGroupNode:t,tableMinWidth:"",clientWidth:i.collapsibleWidth||void 0===a[h]?null:a[h],clientHeight:void 0!==l[h]?l[h]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},r);return(0,o.az)(r?"th":"td",{ref:n.elRef,role:"presentation"},(0,o.az)("div",{className:"fc-scroller-harness"+(d?" fc-scroller-harness-liquid":"")},(0,o.az)(xo,{ref:this.scrollerRefs.createRef(h),elRef:this.scrollerElRefs.createRef(h),overflowY:u,overflowX:i.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:d,liquidIsAbsolute:!0},f)))}_handleScrollerEl(e,t){let n=Bo(this.props.sections,t);n&&en(n.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return Io(this.props.cols)?To(this.scrollerElRefs.getAll()):0}computeScrollerDims(){let e=(Di||(Di=ki()),Di),{scrollerRefs:t,scrollerElRefs:n}=this,r=!1,i={},o={};for(let s in t.currentMap){let e=t.currentMap[s];if(e&&e.needsYScrolling()){r=!0;break}}for(let s of this.props.sections){let t=s.key,a=n.currentMap[t];if(a){let n=a.parentNode;i[t]=Math.floor(n.getBoundingClientRect().width-(r?e.y:0)),o[t]=Math.floor(n.getBoundingClientRect().height)}}return{forceYScrollbars:r,scrollerClientWidths:i,scrollerClientHeights:o}}}function Bo(e,t){for(let n of e)if(n.key===t)return n;return null}Lo.addStateEquality({scrollerClientWidths:Rt,scrollerClientHeights:Rt});class jo extends Xt{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e&&Mr(e,this.props.seg)}}render(){const{props:e,context:t}=this,{options:n}=t,{seg:r}=e,{eventRange:i}=r,{ui:s}=i,a={event:new Sr(t,i.def,i.instance),view:t.viewApi,timeText:e.timeText,textColor:s.textColor,backgroundColor:s.backgroundColor,borderColor:s.borderColor,isDraggable:!e.disableDragging&&Pr(r,t),isStartResizable:!e.disableResizing&&Lr(r,t),isEndResizable:!e.disableResizing&&Br(r),isMirror:Boolean(e.isDragging||e.isResizing||e.isDateSelecting),isStart:Boolean(r.isStart),isEnd:Boolean(r.isEnd),isPast:Boolean(e.isPast),isFuture:Boolean(e.isFuture),isToday:Boolean(e.isToday),isSelected:Boolean(e.isSelected),isDragging:Boolean(e.isDragging),isResizing:Boolean(e.isResizing)};return(0,o.az)(an,Object.assign({},e,{elRef:this.handleEl,elClasses:[...Wr(a),...r.eventRange.ui.classNames,...e.elClasses||[]],renderProps:a,generatorName:"eventContent",customGenerator:n.eventContent,defaultGenerator:e.defaultGenerator,classNameGenerator:n.eventClassNames,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount}))}componentDidUpdate(e){this.el&&this.props.seg!==e.seg&&Mr(this.el,this.props.seg)}}class Uo extends Xt{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r}=e,{ui:i}=r.eventRange,s=jr(r,n.eventTimeFormat||e.defaultTimeFormat,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return(0,o.az)(jo,Object.assign({},e,{elTag:"a",elStyle:{borderColor:i.borderColor,backgroundColor:i.backgroundColor},elAttrs:Fr(r,t),defaultGenerator:Wo,timeText:s}),((e,t)=>(0,o.az)(o.HY,null,(0,o.az)(e,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:t.textColor}}),Boolean(t.isStartResizable)&&(0,o.az)("div",{className:"fc-event-resizer fc-event-resizer-start"}),Boolean(t.isEndResizable)&&(0,o.az)("div",{className:"fc-event-resizer fc-event-resizer-end"}))))}}function Wo(e){return(0,o.az)("div",{className:"fc-event-main-frame"},e.timeText&&(0,o.az)("div",{className:"fc-event-time"},e.timeText),(0,o.az)("div",{className:"fc-event-title-container"},(0,o.az)("div",{className:"fc-event-title fc-sticky"},e.event.title||(0,o.az)(o.HY,null," "))))}const Vo=e=>(0,o.az)(qt.Consumer,null,(t=>{let{options:n}=t,r={isAxis:e.isAxis,date:t.dateEnv.toDate(e.date),view:t.viewApi};return(0,o.az)(an,Object.assign({},e,{elTag:e.elTag||"div",renderProps:r,generatorName:"nowIndicatorContent",customGenerator:n.nowIndicatorContent,classNameGenerator:n.nowIndicatorClassNames,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount}))})),Fo=ht({day:"numeric"});class Go extends Xt{constructor(){super(...arguments),this.refineRenderProps=Fe(Qo)}render(){let{props:e,context:t}=this,{options:n}=t,r=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:t.viewApi,dateEnv:t.dateEnv,monthStartFormat:n.monthStartFormat});return(0,o.az)(an,Object.assign({},e,{elClasses:[...wi(r,t.theme),...e.elClasses||[]],elAttrs:Object.assign(Object.assign({},e.elAttrs),r.isDisabled?{}:{"data-date":Be(e.date)}),renderProps:r,generatorName:"dayCellContent",customGenerator:n.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:r.isDisabled?void 0:n.dayCellClassNames,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount}))}}function Yo(e){return Boolean(e.dayCellContent||nn("dayCellContent",e))}function Qo(e){let{date:t,dateEnv:n,dateProfile:r,isMonthStart:i}=e,o=bi(t,e.todayRange,null,r),s=e.showDayNumber?n.format(t,i?e.monthStartFormat:Fo):"";return Object.assign(Object.assign(Object.assign({date:n.toDate(t),view:e.viewApi},o),{isMonthStart:i,dayNumberText:s}),e.extraRenderProps)}class qo extends Xt{render(){let{props:e}=this,{seg:t}=e;return(0,o.az)(jo,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:t.eventRange.ui.backgroundColor},defaultGenerator:Zo,seg:t,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}}function Zo(e){let{title:t}=e.event;return t&&(0,o.az)("div",{className:"fc-event-title"},e.event.title)}function $o(e){return(0,o.az)("div",{className:`fc-${e}`})}const Xo=e=>(0,o.az)(qt.Consumer,null,(t=>{let{dateEnv:n,options:r}=t,{date:i}=e,s=r.weekNumberFormat||e.defaultFormat,a={num:n.computeWeekNumber(i),text:n.format(i,s),date:i};return(0,o.az)(an,Object.assign({},e,{renderProps:a,generatorName:"weekNumberContent",customGenerator:r.weekNumberContent,defaultGenerator:Jo,classNameGenerator:r.weekNumberClassNames,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount}))}));function Jo(e){return e.text}const Ko=10;class es extends Xt{constructor(){super(...arguments),this.state={titleId:_()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&en(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{const t=k(e);this.rootEl.contains(t)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{"Escape"===e.key&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:t}=this.context,{props:n,state:r}=this,i=["fc-popover",e.getClass("popover")].concat(n.extraClassNames||[]);return(0,s.jz)((0,o.az)("div",Object.assign({},n.extraAttrs,{id:n.id,className:i.join(" "),"aria-labelledby":r.titleId,ref:this.handleRootEl}),(0,o.az)("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},(0,o.az)("span",{className:"fc-popover-title",id:r.titleId},n.title),(0,o.az)("span",{className:"fc-popover-close "+e.getIconClass("close"),title:t.closeHint,onClick:this.handleCloseClick})),(0,o.az)("div",{className:"fc-popover-body "+e.getClass("popoverContent")},n.children)),n.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:t,alignGridTop:n}=this.props,{rootEl:r}=this,i=Ii(t);if(i){let o=r.getBoundingClientRect(),s=n?E(t,".fc-scrollgrid").getBoundingClientRect().top:i.top,a=e?i.right-o.width:i.left;s=Math.max(s,Ko),a=Math.min(a,document.documentElement.clientWidth-Ko-o.width),a=Math.max(a,Ko);let l=r.offsetParent.getBoundingClientRect();T(r,{top:s-l.top,left:a-l.left})}}}class ts extends Bi{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:t}=this.context,{props:n}=this,{startDate:r,todayRange:i,dateProfile:s}=n,a=t.format(r,e.dayPopoverFormat);return(0,o.az)(Go,{elRef:this.handleRootEl,date:r,dateProfile:s,todayRange:i},((t,r,i)=>(0,o.az)(es,{elRef:i.ref,id:n.id,title:a,extraClassNames:["fc-more-popover"].concat(i.className||[]),extraAttrs:i,parentEl:n.parentEl,alignmentEl:n.alignmentEl,alignGridTop:n.alignGridTop,onClose:n.onClose},Yo(e)&&(0,o.az)(t,{elTag:"div",elClasses:["fc-more-popover-misc"]}),n.children)))}queryHit(e,t,n,r){let{rootEl:i,props:o}=this;return e>=0&&e<n&&t>=0&&t<r?{dateProfile:o.dateProfile,dateSpan:Object.assign({allDay:!o.forceTimed,range:{start:o.startDate,end:o.endDate}},o.extraDateSpan),dayEl:i,rect:{left:0,top:0,right:n,bottom:r},layer:1}:null}}class ns extends Xt{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:_()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&en(this.props.elRef,e)},this.handleClick=e=>{let{props:t,context:n}=this,{moreLinkClick:r}=n.options,i=is(t).start;function o(e){let{def:t,instance:r,range:i}=e.eventRange;return{event:new Sr(n,t,r),start:n.dateEnv.toDate(i.start),end:n.dateEnv.toDate(i.end),isStart:e.isStart,isEnd:e.isEnd}}"function"==typeof r&&(r=r({date:i,allDay:Boolean(t.allDayDate),allSegs:t.allSegs.map(o),hiddenSegs:t.hiddenSegs.map(o),jsEvent:e,view:n.viewApi})),r&&"popover"!==r?"string"==typeof r&&n.calendarApi.zoomTo(i,r):this.setState({isPopoverOpen:!0})},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:t}=this;return(0,o.az)(qt.Consumer,null,(n=>{let{viewApi:r,options:i,calendarApi:s}=n,{moreLinkText:a}=i,{moreCnt:l}=e,c=is(e),d="function"==typeof a?a.call(s,l):`+${l} ${a}`,u=X(i.moreLinkHint,[l],d),h={num:l,shortText:`+${l}`,text:d,view:r};return(0,o.az)(o.HY,null,Boolean(e.moreCnt)&&(0,o.az)(an,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),H(this.handleClick)),{title:u,"aria-expanded":t.isPopoverOpen,"aria-controls":t.isPopoverOpen?t.popoverId:""}),renderProps:h,generatorName:"moreLinkContent",customGenerator:i.moreLinkContent,defaultGenerator:e.defaultGenerator||rs,classNameGenerator:i.moreLinkClassNames,didMount:i.moreLinkDidMount,willUnmount:i.moreLinkWillUnmount},e.children),t.isPopoverOpen&&(0,o.az)(ts,{id:t.popoverId,startDate:c.start,endDate:c.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))}))}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=E(this.linkEl,".fc-view-harness"))}}function rs(e){return e.text}function is(e){if(e.allDayDate)return{start:e.allDayDate,end:me(e.allDayDate,1)};let{hiddenSegs:t}=e;return{start:os(t),end:(n=t,n.reduce(as).eventRange.range.end)};var n}function os(e){return e.reduce(ss).eventRange.range.start}function ss(e,t){return e.eventRange.range.start<t.eventRange.range.start?e:t}function as(e,t){return e.eventRange.range.end>t.eventRange.range.end?e:t}r()}catch(l){r(l)}}))},33297:function(e,t,n){n.d(t,{Z:()=>o});function r(e){return"Tag"===e||"Monat"===e?"r":"Jahr"===e?"s":""}function i(e){return"Tag"===e||"Monat"===e?"r":"Jahr"===e?"s":""}var o=[{code:"af",week:{dow:1,doy:4},buttonText:{prev:"Vorige",next:"Volgende",today:"Vandag",year:"Jaar",month:"Maand",week:"Week",day:"Dag",list:"Agenda"},allDayText:"Heeldag",moreLinkText:"Addisionele",noEventsText:"Daar is geen gebeurtenisse nie"},{code:"ar-dz",week:{dow:0,doy:4},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar-kw",week:{dow:0,doy:12},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar-ly",week:{dow:6,doy:12},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar-ma",week:{dow:6,doy:12},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar-sa",week:{dow:0,doy:6},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar-tn",week:{dow:1,doy:4},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"ar",week:{dow:6,doy:12},direction:"rtl",buttonText:{prev:"السابق",next:"التالي",today:"اليوم",year:"سنة",month:"شهر",week:"أسبوع",day:"يوم",list:"أجندة"},weekText:"أسبوع",allDayText:"اليوم كله",moreLinkText:"أخرى",noEventsText:"أي أحداث لعرض"},{code:"az",week:{dow:1,doy:4},buttonText:{prev:"Əvvəl",next:"Sonra",today:"Bu Gün",year:"Il",month:"Ay",week:"Həftə",day:"Gün",list:"Gündəm"},weekText:"Həftə",allDayText:"Bütün Gün",moreLinkText(e){return"+ daha çox "+e},noEventsText:"Göstərmək üçün hadisə yoxdur"},{code:"bg",week:{dow:1,doy:4},buttonText:{prev:"назад",next:"напред",today:"днес",year:"година",month:"Месец",week:"Седмица",day:"Ден",list:"График"},allDayText:"Цял ден",moreLinkText(e){return"+още "+e},noEventsText:"Няма събития за показване"},{code:"bn",week:{dow:0,doy:6},buttonText:{prev:"পেছনে",next:"সামনে",today:"আজ",year:"বছর",month:"মাস",week:"সপ্তাহ",day:"দিন",list:"তালিকা"},weekText:"সপ্তাহ",allDayText:"সারাদিন",moreLinkText(e){return"+অন্যান্য "+e},noEventsText:"কোনো ইভেন্ট নেই"},{code:"bs",week:{dow:1,doy:7},buttonText:{prev:"Prošli",next:"Sljedeći",today:"Danas",year:"Godina",month:"Mjesec",week:"Sedmica",day:"Dan",list:"Raspored"},weekText:"Sed",allDayText:"Cijeli dan",moreLinkText(e){return"+ još "+e},noEventsText:"Nema događaja za prikazivanje"},{code:"ca",week:{dow:1,doy:4},buttonText:{prev:"Anterior",next:"Següent",today:"Avui",year:"Any",month:"Mes",week:"Setmana",day:"Dia",list:"Agenda"},weekText:"Set",allDayText:"Tot el dia",moreLinkText:"més",noEventsText:"No hi ha esdeveniments per mostrar"},{code:"cs",week:{dow:1,doy:4},buttonText:{prev:"Dříve",next:"Později",today:"Nyní",year:"Rok",month:"Měsíc",week:"Týden",day:"Den",list:"Agenda"},weekText:"Týd",allDayText:"Celý den",moreLinkText(e){return"+další: "+e},noEventsText:"Žádné akce k zobrazení"},{code:"cy",week:{dow:1,doy:4},buttonText:{prev:"Blaenorol",next:"Nesaf",today:"Heddiw",year:"Blwyddyn",month:"Mis",week:"Wythnos",day:"Dydd",list:"Rhestr"},weekText:"Wythnos",allDayText:"Trwy'r dydd",moreLinkText:"Mwy",noEventsText:"Dim digwyddiadau"},{code:"da",week:{dow:1,doy:4},buttonText:{prev:"Forrige",next:"Næste",today:"I dag",year:"År",month:"Måned",week:"Uge",day:"Dag",list:"Agenda"},weekText:"Uge",allDayText:"Hele dagen",moreLinkText:"flere",noEventsText:"Ingen arrangementer at vise"},{code:"de-at",week:{dow:1,doy:4},buttonText:{prev:"Zurück",next:"Vor",today:"Heute",year:"Jahr",month:"Monat",week:"Woche",day:"Tag",list:"Terminübersicht"},weekText:"KW",weekTextLong:"Woche",allDayText:"Ganztägig",moreLinkText(e){return"+ weitere "+e},noEventsText:"Keine Ereignisse anzuzeigen",buttonHints:{prev(e){return`Vorherige${r(e)} ${e}`},next(e){return`Nächste${r(e)} ${e}`},today(e){return"Tag"===e?"Heute":`Diese${r(e)} ${e}`}},viewHint(e){return e+("Woche"===e?"n":"Monat"===e?"s":"es")+"ansicht"},navLinkHint:"Gehe zu $0",moreLinkHint(e){return"Zeige "+(1===e?"ein weiteres Ereignis":e+" weitere Ereignisse")},closeHint:"Schließen",timeHint:"Uhrzeit",eventHint:"Ereignis"},{code:"de",week:{dow:1,doy:4},buttonText:{prev:"Zurück",next:"Vor",today:"Heute",year:"Jahr",month:"Monat",week:"Woche",day:"Tag",list:"Terminübersicht"},weekText:"KW",weekTextLong:"Woche",allDayText:"Ganztägig",moreLinkText(e){return"+ weitere "+e},noEventsText:"Keine Ereignisse anzuzeigen",buttonHints:{prev(e){return`Vorherige${i(e)} ${e}`},next(e){return`Nächste${i(e)} ${e}`},today(e){return"Tag"===e?"Heute":`Diese${i(e)} ${e}`}},viewHint(e){return e+("Woche"===e?"n":"Monat"===e?"s":"es")+"ansicht"},navLinkHint:"Gehe zu $0",moreLinkHint(e){return"Zeige "+(1===e?"ein weiteres Ereignis":e+" weitere Ereignisse")},closeHint:"Schließen",timeHint:"Uhrzeit",eventHint:"Ereignis"},{code:"el",week:{dow:1,doy:4},buttonText:{prev:"Προηγούμενος",next:"Επόμενος",today:"Σήμερα",year:"Ετος",month:"Μήνας",week:"Εβδομάδα",day:"Ημέρα",list:"Ατζέντα"},weekText:"Εβδ",allDayText:"Ολοήμερο",moreLinkText:"περισσότερα",noEventsText:"Δεν υπάρχουν γεγονότα προς εμφάνιση"},{code:"en-au",week:{dow:1,doy:4},buttonHints:{prev:"Previous $0",next:"Next $0",today:"This $0"},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(e){return`Show ${e} more event${1===e?"":"s"}`}},{code:"en-gb",week:{dow:1,doy:4},buttonHints:{prev:"Previous $0",next:"Next $0",today:"This $0"},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(e){return`Show ${e} more event${1===e?"":"s"}`}},{code:"en-nz",week:{dow:1,doy:4},buttonHints:{prev:"Previous $0",next:"Next $0",today:"This $0"},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(e){return`Show ${e} more event${1===e?"":"s"}`}},{code:"eo",week:{dow:1,doy:4},buttonText:{prev:"Antaŭa",next:"Sekva",today:"Hodiaŭ",year:"Jaro",month:"Monato",week:"Semajno",day:"Tago",list:"Tagordo"},weekText:"Sm",allDayText:"Tuta tago",moreLinkText:"pli",noEventsText:"Neniuj eventoj por montri"},{code:"es",week:{dow:0,doy:6},buttonText:{prev:"Ant",next:"Sig",today:"Hoy",year:"Año",month:"Mes",week:"Semana",day:"Día",list:"Agenda"},weekText:"Sm",allDayText:"Todo el día",moreLinkText:"más",noEventsText:"No hay eventos para mostrar"},{code:"es",week:{dow:1,doy:4},buttonText:{prev:"Ant",next:"Sig",today:"Hoy",year:"Año",month:"Mes",week:"Semana",day:"Día",list:"Agenda"},buttonHints:{prev:"$0 antes",next:"$0 siguiente",today(e){return"Día"===e?"Hoy":("Semana"===e?"Esta":"Este")+" "+e.toLocaleLowerCase()}},viewHint(e){return"Vista "+("Semana"===e?"de la":"del")+" "+e.toLocaleLowerCase()},weekText:"Sm",weekTextLong:"Semana",allDayText:"Todo el día",moreLinkText:"más",moreLinkHint(e){return`Mostrar ${e} eventos más`},noEventsText:"No hay eventos para mostrar",navLinkHint:"Ir al $0",closeHint:"Cerrar",timeHint:"La hora",eventHint:"Evento"},{code:"et",week:{dow:1,doy:4},buttonText:{prev:"Eelnev",next:"Järgnev",today:"Täna",year:"Aasta",month:"Kuu",week:"Nädal",day:"Päev",list:"Päevakord"},weekText:"näd",allDayText:"Kogu päev",moreLinkText(e){return"+ veel "+e},noEventsText:"Kuvamiseks puuduvad sündmused"},{code:"eu",week:{dow:1,doy:7},buttonText:{prev:"Aur",next:"Hur",today:"Gaur",year:"Urtea",month:"Hilabetea",week:"Astea",day:"Eguna",list:"Agenda"},weekText:"As",allDayText:"Egun osoa",moreLinkText:"gehiago",noEventsText:"Ez dago ekitaldirik erakusteko"},{code:"fa",week:{dow:6,doy:12},direction:"rtl",buttonText:{prev:"قبلی",next:"بعدی",today:"امروز",year:"سال",month:"ماه",week:"هفته",day:"روز",list:"برنامه"},weekText:"هف",allDayText:"تمام روز",moreLinkText(e){return"بیش از "+e},noEventsText:"هیچ رویدادی به نمایش"},{code:"fi",week:{dow:1,doy:4},buttonText:{prev:"Edellinen",next:"Seuraava",today:"Tänään",year:"Vuosi",month:"Kuukausi",week:"Viikko",day:"Päivä",list:"Tapahtumat"},weekText:"Vk",allDayText:"Koko päivä",moreLinkText:"lisää",noEventsText:"Ei näytettäviä tapahtumia"},{code:"fr",buttonText:{prev:"Précédent",next:"Suivant",today:"Aujourd'hui",year:"Année",month:"Mois",week:"Semaine",day:"Jour",list:"Mon planning"},weekText:"Sem.",allDayText:"Toute la journée",moreLinkText:"en plus",noEventsText:"Aucun évènement à afficher"},{code:"fr-ch",week:{dow:1,doy:4},buttonText:{prev:"Précédent",next:"Suivant",today:"Courant",year:"Année",month:"Mois",week:"Semaine",day:"Jour",list:"Mon planning"},weekText:"Sm",allDayText:"Toute la journée",moreLinkText:"en plus",noEventsText:"Aucun évènement à afficher"},{code:"fr",week:{dow:1,doy:4},buttonText:{prev:"Précédent",next:"Suivant",today:"Aujourd'hui",year:"Année",month:"Mois",week:"Semaine",day:"Jour",list:"Planning"},weekText:"Sem.",weekTextLong:"Semaine",allDayText:"Toute la journée",moreLinkText:"en plus",noEventsText:"Aucun évènement à afficher"},{code:"gl",week:{dow:1,doy:4},buttonText:{prev:"Ant",next:"Seg",today:"Hoxe",year:"Ano",month:"Mes",week:"Semana",day:"Día",list:"Axenda"},buttonHints:{prev:"$0 antes",next:"$0 seguinte",today(e){return"Día"===e?"Hoxe":("Semana"===e?"Esta":"Este")+" "+e.toLocaleLowerCase()}},viewHint(e){return"Vista "+("Semana"===e?"da":"do")+" "+e.toLocaleLowerCase()},weekText:"Sm",weekTextLong:"Semana",allDayText:"Todo o día",moreLinkText:"máis",moreLinkHint(e){return`Amosar ${e} eventos máis`},noEventsText:"Non hai eventos para amosar",navLinkHint:"Ir ao $0",closeHint:"Pechar",timeHint:"A hora",eventHint:"Evento"},{code:"he",direction:"rtl",buttonText:{prev:"הקודם",next:"הבא",today:"היום",year:"שנה",month:"חודש",week:"שבוע",day:"יום",list:"סדר יום"},allDayText:"כל היום",moreLinkText:"נוספים",noEventsText:"אין אירועים להצגה",weekText:"שבוע"},{code:"hi",week:{dow:0,doy:6},buttonText:{prev:"पिछला",next:"अगला",today:"आज",year:"वर्ष",month:"महीना",week:"सप्ताह",day:"दिन",list:"कार्यसूची"},weekText:"हफ्ता",allDayText:"सभी दिन",moreLinkText(e){return"+अधिक "+e},noEventsText:"कोई घटनाओं को प्रदर्शित करने के लिए"},{code:"hr",week:{dow:1,doy:7},buttonText:{prev:"Prijašnji",next:"Sljedeći",today:"Danas",year:"Godina",month:"Mjesec",week:"Tjedan",day:"Dan",list:"Raspored"},weekText:"Tje",allDayText:"Cijeli dan",moreLinkText(e){return"+ još "+e},noEventsText:"Nema događaja za prikaz"},{code:"hu",week:{dow:1,doy:4},buttonText:{prev:"vissza",next:"előre",today:"ma",year:"Év",month:"Hónap",week:"Hét",day:"Nap",list:"Lista"},weekText:"Hét",allDayText:"Egész nap",moreLinkText:"további",noEventsText:"Nincs megjeleníthető esemény"},{code:"hy-am",week:{dow:1,doy:4},buttonText:{prev:"Նախորդ",next:"Հաջորդ",today:"Այսօր",year:"Տարի",month:"Ամիս",week:"Շաբաթ",day:"Օր",list:"Օրվա ցուցակ"},weekText:"Շաբ",allDayText:"Ամբողջ օր",moreLinkText(e){return"+ ևս "+e},noEventsText:"Բացակայում է իրադարձությունը ցուցադրելու"},{code:"id",week:{dow:1,doy:7},buttonText:{prev:"mundur",next:"maju",today:"hari ini",year:"Tahun",month:"Bulan",week:"Minggu",day:"Hari",list:"Agenda"},weekText:"Mg",allDayText:"Sehari penuh",moreLinkText:"lebih",noEventsText:"Tidak ada acara untuk ditampilkan"},{code:"is",week:{dow:1,doy:4},buttonText:{prev:"Fyrri",next:"Næsti",today:"Í dag",year:"Ár",month:"Mánuður",week:"Vika",day:"Dagur",list:"Dagskrá"},weekText:"Vika",allDayText:"Allan daginn",moreLinkText:"meira",noEventsText:"Engir viðburðir til að sýna"},{code:"it",week:{dow:1,doy:4},buttonText:{prev:"Prec",next:"Succ",today:"Oggi",year:"Anno",month:"Mese",week:"Settimana",day:"Giorno",list:"Agenda"},weekText:"Sm",allDayText:"Tutto il giorno",moreLinkText(e){return"+altri "+e},noEventsText:"Non ci sono eventi da visualizzare"},{code:"ja",buttonText:{prev:"前",next:"次",today:"今日",year:"年",month:"月",week:"週",day:"日",list:"予定リスト"},weekText:"週",allDayText:"終日",moreLinkText(e){return"他 "+e+" 件"},noEventsText:"表示する予定はありません"},{code:"ka",week:{dow:1,doy:7},buttonText:{prev:"წინა",next:"შემდეგი",today:"დღეს",year:"წელიწადი",month:"თვე",week:"კვირა",day:"დღე",list:"დღის წესრიგი"},weekText:"კვ",allDayText:"მთელი დღე",moreLinkText(e){return"+ კიდევ "+e},noEventsText:"ღონისძიებები არ არის"},{code:"kk",week:{dow:1,doy:7},buttonText:{prev:"Алдыңғы",next:"Келесі",today:"Бүгін",year:"Жыл",month:"Ай",week:"Апта",day:"Күн",list:"Күн тәртібі"},weekText:"Не",allDayText:"Күні бойы",moreLinkText(e){return"+ тағы "+e},noEventsText:"Көрсету үшін оқиғалар жоқ"},{code:"km",week:{dow:1,doy:4},buttonText:{prev:"មុន",next:"បន្ទាប់",today:"ថ្ងៃនេះ",year:"ឆ្នាំ",month:"ខែ",week:"សប្តាហ៍",day:"ថ្ងៃ",list:"បញ្ជី"},weekText:"សប្តាហ៍",allDayText:"ពេញមួយថ្ងៃ",moreLinkText:"ច្រើនទៀត",noEventsText:"គ្មានព្រឹត្តិការណ៍ត្រូវបង្ហាញ"},{code:"ko",buttonText:{prev:"이전달",next:"다음달",today:"오늘",year:"년도",month:"월",week:"주",day:"일",list:"일정목록"},weekText:"주",allDayText:"종일",moreLinkText:"개",noEventsText:"일정이 없습니다"},{code:"ku",week:{dow:6,doy:12},direction:"rtl",buttonText:{prev:"پێشتر",next:"دواتر",today:"ئەمڕو",year:"ساڵ",month:"مانگ",week:"هەفتە",day:"ڕۆژ",list:"بەرنامە"},weekText:"هەفتە",allDayText:"هەموو ڕۆژەکە",moreLinkText:"زیاتر",noEventsText:"هیچ ڕووداوێك نیە"},{code:"lb",week:{dow:1,doy:4},buttonText:{prev:"Zréck",next:"Weider",today:"Haut",year:"Joer",month:"Mount",week:"Woch",day:"Dag",list:"Terminiwwersiicht"},weekText:"W",allDayText:"Ganzen Dag",moreLinkText:"méi",noEventsText:"Nee Evenementer ze affichéieren"},{code:"lt",week:{dow:1,doy:4},buttonText:{prev:"Atgal",next:"Pirmyn",today:"Šiandien",year:"Metai",month:"Mėnuo",week:"Savaitė",day:"Diena",list:"Darbotvarkė"},weekText:"SAV",allDayText:"Visą dieną",moreLinkText:"daugiau",noEventsText:"Nėra įvykių rodyti"},{code:"lv",week:{dow:1,doy:4},buttonText:{prev:"Iepr.",next:"Nāk.",today:"Šodien",year:"Gads",month:"Mēnesis",week:"Nedēļa",day:"Diena",list:"Dienas kārtība"},weekText:"Ned.",allDayText:"Visu dienu",moreLinkText(e){return"+vēl "+e},noEventsText:"Nav notikumu"},{code:"mk",buttonText:{prev:"претходно",next:"следно",today:"Денес",year:"година",month:"Месец",week:"Недела",day:"Ден",list:"График"},weekText:"Сед",allDayText:"Цел ден",moreLinkText(e){return"+повеќе "+e},noEventsText:"Нема настани за прикажување"},{code:"ms",week:{dow:1,doy:7},buttonText:{prev:"Sebelum",next:"Selepas",today:"hari ini",year:"Tahun",month:"Bulan",week:"Minggu",day:"Hari",list:"Agenda"},weekText:"Mg",allDayText:"Sepanjang hari",moreLinkText(e){return"masih ada "+e+" acara"},noEventsText:"Tiada peristiwa untuk dipaparkan"},{code:"nb",week:{dow:1,doy:4},buttonText:{prev:"Forrige",next:"Neste",today:"I dag",year:"År",month:"Måned",week:"Uke",day:"Dag",list:"Agenda"},weekText:"Uke",weekTextLong:"Uke",allDayText:"Hele dagen",moreLinkText:"til",noEventsText:"Ingen hendelser å vise",buttonHints:{prev:"Forrige $0",next:"Neste $0",today:"Nåværende $0"},viewHint:"$0 visning",navLinkHint:"Gå til $0",moreLinkHint(e){return`Vis ${e} flere hendelse${1===e?"":"r"}`}},{code:"ne",week:{dow:7,doy:1},buttonText:{prev:"अघिल्लो",next:"अर्को",today:"आज",year:"वर्ष",month:"महिना",week:"हप्ता",day:"दिन",list:"सूची"},weekText:"हप्ता",allDayText:"दिनभरि",moreLinkText:"थप लिंक",noEventsText:"देखाउनको लागि कुनै घटनाहरू छैनन्"},{code:"nl",week:{dow:1,doy:4},buttonText:{prev:"Vorige",next:"Volgende",today:"Vandaag",year:"Jaar",month:"Maand",week:"Week",day:"Dag",list:"Lijst"},allDayText:"Hele dag",moreLinkText:"extra",noEventsText:"Geen evenementen om te laten zien"},{code:"nn",week:{dow:1,doy:4},buttonText:{prev:"Førre",next:"Neste",today:"I dag",year:"År",month:"Månad",week:"Veke",day:"Dag",list:"Agenda"},weekText:"Veke",allDayText:"Heile dagen",moreLinkText:"til",noEventsText:"Ingen hendelser å vise"},{code:"pl",week:{dow:1,doy:4},buttonText:{prev:"Poprzedni",next:"Następny",today:"Dziś",year:"Rok",month:"Miesiąc",week:"Tydzień",day:"Dzień",list:"Plan dnia"},weekText:"Tydz",allDayText:"Cały dzień",moreLinkText:"więcej",noEventsText:"Brak wydarzeń do wyświetlenia"},{code:"pt-br",buttonText:{prev:"Anterior",next:"Próximo",prevYear:"Ano anterior",nextYear:"Próximo ano",year:"Ano",today:"Hoje",month:"Mês",week:"Semana",day:"Dia",list:"Lista"},buttonHints:{prev:"$0 Anterior",next:"Próximo $0",today(e){return"Dia"===e?"Hoje":("Semana"===e?"Esta":"Este")+" "+e.toLocaleLowerCase()}},viewHint(e){return"Visualizar "+("Semana"===e?"a":"o")+" "+e.toLocaleLowerCase()},weekText:"Sm",weekTextLong:"Semana",allDayText:"dia inteiro",moreLinkText(e){return"mais +"+e},moreLinkHint(e){return`Mostrar mais ${e} eventos`},noEventsText:"Não há eventos para mostrar",navLinkHint:"Ir para $0",closeHint:"Fechar",timeHint:"A hora",eventHint:"Evento"},{code:"pt",week:{dow:1,doy:4},buttonText:{prev:"Anterior",next:"Seguinte",today:"Hoje",year:"Ano",month:"Mês",week:"Semana",day:"Dia",list:"Agenda"},weekText:"Sem",allDayText:"Todo o dia",moreLinkText:"mais",noEventsText:"Não há eventos para mostrar"},{code:"ro",week:{dow:1,doy:7},buttonText:{prev:"precedentă",next:"următoare",today:"Azi",year:"An",month:"Lună",week:"Săptămână",day:"Zi",list:"Agendă"},weekText:"Săpt",allDayText:"Toată ziua",moreLinkText(e){return"+alte "+e},noEventsText:"Nu există evenimente de afișat"},{code:"ru",week:{dow:1,doy:4},buttonText:{prev:"Пред",next:"След",today:"Сегодня",year:"Год",month:"Месяц",week:"Неделя",day:"День",list:"Повестка дня"},weekText:"Нед",allDayText:"Весь день",moreLinkText(e){return"+ ещё "+e},noEventsText:"Нет событий для отображения"},{code:"si-lk",week:{dow:1,doy:4},buttonText:{prev:"පෙර",next:"පසු",today:"අද",year:"අවුරුදු",month:"මාසය",week:"සතිය",day:"දවස",list:"ලැයිස්තුව"},weekText:"සති",allDayText:"සියලු",moreLinkText:"තවත්",noEventsText:"මුකුත් නැත"},{code:"sk",week:{dow:1,doy:4},buttonText:{prev:"Predchádzajúci",next:"Nasledujúci",today:"Dnes",year:"Rok",month:"Mesiac",week:"Týždeň",day:"Deň",list:"Rozvrh"},weekText:"Ty",allDayText:"Celý deň",moreLinkText(e){return"+ďalšie: "+e},noEventsText:"Žiadne akcie na zobrazenie"},{code:"sl",week:{dow:1,doy:7},buttonText:{prev:"Prejšnji",next:"Naslednji",today:"Trenutni",year:"Leto",month:"Mesec",week:"Teden",day:"Dan",list:"Dnevni red"},weekText:"Teden",allDayText:"Ves dan",moreLinkText:"več",noEventsText:"Ni dogodkov za prikaz"},{code:"sm",buttonText:{prev:"Talu ai",next:"Mulimuli atu",today:"Aso nei",year:"Tausaga",month:"Masina",week:"Vaiaso",day:"Aso",list:"Faasologa"},weekText:"Vaiaso",allDayText:"Aso atoa",moreLinkText:"sili atu",noEventsText:"Leai ni mea na tutupu"},{code:"sq",week:{dow:1,doy:4},buttonText:{prev:"mbrapa",next:"Përpara",today:"Sot",year:"Viti",month:"Muaj",week:"Javë",day:"Ditë",list:"Listë"},weekText:"Ja",allDayText:"Gjithë ditën",moreLinkText(e){return"+më tepër "+e},noEventsText:"Nuk ka evente për të shfaqur"},{code:"sr-cyrl",week:{dow:1,doy:7},buttonText:{prev:"Претходна",next:"следећи",today:"Данас",year:"Година",month:"Месец",week:"Недеља",day:"Дан",list:"Планер"},weekText:"Сед",allDayText:"Цео дан",moreLinkText(e){return"+ још "+e},noEventsText:"Нема догађаја за приказ"},{code:"sr",week:{dow:1,doy:7},buttonText:{prev:"Prethodna",next:"Sledeći",today:"Danas",year:"Godina",month:"Mеsеc",week:"Nеdеlja",day:"Dan",list:"Planеr"},weekText:"Sed",allDayText:"Cеo dan",moreLinkText(e){return"+ još "+e},noEventsText:"Nеma događaja za prikaz"},{code:"sv",week:{dow:1,doy:4},buttonText:{prev:"Förra",next:"Nästa",today:"Idag",year:"År",month:"Månad",week:"Vecka",day:"Dag",list:"Program"},buttonHints:{prev(e){return`Föregående ${e.toLocaleLowerCase()}`},next(e){return`Nästa ${e.toLocaleLowerCase()}`},today(e){return("Program"===e?"Detta":"Denna")+" "+e.toLocaleLowerCase()}},viewHint:"$0 vy",navLinkHint:"Gå till $0",moreLinkHint(e){return`Visa ytterligare ${e} händelse${1===e?"":"r"}`},weekText:"v.",weekTextLong:"Vecka",allDayText:"Heldag",moreLinkText:"till",noEventsText:"Inga händelser att visa",closeHint:"Stäng",timeHint:"Klockan",eventHint:"Händelse"},{code:"ta-in",week:{dow:1,doy:4},buttonText:{prev:"முந்தைய",next:"அடுத்தது",today:"இன்று",year:"ஆண்டு",month:"மாதம்",week:"வாரம்",day:"நாள்",list:"தினசரி அட்டவணை"},weekText:"வாரம்",allDayText:"நாள் முழுவதும்",moreLinkText(e){return"+ மேலும் "+e},noEventsText:"காண்பிக்க நிகழ்வுகள் இல்லை"},{code:"th",week:{dow:1,doy:4},buttonText:{prev:"ก่อนหน้า",next:"ถัดไป",prevYear:"ปีก่อนหน้า",nextYear:"ปีถัดไป",year:"ปี",today:"วันนี้",month:"เดือน",week:"สัปดาห์",day:"วัน",list:"กำหนดการ"},weekText:"สัปดาห์",allDayText:"ตลอดวัน",moreLinkText:"เพิ่มเติม",noEventsText:"ไม่มีกิจกรรมที่จะแสดง"},{code:"tr",week:{dow:1,doy:7},buttonText:{prev:"geri",next:"ileri",today:"bugün",year:"Yıl",month:"Ay",week:"Hafta",day:"Gün",list:"Ajanda"},weekText:"Hf",allDayText:"Tüm gün",moreLinkText:"daha fazla",noEventsText:"Gösterilecek etkinlik yok"},{code:"ug",buttonText:{prev:"ئالدىنقى",next:"كېيىنكى",today:"بۈگۈن",year:"يىل",month:"ئاي",week:"ھەپتە",day:"كۈن",list:"كۈنتەرتىپ"},allDayText:"پۈتۈن كۈن"},{code:"uk",week:{dow:1,doy:7},buttonText:{prev:"Попередній",next:"далі",today:"Сьогодні",year:"рік",month:"Місяць",week:"Тиждень",day:"День",list:"Порядок денний"},weekText:"Тиж",allDayText:"Увесь день",moreLinkText(e){return"+ще "+e+"..."},noEventsText:"Немає подій для відображення"},{code:"uz-cy",week:{dow:1,doy:4},buttonText:{prev:"Олин",next:"Кейин",today:"Бугун",month:"Ой",week:"Ҳафта",day:"Кун",list:"Кун тартиби"},weekText:"Ҳафта",allDayText:"Кун бўйича",moreLinkText(e){return"+ яна "+e},noEventsText:"Кўрсатиш учун воқеалар йўқ"},{code:"uz",week:{dow:1,doy:4},buttonText:{prev:"Oldingi",next:"Keyingi",today:"Bugun",year:"Yil",month:"Oy",week:"Xafta",day:"Kun",list:"Kun tartibi"},allDayText:"Kun bo'yi",moreLinkText(e){return"+ yana "+e},noEventsText:"Ko'rsatish uchun voqealar yo'q"},{code:"vi",week:{dow:1,doy:4},buttonText:{prev:"Trước",next:"Tiếp",today:"Hôm nay",year:"Năm",month:"Tháng",week:"Tuần",day:"Ngày",list:"Lịch biểu"},weekText:"Tu",allDayText:"Cả ngày",moreLinkText(e){return"+ thêm "+e},noEventsText:"Không có sự kiện để hiển thị"},{code:"zh-cn",week:{dow:1,doy:4},buttonText:{prev:"上月",next:"下月",today:"今天",year:"年",month:"月",week:"周",day:"日",list:"日程"},weekText:"周",allDayText:"全天",moreLinkText(e){return"另外 "+e+" 个"},noEventsText:"没有事件显示"},{code:"zh-tw",buttonText:{prev:"上個",next:"下個",today:"今天",year:"年",month:"月",week:"週",day:"天",list:"活動列表"},weekText:"週",allDayText:"整天",moreLinkText:"顯示更多",noEventsText:"沒有任何活動"}]},35325:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{iz:function(){return P}});n(52247),n(71695),n(92745),n(19423),n(19134),n(5740),n(44495),n(39527),n(99790),n(13334),n(47021);var i=n(66952),o=n(65054),s=e([i]);i=(s.then?(await s)():s)[0];i.be;function l(e,t){let n=[];for(let r=0;r<t;r+=1)n[r]=[];for(let r of e)n[r.row].push(r);return n}function c(e,t){let n=[];for(let r=0;r<t;r+=1)n[r]=[];for(let r of e)n[r.firstCol].push(r);return n}function d(e,t){let n=[];if(e){for(let r=0;r<t;r+=1)n[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(let t of e.segs)n[t.row].segs.push(t)}else for(let r=0;r<t;r+=1)n[r]=null;return n}const u=(0,i.x)({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function h(e){let{display:t}=e.eventRange.ui;return"list-item"===t||"auto"===t&&!e.eventRange.def.allDay&&e.firstCol===e.lastCol&&e.isStart&&e.isEnd}class f extends i.B{render(){let{props:e}=this;return(0,o.az)(i.cj,Object.assign({},e,{elClasses:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:u,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))}}class g extends i.B{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r}=e,s=n.eventTimeFormat||u,a=(0,i.bQ)(r,s,t,!0,e.defaultDisplayEventEnd);return(0,o.az)(i.cn,Object.assign({},e,{elTag:"a",elClasses:["fc-daygrid-event","fc-daygrid-dot-event"],elAttrs:(0,i.bU)(e.seg,t),defaultGenerator:p,timeText:a,isResizing:!1,isDateSelecting:!1}))}}function p(e){return(0,o.az)(o.HY,null,(0,o.az)("div",{className:"fc-daygrid-event-dot",style:{borderColor:e.borderColor||e.backgroundColor}}),e.timeText&&(0,o.az)("div",{className:"fc-event-time"},e.timeText),(0,o.az)("div",{className:"fc-event-title"},e.event.title||(0,o.az)(o.HY,null," ")))}class m extends i.B{constructor(){super(...arguments),this.compileSegs=(0,i.z)(v)}render(){let{props:e}=this,{allSegs:t,invisibleSegs:n}=this.compileSegs(e.singlePlacements);return(0,o.az)(i.cr,{elClasses:["fc-daygrid-more-link"],dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:t,hiddenSegs:n,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:()=>{let n=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return(0,o.az)(o.HY,null,t.map((t=>{let r=t.eventRange.instance.instanceId;return(0,o.az)("div",{className:"fc-daygrid-event-harness",key:r,style:{visibility:n[r]?"hidden":""}},h(t)?(0,o.az)(g,Object.assign({seg:t,isDragging:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},(0,i.bS)(t,e.todayRange))):(0,o.az)(f,Object.assign({seg:t,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},(0,i.bS)(t,e.todayRange))))})))}})}}function v(e){let t=[],n=[];for(let r of e)t.push(r.seg),r.isVisible||n.push(r.seg);return{allSegs:t,invisibleSegs:n}}const y=(0,i.x)({week:"narrow"});class b extends i.be{constructor(){super(...arguments),this.rootElRef=(0,o.Vf)(),this.state={dayNumberId:(0,i.a5)()},this.handleRootEl=e=>{(0,i.Y)(this.rootElRef,e),(0,i.Y)(this.props.elRef,e)}}render(){let{context:e,props:t,state:n,rootElRef:r}=this,{options:s,dateEnv:a}=e,{date:l,dateProfile:c}=t;const d=t.showDayNumber&&E(l,c.currentRange,a);return(0,o.az)(i.cl,{elTag:"td",elRef:this.handleRootEl,elClasses:["fc-daygrid-day",...t.extraClassNames||[]],elAttrs:Object.assign(Object.assign(Object.assign({},t.extraDataAttrs),t.showDayNumber?{"aria-labelledby":n.dayNumberId}:{}),{role:"gridcell"}),defaultGenerator:w,date:l,dateProfile:c,todayRange:t.todayRange,showDayNumber:t.showDayNumber,isMonthStart:d,extraRenderProps:t.extraRenderProps},((a,c)=>(0,o.az)("div",{ref:t.innerElRef,className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",style:{minHeight:t.minHeight}},t.showWeekNumber&&(0,o.az)(i.cq,{elTag:"a",elClasses:["fc-daygrid-week-number"],elAttrs:(0,i.b0)(e,l,"week"),date:l,defaultFormat:y}),!c.isDisabled&&(t.showDayNumber||(0,i.cm)(s)||t.forceDayTop)?(0,o.az)("div",{className:"fc-daygrid-day-top"},(0,o.az)(a,{elTag:"a",elClasses:["fc-daygrid-day-number",d&&"fc-daygrid-month-start"],elAttrs:Object.assign(Object.assign({},(0,i.b0)(e,l)),{id:n.dayNumberId})})):t.showDayNumber?(0,o.az)("div",{className:"fc-daygrid-day-top",style:{visibility:"hidden"}},(0,o.az)("a",{className:"fc-daygrid-day-number"}," ")):void 0,(0,o.az)("div",{className:"fc-daygrid-day-events",ref:t.fgContentElRef},t.fgContent,(0,o.az)("div",{className:"fc-daygrid-day-bottom",style:{marginTop:t.moreMarginTop}},(0,o.az)(m,{allDayDate:l,singlePlacements:t.singlePlacements,moreCnt:t.moreCnt,alignmentElRef:r,alignGridTop:!t.showDayNumber,extraDateSpan:t.extraDateSpan,dateProfile:t.dateProfile,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,todayRange:t.todayRange}))),(0,o.az)("div",{className:"fc-daygrid-day-bg"},t.bgContent))))}}function w(e){return e.dayNumberText||(0,o.az)(o.HY,null," ")}function E(e,t,n){const{start:r,end:o}=t,s=(0,i.bg)(o,-1),a=n.getYear(r),l=n.getMonth(r),c=n.getYear(s),d=n.getMonth(s);return!(a===c&&l===d)&&Boolean(e.valueOf()===r.valueOf()||1===n.getDay(e)&&e.valueOf()<o.valueOf())}function A(e){return e.eventRange.instance.instanceId+":"+e.firstCol}function x(e){return A(e)+":"+e.lastCol}function D(e,t,n,r,i,o,s){let a=new C((t=>{let n=e[t.index].eventRange.instance.instanceId+":"+t.span.start+":"+(t.span.end-1);return i[n]||1}));a.allowReslicing=!0,a.strictOrder=r,!0===t||!0===n?(a.maxCoord=o,a.hiddenConsumes=!0):"number"==typeof t?a.maxStackCnt=t:"number"==typeof n&&(a.maxStackCnt=n,a.hiddenConsumes=!0);let l=[],c=[];for(let v=0;v<e.length;v+=1){let t=e[v],n=x(t);null!=i[n]?l.push({index:v,span:{start:t.firstCol,end:t.lastCol+1}}):c.push(t)}let d=a.addSegs(l),u=a.toRects(),{singleColPlacements:h,multiColPlacements:f,leftoverMargins:g}=T(u,e,s),p=[],m=[];for(let v of c){f[v.firstCol].push({seg:v,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let e=v.firstCol;e<=v.lastCol;e+=1)h[e].push({seg:k(v,e,e+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let v=0;v<s.length;v+=1)p.push(0);for(let v of d){let t=e[v.index],n=v.span;f[n.start].push({seg:k(t,n.start,n.end,s),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let e=n.start;e<n.end;e+=1)p[e]+=1,h[e].push({seg:k(t,e,e+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let v=0;v<s.length;v+=1)m.push(g[v]);return{singleColPlacements:h,multiColPlacements:f,moreCnts:p,moreMarginTops:m}}function T(e,t,n){let r=S(e,n.length),i=[],o=[],s=[];for(let a=0;a<n.length;a+=1){let e=r[a],l=[],c=0,d=0;for(let r of e){let e=t[r.index];l.push({seg:k(e,a,a+1,n),isVisible:!0,isAbsolute:!1,absoluteTop:r.levelCoord,marginTop:r.levelCoord-c}),c=r.levelCoord+r.thickness}let u=[];c=0,d=0;for(let r of e){let e=t[r.index],i=r.span.end-r.span.start>1,o=r.span.start===a;d+=r.levelCoord-c,c=r.levelCoord+r.thickness,i?(d+=r.thickness,o&&u.push({seg:k(e,r.span.start,r.span.end,n),isVisible:!0,isAbsolute:!0,absoluteTop:r.levelCoord,marginTop:0})):o&&(u.push({seg:k(e,r.span.start,r.span.end,n),isVisible:!0,isAbsolute:!1,absoluteTop:r.levelCoord,marginTop:d}),d=0)}i.push(l),o.push(u),s.push(d)}return{singleColPlacements:i,multiColPlacements:o,leftoverMargins:s}}function S(e,t){let n=[];for(let r=0;r<t;r+=1)n.push([]);for(let r of e)for(let e=r.span.start;e<r.span.end;e+=1)n[e].push(r);return n}function k(e,t,n,r){if(e.firstCol===t&&e.lastCol===n-1)return e;let o=e.eventRange,s=o.range,a=(0,i.o)(s,{start:r[t].date,end:(0,i.t)(r[n-1].date,1)});return Object.assign(Object.assign({},e),{firstCol:t,lastCol:n-1,eventRange:{def:o.def,ui:Object.assign(Object.assign({},o.ui),{durationEditable:!1}),instance:o.instance,range:a},isStart:e.isStart&&a.start.valueOf()===s.start.valueOf(),isEnd:e.isEnd&&a.end.valueOf()===s.end.valueOf()})}class C extends i.bA{constructor(){super(...arguments),this.hiddenConsumes=!1,this.forceHidden={}}addSegs(e){const t=super.addSegs(e),{entriesByLevel:n}=this,r=e=>!this.forceHidden[(0,i.bB)(e)];for(let i=0;i<n.length;i+=1)n[i]=n[i].filter(r);return t}handleInvalidInsertion(e,t,n){const{entriesByLevel:r,forceHidden:o}=this,{touchingEntry:s,touchingLevel:a,touchingLateral:l}=e;if(this.hiddenConsumes&&s){const e=(0,i.bB)(s);if(!o[e])if(this.allowReslicing){const e=Object.assign(Object.assign({},s),{span:(0,i.bF)(s.span,t.span)});o[(0,i.bB)(e)]=!0,r[a][l]=e,n.push(e),this.splitEntry(s,t,n)}else o[e]=!0,n.push(s)}super.handleInvalidInsertion(e,t,n)}}class _ extends i.be{constructor(){super(...arguments),this.cellElRefs=new i.cf,this.frameElRefs=new i.cf,this.fgElRefs=new i.cf,this.segHarnessRefs=new i.cf,this.rootElRef=(0,o.Vf)(),this.state={framePositions:null,maxContentHeight:null,segHeights:{}},this.handleResize=e=>{e&&this.updateSizing(!0)}}render(){let{props:e,state:t,context:n}=this,{options:r}=n,s=e.cells.length,a=c(e.businessHourSegs,s),l=c(e.bgEventSegs,s),d=c(this.getHighlightSegs(),s),u=c(this.getMirrorSegs(),s),{singleColPlacements:h,multiColPlacements:f,moreCnts:g,moreMarginTops:p}=D((0,i.bR)(e.fgEventSegs,r.eventOrder),e.dayMaxEvents,e.dayMaxEventRows,r.eventOrderStrict,t.segHeights,t.maxContentHeight,e.cells),m=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{};return(0,o.az)("tr",{ref:this.rootElRef,role:"row"},e.renderIntro&&e.renderIntro(),e.cells.map(((t,n)=>{let r=this.renderFgSegs(n,e.forPrint?h[n]:f[n],e.todayRange,m),i=this.renderFgSegs(n,R(u[n],f),e.todayRange,{},Boolean(e.eventDrag),Boolean(e.eventResize),!1);return(0,o.az)(b,{key:t.key,elRef:this.cellElRefs.createRef(t.key),innerElRef:this.frameElRefs.createRef(t.key),dateProfile:e.dateProfile,date:t.date,showDayNumber:e.showDayNumbers,showWeekNumber:e.showWeekNumbers&&0===n,forceDayTop:e.showWeekNumbers,todayRange:e.todayRange,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,extraRenderProps:t.extraRenderProps,extraDataAttrs:t.extraDataAttrs,extraClassNames:t.extraClassNames,extraDateSpan:t.extraDateSpan,moreCnt:g[n],moreMarginTop:p[n],singlePlacements:h[n],fgContentElRef:this.fgElRefs.createRef(t.key),fgContent:(0,o.az)(o.HY,null,(0,o.az)(o.HY,null,r),(0,o.az)(o.HY,null,i)),bgContent:(0,o.az)(o.HY,null,this.renderFillSegs(d[n],"highlight"),this.renderFillSegs(a[n],"non-business"),this.renderFillSegs(l[n],"bg-event")),minHeight:e.cellMinHeight})})))}componentDidMount(){this.updateSizing(!0),this.context.addResizeHandler(this.handleResize)}componentDidUpdate(e,t){let n=this.props;this.updateSizing(!(0,i.E)(e,n))}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}getHighlightSegs(){let{props:e}=this;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs}getMirrorSegs(){let{props:e}=this;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]}renderFgSegs(e,t,n,r,s,a,l){let{context:c}=this,{eventSelection:d}=this.props,{framePositions:u}=this.state,p=1===this.props.cells.length,m=s||a||l,v=[];if(u)for(let y of t){let{seg:e}=y,{instanceId:t}=e.eventRange.instance,b=y.isVisible&&!r[t],w=y.isAbsolute,E="",D="";w&&(c.isRtl?(D=0,E=u.lefts[e.lastCol]-u.lefts[e.firstCol]):(E=0,D=u.rights[e.firstCol]-u.rights[e.lastCol])),v.push((0,o.az)("div",{className:"fc-daygrid-event-harness"+(w?" fc-daygrid-event-harness-abs":""),key:A(e),ref:m?null:this.segHarnessRefs.createRef(x(e)),style:{visibility:b?"":"hidden",marginTop:w?"":y.marginTop,top:w?y.absoluteTop:"",left:E,right:D}},h(e)?(0,o.az)(g,Object.assign({seg:e,isDragging:s,isSelected:t===d,defaultDisplayEventEnd:p},(0,i.bS)(e,n))):(0,o.az)(f,Object.assign({seg:e,isDragging:s,isResizing:a,isDateSelecting:l,isSelected:t===d,defaultDisplayEventEnd:p},(0,i.bS)(e,n)))))}return v}renderFillSegs(e,t){let{isRtl:n}=this.context,{todayRange:r}=this.props,{framePositions:s}=this.state,a=[];if(s)for(let l of e){let e=n?{right:0,left:s.lefts[l.lastCol]-s.lefts[l.firstCol]}:{left:0,right:s.rights[l.firstCol]-s.rights[l.lastCol]};a.push((0,o.az)("div",{key:(0,i.bT)(l.eventRange),className:"fc-daygrid-bg-harness",style:e},"bg-event"===t?(0,o.az)(i.cp,Object.assign({seg:l},(0,i.bS)(l,r))):(0,i.co)(t)))}return(0,o.az)(o.HY,{},...a)}updateSizing(e){let{props:t,state:n,frameElRefs:r}=this;if(!t.forPrint&&null!==t.clientWidth){if(e){let e=t.cells.map((e=>r.currentMap[e.key]));if(e.length){let t=this.rootElRef.current,r=new i.ba(t,e,!0,!1);n.framePositions&&n.framePositions.similarTo(r)||this.setState({framePositions:new i.ba(t,e,!0,!1)})}}const o=this.state.segHeights,s=this.querySegHeights(),a=!0===t.dayMaxEvents||!0===t.dayMaxEventRows;this.safeSetState({segHeights:Object.assign(Object.assign({},o),s),maxContentHeight:a?this.computeMaxContentHeight():null})}}querySegHeights(){let e=this.segHarnessRefs.currentMap,t={};for(let n in e){let r=Math.round(e[n].getBoundingClientRect().height);t[n]=Math.max(t[n]||0,r)}return t}computeMaxContentHeight(){let e=this.props.cells[0].key,t=this.cellElRefs.currentMap[e],n=this.fgElRefs.currentMap[e];return t.getBoundingClientRect().bottom-n.getBoundingClientRect().top}getCellEls(){let e=this.cellElRefs.currentMap;return this.props.cells.map((t=>e[t.key]))}}function R(e,t){if(!e.length)return[];let n=M(t);return e.map((e=>({seg:e,isVisible:!0,isAbsolute:!0,absoluteTop:n[e.eventRange.instance.instanceId],marginTop:0})))}function M(e){let t={};for(let n of e)for(let e of n)t[e.seg.eventRange.instance.instanceId]=e.absoluteTop;return t}_.addStateEquality({segHeights:i.E});class I extends i.be{constructor(){super(...arguments),this.splitBusinessHourSegs=(0,i.z)(l),this.splitBgEventSegs=(0,i.z)(l),this.splitFgEventSegs=(0,i.z)(l),this.splitDateSelectionSegs=(0,i.z)(l),this.splitEventDrag=(0,i.z)(d),this.splitEventResize=(0,i.z)(d),this.rowRefs=new i.cf}render(){let{props:e,context:t}=this,n=e.cells.length,r=this.splitBusinessHourSegs(e.businessHourSegs,n),s=this.splitBgEventSegs(e.bgEventSegs,n),a=this.splitFgEventSegs(e.fgEventSegs,n),l=this.splitDateSelectionSegs(e.dateSelectionSegs,n),c=this.splitEventDrag(e.eventDrag,n),d=this.splitEventResize(e.eventResize,n),u=n>=7&&e.clientWidth?e.clientWidth/t.options.aspectRatio/6:null;return(0,o.az)(i.ch,{unit:"day"},((t,i)=>(0,o.az)(o.HY,null,e.cells.map(((t,h)=>(0,o.az)(_,{ref:this.rowRefs.createRef(h),key:t.length?t[0].date.toISOString():h,showDayNumbers:n>1,showWeekNumbers:e.showWeekNumbers,todayRange:i,dateProfile:e.dateProfile,cells:t,renderIntro:e.renderRowIntro,businessHourSegs:r[h],eventSelection:e.eventSelection,bgEventSegs:s[h].filter(N),fgEventSegs:a[h],dateSelectionSegs:l[h],eventDrag:c[h],eventResize:d[h],dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,clientWidth:e.clientWidth,clientHeight:e.clientHeight,cellMinHeight:u,forPrint:e.forPrint}))))))}componentDidMount(){this.registerInteractiveComponent()}componentDidUpdate(){this.registerInteractiveComponent()}registerInteractiveComponent(){if(!this.rootEl){const e=this.rowRefs.currentMap[0].getCellEls()[0],t=e?e.closest(".fc-daygrid-body"):null;t&&(this.rootEl=t,this.context.registerInteractiveComponent(this,{el:t,isHitComboAllowed:this.props.isHitComboAllowed}))}}componentWillUnmount(){this.rootEl&&(this.context.unregisterInteractiveComponent(this),this.rootEl=null)}prepareHits(){this.rowPositions=new i.ba(this.rootEl,this.rowRefs.collect().map((e=>e.getCellEls()[0])),!1,!0),this.colPositions=new i.ba(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)}queryHit(e,t){let{colPositions:n,rowPositions:r}=this,i=n.leftToIndex(e),o=r.topToIndex(t);if(null!=o&&null!=i){let e=this.props.cells[o][i];return{dateProfile:this.props.dateProfile,dateSpan:Object.assign({range:this.getCellRange(o,i),allDay:!0},e.extraDateSpan),dayEl:this.getCellEl(o,i),rect:{left:n.lefts[i],right:n.rights[i],top:r.tops[o],bottom:r.bottoms[o]},layer:0}}return null}getCellEl(e,t){return this.rowRefs.currentMap[e].getCellEls()[t]}getCellRange(e,t){let n=this.props.cells[e][t].date;return{start:n,end:(0,i.t)(n,1)}}}function N(e){return e.eventRange.def.allDay}class O extends i.be{constructor(){super(...arguments),this.elRef=(0,o.Vf)(),this.needsScrollReset=!1}render(){let{props:e}=this,{dayMaxEventRows:t,dayMaxEvents:n,expandRows:r}=e,i=!0===n||!0===t;i&&!r&&(i=!1,t=null,n=null);let s=["fc-daygrid-body",i?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",r?"":"fc-daygrid-body-natural"];return(0,o.az)("div",{ref:this.elRef,className:s.join(" "),style:{width:e.clientWidth,minWidth:e.tableMinWidth}},(0,o.az)("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:e.clientWidth,minWidth:e.tableMinWidth,height:r?e.clientHeight:""}},e.colGroupNode,(0,o.az)("tbody",{role:"presentation"},(0,o.az)(I,{dateProfile:e.dateProfile,cells:e.cells,renderRowIntro:e.renderRowIntro,showWeekNumbers:e.showWeekNumbers,clientWidth:e.clientWidth,clientHeight:e.clientHeight,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,dayMaxEvents:n,dayMaxEventRows:t,forPrint:e.forPrint,isHitComboAllowed:e.isHitComboAllowed}))))}componentDidMount(){this.requestScrollReset()}componentDidUpdate(e){e.dateProfile!==this.props.dateProfile?this.requestScrollReset():this.flushScrollReset()}requestScrollReset(){this.needsScrollReset=!0,this.flushScrollReset()}flushScrollReset(){if(this.needsScrollReset&&this.props.clientWidth){const e=z(this.elRef.current,this.props.dateProfile);if(e){const t=e.closest(".fc-daygrid-body"),n=t.closest(".fc-scroller"),r=e.getBoundingClientRect().top-t.getBoundingClientRect().top;n.scrollTop=r?r+1:0}this.needsScrollReset=!1}}}function z(e,t){let n;return t.currentRangeUnit.match(/year|month/)&&(n=e.querySelector(`[data-date="${(0,i.bx)(t.currentDate)}-01"]`)),n||(n=e.querySelector(`[data-date="${(0,i.bv)(t.currentDate)}"]`)),n}class H extends i.bW{constructor(){super(...arguments),this.forceDayIfListItem=!0}sliceRange(e,t){return t.sliceRange(e)}}class P extends i.be{constructor(){super(...arguments),this.slicer=new H,this.tableRef=(0,o.Vf)()}render(){let{props:e,context:t}=this;return(0,o.az)(O,Object.assign({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,t,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))}}(0,i.cw)(':root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:"";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:"";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}'),r()}catch(a){r(a)}}))},70001:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{ZP:function(){return a}});n(71695),n(92745),n(61893),n(19423),n(13334),n(88972),n(47021);var i=n(95559),o=n(66952),s=e([o,i]);[o,i]=s.then?(await s)():s,o.bI.touchMouseIgnoreWait=500;let c=0,d=0,u=!1;class h{constructor(e){this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=e=>{if(!this.shouldIgnoreMouse()&&f(e)&&this.tryStart(e)){let t=this.createEventFromMouse(e,!0);this.emitter.trigger("pointerdown",t),this.initScrollWatch(t),this.shouldIgnoreMove||document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp)}},this.handleMouseMove=e=>{let t=this.createEventFromMouse(e);this.recordCoords(t),this.emitter.trigger("pointermove",t)},this.handleMouseUp=e=>{document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp),this.emitter.trigger("pointerup",this.createEventFromMouse(e)),this.cleanup()},this.handleTouchStart=e=>{if(this.tryStart(e)){this.isTouchDragging=!0;let t=this.createEventFromTouch(e,!0);this.emitter.trigger("pointerdown",t),this.initScrollWatch(t);let n=e.target;this.shouldIgnoreMove||n.addEventListener("touchmove",this.handleTouchMove),n.addEventListener("touchend",this.handleTouchEnd),n.addEventListener("touchcancel",this.handleTouchEnd),window.addEventListener("scroll",this.handleTouchScroll,!0)}},this.handleTouchMove=e=>{let t=this.createEventFromTouch(e);this.recordCoords(t),this.emitter.trigger("pointermove",t)},this.handleTouchEnd=e=>{if(this.isDragging){let t=e.target;t.removeEventListener("touchmove",this.handleTouchMove),t.removeEventListener("touchend",this.handleTouchEnd),t.removeEventListener("touchcancel",this.handleTouchEnd),window.removeEventListener("scroll",this.handleTouchScroll,!0),this.emitter.trigger("pointerup",this.createEventFromTouch(e)),this.cleanup(),this.isTouchDragging=!1,g()}},this.handleTouchScroll=()=>{this.wasTouchScroll=!0},this.handleScroll=e=>{if(!this.shouldIgnoreMove){let t=window.scrollX-this.prevScrollX+this.prevPageX,n=window.scrollY-this.prevScrollY+this.prevPageY;this.emitter.trigger("pointermove",{origEvent:e,isTouch:this.isTouchDragging,subjectEl:this.subjectEl,pageX:t,pageY:n,deltaX:t-this.origPageX,deltaY:n-this.origPageY})}},this.containerEl=e,this.emitter=new o.F,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),p()}destroy(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),m()}tryStart(e){let t=this.querySubjectEl(e),n=e.target;return!(!t||this.handleSelector&&!(0,o.$)(n,this.handleSelector))&&(this.subjectEl=t,this.isDragging=!0,this.wasTouchScroll=!1,!0)}cleanup(){u=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()}querySubjectEl(e){return this.selector?(0,o.$)(e.target,this.selector):this.containerEl}shouldIgnoreMouse(){return c||this.isTouchDragging}cancelTouchScroll(){this.isDragging&&(u=!0)}initScrollWatch(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))}recordCoords(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.scrollX,this.prevScrollY=window.scrollY)}destroyScrollWatch(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)}createEventFromMouse(e,t){let n=0,r=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,r=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:r}}createEventFromTouch(e,t){let n,r,i=e.touches,o=0,s=0;return i&&i.length?(n=i[0].pageX,r=i[0].pageY):(n=e.pageX,r=e.pageY),t?(this.origPageX=n,this.origPageY=r):(o=n-this.origPageX,s=r-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:r,deltaX:o,deltaY:s}}}function f(e){return 0===e.button&&!e.ctrlKey}function g(){c+=1,setTimeout((()=>{c-=1}),o.bI.touchMouseIgnoreWait)}function p(){d+=1,1===d&&window.addEventListener("touchmove",v,{passive:!1})}function m(){d-=1,d||window.removeEventListener("touchmove",v,{passive:!1})}function v(e){u&&e.preventDefault()}class y{constructor(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}start(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.scrollX,this.origScreenY=n-window.scrollY,this.deltaX=0,this.deltaY=0,this.updateElPosition()}handleMove(e,t){this.deltaX=e-window.scrollX-this.origScreenX,this.deltaY=t-window.scrollY-this.origScreenY,this.updateElPosition()}setIsVisible(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)}stop(e,t){let n=()=>{this.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(n,this.revertDuration):setTimeout(n,0)}doRevertAnimation(e,t){let n=this.mirrorEl,r=this.sourceEl.getBoundingClientRect();n.style.transition="top "+t+"ms,left "+t+"ms",(0,o.aP)(n,{left:r.left,top:r.top}),(0,o.b2)(n,(()=>{n.style.transition="",e()}))}cleanup(){this.mirrorEl&&((0,o.aO)(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null}updateElPosition(){this.sourceEl&&this.isVisible&&(0,o.aP)(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})}getMirrorEl(){let e=this.sourceElRect,t=this.mirrorEl;return t||(t=this.mirrorEl=this.sourceEl.cloneNode(!0),t.style.userSelect="none",t.style.webkitUserSelect="none",t.style.pointerEvents="none",t.classList.add("fc-event-dragging"),(0,o.aP)(t,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(t)),t}}class b extends o.bb{constructor(e,t){super(),this.handleScroll=()=>{this.scrollTop=this.scrollController.getScrollTop(),this.scrollLeft=this.scrollController.getScrollLeft(),this.handleScrollChange()},this.scrollController=e,this.doesListening=t,this.scrollTop=this.origScrollTop=e.getScrollTop(),this.scrollLeft=this.origScrollLeft=e.getScrollLeft(),this.scrollWidth=e.getScrollWidth(),this.scrollHeight=e.getScrollHeight(),this.clientWidth=e.getClientWidth(),this.clientHeight=e.getClientHeight(),this.clientRect=this.computeClientRect(),this.doesListening&&this.getEventTarget().addEventListener("scroll",this.handleScroll)}destroy(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)}getScrollTop(){return this.scrollTop}getScrollLeft(){return this.scrollLeft}setScrollTop(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())}setScrollLeft(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())}getClientWidth(){return this.clientWidth}getClientHeight(){return this.clientHeight}getScrollWidth(){return this.scrollWidth}getScrollHeight(){return this.scrollHeight}handleScrollChange(){}}class w extends b{constructor(e,t){super(new o.bc(e),t)}getEventTarget(){return this.scrollController.el}computeClientRect(){return(0,o.b3)(this.scrollController.el)}}class E extends b{constructor(e){super(new o.bd,e)}getEventTarget(){return window}computeClientRect(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}}handleScrollChange(){this.clientRect=this.computeClientRect()}}const A="function"==typeof performance?performance.now:Date.now;class x{constructor(){this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=()=>{if(this.isAnimating){let e=this.computeBestEdge(this.pointerScreenX+window.scrollX,this.pointerScreenY+window.scrollY);if(e){let t=A();this.handleSide(e,(t-this.msSinceRequest)/1e3),this.requestAnimation(t)}else this.isAnimating=!1}}}start(e,t,n){this.isEnabled&&(this.scrollCaches=this.buildCaches(n),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))}handleMove(e,t){if(this.isEnabled){let n=e-window.scrollX,r=t-window.scrollY,i=null===this.pointerScreenY?0:r-this.pointerScreenY,o=null===this.pointerScreenX?0:n-this.pointerScreenX;i<0?this.everMovedUp=!0:i>0&&(this.everMovedDown=!0),o<0?this.everMovedLeft=!0:o>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=r,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(A()))}}stop(){if(this.isEnabled){this.isAnimating=!1;for(let e of this.scrollCaches)e.destroy();this.scrollCaches=null}}requestAnimation(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)}handleSide(e,t){let{scrollCache:n}=e,{edgeThreshold:r}=this,i=r-e.distance,o=i*i/(r*r)*this.maxVelocity*t,s=1;switch(e.name){case"left":s=-1;case"right":n.setScrollLeft(n.getScrollLeft()+o*s);break;case"top":s=-1;case"bottom":n.setScrollTop(n.getScrollTop()+o*s)}}computeBestEdge(e,t){let{edgeThreshold:n}=this,r=null,i=this.scrollCaches||[];for(let o of i){let i=o.clientRect,s=e-i.left,a=i.right-e,l=t-i.top,c=i.bottom-t;s>=0&&a>=0&&l>=0&&c>=0&&(l<=n&&this.everMovedUp&&o.canScrollUp()&&(!r||r.distance>l)&&(r={scrollCache:o,name:"top",distance:l}),c<=n&&this.everMovedDown&&o.canScrollDown()&&(!r||r.distance>c)&&(r={scrollCache:o,name:"bottom",distance:c}),s<=n&&this.everMovedLeft&&o.canScrollLeft()&&(!r||r.distance>s)&&(r={scrollCache:o,name:"left",distance:s}),a<=n&&this.everMovedRight&&o.canScrollRight()&&(!r||r.distance>a)&&(r={scrollCache:o,name:"right",distance:a}))}return r}buildCaches(e){return this.queryScrollEls(e).map((e=>e===window?new E(!1):new w(e,!1)))}queryScrollEls(e){let t=[];for(let n of this.scrollQuery)"object"==typeof n?t.push(n):t.push(...Array.prototype.slice.call(e.getRootNode().querySelectorAll(n)));return t}}class D extends o.bH{constructor(e,t){super(e),this.containerEl=e,this.delay=null,this.minDistance=0,this.touchScrollAllowed=!0,this.mirrorNeedsRevert=!1,this.isInteracting=!1,this.isDragging=!1,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,this.delayTimeoutId=null,this.onPointerDown=e=>{this.isDragging||(this.isInteracting=!0,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,(0,o.ar)(document.body),(0,o.at)(document.body),e.isTouch||e.origEvent.preventDefault(),this.emitter.trigger("pointerdown",e),this.isInteracting&&!this.pointer.shouldIgnoreMove&&(this.mirror.setIsVisible(!1),this.mirror.start(e.subjectEl,e.pageX,e.pageY),this.startDelay(e),this.minDistance||this.handleDistanceSurpassed(e)))},this.onPointerMove=e=>{if(this.isInteracting){if(this.emitter.trigger("pointermove",e),!this.isDistanceSurpassed){let t,n=this.minDistance,{deltaX:r,deltaY:i}=e;t=r*r+i*i,t>=n*n&&this.handleDistanceSurpassed(e)}this.isDragging&&("scroll"!==e.origEvent.type&&(this.mirror.handleMove(e.pageX,e.pageY),this.autoScroller.handleMove(e.pageX,e.pageY)),this.emitter.trigger("dragmove",e))}},this.onPointerUp=e=>{this.isInteracting&&(this.isInteracting=!1,(0,o.as)(document.body),(0,o.au)(document.body),this.emitter.trigger("pointerup",e),this.isDragging&&(this.autoScroller.stop(),this.tryStopDrag(e)),this.delayTimeoutId&&(clearTimeout(this.delayTimeoutId),this.delayTimeoutId=null))};let n=this.pointer=new h(e);n.emitter.on("pointerdown",this.onPointerDown),n.emitter.on("pointermove",this.onPointerMove),n.emitter.on("pointerup",this.onPointerUp),t&&(n.selector=t),this.mirror=new y,this.autoScroller=new x}destroy(){this.pointer.destroy(),this.onPointerUp({})}startDelay(e){"number"==typeof this.delay?this.delayTimeoutId=setTimeout((()=>{this.delayTimeoutId=null,this.handleDelayEnd(e)}),this.delay):this.handleDelayEnd(e)}handleDelayEnd(e){this.isDelayEnded=!0,this.tryStartDrag(e)}handleDistanceSurpassed(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)}tryStartDrag(e){this.isDelayEnded&&this.isDistanceSurpassed&&(this.pointer.wasTouchScroll&&!this.touchScrollAllowed||(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY,this.containerEl),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll()))}tryStopDrag(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))}stopDrag(e){this.isDragging=!1,this.emitter.trigger("dragend",e)}setIgnoreMove(e){this.pointer.shouldIgnoreMove=e}setMirrorIsVisible(e){this.mirror.setIsVisible(e)}setMirrorNeedsRevert(e){this.mirrorNeedsRevert=e}setAutoScrollEnabled(e){this.autoScroller.isEnabled=e}}class T{constructor(e){this.el=e,this.origRect=(0,o.b6)(e),this.scrollCaches=(0,o.b5)(e).map((e=>new w(e,!0)))}destroy(){for(let e of this.scrollCaches)e.destroy()}computeLeft(){let e=this.origRect.left;for(let t of this.scrollCaches)e+=t.origScrollLeft-t.getScrollLeft();return e}computeTop(){let e=this.origRect.top;for(let t of this.scrollCaches)e+=t.origScrollTop-t.getScrollTop();return e}isWithinClipping(e,t){let n={left:e,top:t};for(let r of this.scrollCaches)if(!S(r.getEventTarget())&&!(0,o.aF)(n,r.clientRect))return!1;return!0}}function S(e){let t=e.tagName;return"HTML"===t||"BODY"===t}class k{constructor(e,t){this.useSubjectCenter=!1,this.requireInitial=!0,this.disablePointCheck=!1,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=e=>{let{dragging:t}=this;this.initialHit=null,this.movingHit=null,this.finalHit=null,this.prepareHits(),this.processFirstCoord(e),this.initialHit||!this.requireInitial?(t.setIgnoreMove(!1),this.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=e=>{this.emitter.trigger("dragstart",e),this.handleMove(e,!0)},this.handleDragMove=e=>{this.emitter.trigger("dragmove",e),this.handleMove(e)},this.handlePointerUp=e=>{this.releaseHits(),this.emitter.trigger("pointerup",e)},this.handleDragEnd=e=>{this.movingHit&&this.emitter.trigger("hitupdate",null,!0,e),this.finalHit=this.movingHit,this.movingHit=null,this.emitter.trigger("dragend",e)},this.droppableStore=t,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new o.F}processFirstCoord(e){let t,n={left:e.pageX,top:e.pageY},r=n,i=e.subjectEl;i instanceof HTMLElement&&(t=(0,o.b6)(i),r=(0,o.aG)(r,t));let s=this.initialHit=this.queryHitForOffset(r.left,r.top);if(s){if(this.useSubjectCenter&&t){let e=(0,o.aE)(t,s.rect);e&&(r=(0,o.aH)(e))}this.coordAdjust=(0,o.aI)(r,n)}else this.coordAdjust={left:0,top:0}}handleMove(e,t){let n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);!t&&C(this.movingHit,n)||(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))}prepareHits(){this.offsetTrackers=(0,o.a)(this.droppableStore,(e=>(e.component.prepareHits(),new T(e.el))))}releaseHits(){let{offsetTrackers:e}=this;for(let t in e)e[t].destroy();this.offsetTrackers={}}queryHitForOffset(e,t){let{droppableStore:n,offsetTrackers:r}=this,i=null;for(let s in n){let a=n[s].component,l=r[s];if(l&&l.isWithinClipping(e,t)){let n=l.computeLeft(),r=l.computeTop(),c=e-n,d=t-r,{origRect:u}=l,h=u.right-u.left,f=u.bottom-u.top;if(c>=0&&c<h&&d>=0&&d<f){let e=a.queryHit(c,d,h,f);e&&(0,o.b9)(e.dateProfile.activeRange,e.dateSpan.range)&&(this.disablePointCheck||l.el.contains(l.el.getRootNode().elementFromPoint(c+n-window.scrollX,d+r-window.scrollY)))&&(!i||e.layer>i.layer)&&(e.componentId=s,e.context=a.context,e.rect.left+=n,e.rect.right+=n,e.rect.top+=r,e.rect.bottom+=r,i=e)}}}return i}}function C(e,t){return!e&&!t||Boolean(e)===Boolean(t)&&(0,o.bf)(e.dateSpan,t.dateSpan)}function _(e,t){let n={};for(let o of t.pluginHooks.datePointTransforms)Object.assign(n,o(e,t));var r,i;return Object.assign(n,(r=e,{date:(i=t.dateEnv).toDate(r.range.start),dateStr:i.formatIso(r.range.start,{omitTime:r.allDay}),allDay:r.allDay})),n}class R extends o.Z{constructor(e){super(e),this.handlePointerDown=e=>{let{dragging:t}=this,n=e.origEvent.target;t.setIgnoreMove(!this.component.isValidDateDownEl(n))},this.handleDragEnd=e=>{let{component:t}=this,{pointer:n}=this.dragging;if(!n.wasTouchScroll){let{initialHit:n,finalHit:r}=this.hitDragging;if(n&&r&&C(n,r)){let{context:r}=t,i=Object.assign(Object.assign({},_(n.dateSpan,r)),{dayEl:n.dayEl,jsEvent:e.origEvent,view:r.viewApi||r.calendarApi.view});r.emitter.trigger("dateClick",i)}}},this.dragging=new D(e.el),this.dragging.autoScroller.isEnabled=!1;let t=this.hitDragging=new k(this.dragging,(0,o.bG)(e));t.emitter.on("pointerdown",this.handlePointerDown),t.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}}class M extends o.Z{constructor(e){super(e),this.dragSelection=null,this.handlePointerDown=e=>{let{component:t,dragging:n}=this,{options:r}=t.context,i=r.selectable&&t.isValidDateDownEl(e.origEvent.target);n.setIgnoreMove(!i),n.delay=e.isTouch?I(t):null},this.handleDragStart=e=>{this.component.context.calendarApi.unselect(e)},this.handleHitUpdate=(e,t)=>{let{context:n}=this.component,r=null,i=!1;if(e){let t=this.hitDragging.initialHit;e.componentId===t.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(t,e)||(r=N(t,e,n.pluginHooks.dateSelectionTransformers)),r&&(0,o.b_)(r,e.dateProfile,n)||(i=!0,r=null)}r?n.dispatch({type:"SELECT_DATES",selection:r}):t||n.dispatch({type:"UNSELECT_DATES"}),i?(0,o.ax)():(0,o.aw)(),t||(this.dragSelection=r)},this.handlePointerUp=e=>{this.dragSelection&&((0,o.cu)(this.dragSelection,e,this.component.context),this.dragSelection=null)};let{component:t}=e,{options:n}=t.context,r=this.dragging=new D(e.el);r.touchScrollAllowed=!1,r.minDistance=n.selectMinDistance||0,r.autoScroller.isEnabled=n.dragScroll;let i=this.hitDragging=new k(this.dragging,(0,o.bG)(e));i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),i.emitter.on("hitupdate",this.handleHitUpdate),i.emitter.on("pointerup",this.handlePointerUp)}destroy(){this.dragging.destroy()}}function I(e){let{options:t}=e.context,n=t.selectLongPressDelay;return null==n&&(n=t.longPressDelay),n}function N(e,t,n){let r=e.dateSpan,i=t.dateSpan,s=[r.range.start,r.range.end,i.range.start,i.range.end];s.sort(o.av);let a={};for(let o of n){let n=o(e,t);if(!1===n)return null;n&&Object.assign(a,n)}return a.range={start:s[0],end:s[3]},a.allDay=r.allDay,a}class O extends o.Z{constructor(e){super(e),this.subjectEl=null,this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=e=>{let t=e.origEvent.target,{component:n,dragging:r}=this,{mirror:i}=r,{options:s}=n.context,a=n.context;this.subjectEl=e.subjectEl;let l=this.subjectSeg=(0,o._)(e.subjectEl),c=(this.eventRange=l.eventRange).instance.instanceId;this.relevantEvents=(0,o.aV)(a.getCurrentData().eventStore,c),r.minDistance=e.isTouch?0:s.eventDragMinDistance,r.delay=e.isTouch&&c!==n.props.eventSelection?H(n):null,s.fixedMirrorParent?i.parentNode=s.fixedMirrorParent:i.parentNode=(0,o.$)(t,".fc"),i.revertDuration=s.dragRevertDuration;let d=n.isValidSegDownEl(t)&&!(0,o.$)(t,".fc-event-resizer");r.setIgnoreMove(!d),this.isDragging=d&&e.subjectEl.classList.contains("fc-event-draggable")},this.handleDragStart=e=>{let t=this.component.context,n=this.eventRange,r=n.instance.instanceId;e.isTouch?r!==this.component.props.eventSelection&&t.dispatch({type:"SELECT_EVENT",eventInstanceId:r}):t.dispatch({type:"UNSELECT_EVENT"}),this.isDragging&&(t.calendarApi.unselect(e),t.emitter.trigger("eventDragStart",{el:this.subjectEl,event:new o.a0(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi}))},this.handleHitUpdate=(e,t)=>{if(!this.isDragging)return;let n=this.relevantEvents,r=this.hitDragging.initialHit,i=this.component.context,s=null,a=null,l=null,c=!1,d={affectedEvents:n,mutatedEvents:(0,o.I)(),isEvent:!0};if(e){s=e.context;let t=s.options;i===s||t.editable&&t.droppable?(a=z(r,e,this.eventRange.instance.range.start,s.getCurrentData().pluginHooks.eventDragMutationMassagers),a&&(l=(0,o.bX)(n,s.getCurrentData().eventUiBases,a,s),d.mutatedEvents=l,(0,o.bZ)(d,e.dateProfile,s)||(c=!0,a=null,l=null,d.mutatedEvents=(0,o.I)()))):s=null}this.displayDrag(s,d),c?(0,o.ax)():(0,o.aw)(),t||(i===s&&C(r,e)&&(a=null),this.dragging.setMirrorNeedsRevert(!a),this.dragging.setMirrorIsVisible(!e||!this.subjectEl.getRootNode().querySelector(".fc-event-mirror")),this.receivingContext=s,this.validMutation=a,this.mutatedRelevantEvents=l)},this.handlePointerUp=()=>{this.isDragging||this.cleanup()},this.handleDragEnd=e=>{if(this.isDragging){let t=this.component.context,n=t.viewApi,{receivingContext:r,validMutation:i}=this,s=this.eventRange.def,a=this.eventRange.instance,l=new o.a0(t,s,a),c=this.relevantEvents,d=this.mutatedRelevantEvents,{finalHit:u}=this.hitDragging;if(this.clearDrag(),t.emitter.trigger("eventDragStop",{el:this.subjectEl,event:l,jsEvent:e.origEvent,view:n}),i){if(r===t){let r=new o.a0(t,d.defs[s.defId],a?d.instances[a.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:d});let u={oldEvent:l,event:r,relatedEvents:(0,o.w)(d,t,a),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:c})}},h={};for(let e of t.getCurrentData().pluginHooks.eventDropTransformers)Object.assign(h,e(i,t));t.emitter.trigger("eventDrop",Object.assign(Object.assign(Object.assign({},u),h),{el:e.subjectEl,delta:i.datesDelta,jsEvent:e.origEvent,view:n})),t.emitter.trigger("eventChange",u)}else if(r){let i={event:l,relatedEvents:(0,o.w)(c,t,a),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:c})}};t.emitter.trigger("eventLeave",Object.assign(Object.assign({},i),{draggedEl:e.subjectEl,view:n})),t.dispatch({type:"REMOVE_EVENTS",eventStore:c}),t.emitter.trigger("eventRemove",i);let h=d.defs[s.defId],f=d.instances[a.instanceId],g=new o.a0(r,h,f);r.dispatch({type:"MERGE_EVENTS",eventStore:d});let p={event:g,relatedEvents:(0,o.w)(d,r,f),revert(){r.dispatch({type:"REMOVE_EVENTS",eventStore:d})}};r.emitter.trigger("eventAdd",p),e.isTouch&&r.dispatch({type:"SELECT_EVENT",eventInstanceId:a.instanceId}),r.emitter.trigger("drop",Object.assign(Object.assign({},_(u.dateSpan,r)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:u.context.viewApi})),r.emitter.trigger("eventReceive",Object.assign(Object.assign({},p),{draggedEl:e.subjectEl,view:u.context.viewApi}))}}else t.emitter.trigger("_noEventDrop")}this.cleanup()};let{component:t}=this,{options:n}=t.context,r=this.dragging=new D(e.el);r.pointer.selector=O.SELECTOR,r.touchScrollAllowed=!1,r.autoScroller.isEnabled=n.dragScroll;let i=this.hitDragging=new k(this.dragging,o.a7);i.useSubjectCenter=e.useEventCenter,i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),i.emitter.on("hitupdate",this.handleHitUpdate),i.emitter.on("pointerup",this.handlePointerUp),i.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}displayDrag(e,t){let n=this.component.context,r=this.receivingContext;r&&r!==e&&(r===n?r.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:t.affectedEvents,mutatedEvents:(0,o.I)(),isEvent:!0}}):r.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})}clearDrag(){let e=this.component.context,{receivingContext:t}=this;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})}cleanup(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null}}function z(e,t,n,r){let i=e.dateSpan,s=t.dateSpan,a=i.range.start,l=s.range.start,c={};i.allDay!==s.allDay&&(c.allDay=s.allDay,c.hasEnd=t.context.options.allDayMaintainDuration,a=s.allDay?(0,o.q)(n):n);let d=(0,o.aA)(a,l,e.context.dateEnv,e.componentId===t.componentId?e.largeUnit:null);d.milliseconds&&(c.allDay=!1);let u={datesDelta:d,standardProps:c};for(let o of r)o(u,e,t);return u}function H(e){let{options:t}=e.context,n=t.eventLongPressDelay;return null==n&&(n=t.longPressDelay),n}O.SELECTOR=".fc-event-draggable, .fc-event-resizable";class P extends o.Z{constructor(e){super(e),this.draggingSegEl=null,this.draggingSeg=null,this.eventRange=null,this.relevantEvents=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=e=>{let{component:t}=this,n=this.querySegEl(e),r=(0,o._)(n),i=this.eventRange=r.eventRange;this.dragging.minDistance=t.context.options.eventDragMinDistance,this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&this.component.props.eventSelection!==i.instance.instanceId)},this.handleDragStart=e=>{let{context:t}=this.component,n=this.eventRange;this.relevantEvents=(0,o.aV)(t.getCurrentData().eventStore,this.eventRange.instance.instanceId);let r=this.querySegEl(e);this.draggingSegEl=r,this.draggingSeg=(0,o._)(r),t.calendarApi.unselect(),t.emitter.trigger("eventResizeStart",{el:r,event:new o.a0(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi})},this.handleHitUpdate=(e,t,n)=>{let{context:r}=this.component,i=this.relevantEvents,s=this.hitDragging.initialHit,a=this.eventRange.instance,l=null,c=null,d=!1,u={affectedEvents:i,mutatedEvents:(0,o.I)(),isEvent:!0};if(e){e.componentId===s.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(s,e)||(l=L(s,e,n.subjectEl.classList.contains("fc-event-resizer-start"),a.range))}l&&(c=(0,o.bX)(i,r.getCurrentData().eventUiBases,l,r),u.mutatedEvents=c,(0,o.bZ)(u,e.dateProfile,r)||(d=!0,l=null,c=null,u.mutatedEvents=null)),c?r.dispatch({type:"SET_EVENT_RESIZE",state:u}):r.dispatch({type:"UNSET_EVENT_RESIZE"}),d?(0,o.ax)():(0,o.aw)(),t||(l&&C(s,e)&&(l=null),this.validMutation=l,this.mutatedRelevantEvents=c)},this.handleDragEnd=e=>{let{context:t}=this.component,n=this.eventRange.def,r=this.eventRange.instance,i=new o.a0(t,n,r),s=this.relevantEvents,a=this.mutatedRelevantEvents;if(t.emitter.trigger("eventResizeStop",{el:this.draggingSegEl,event:i,jsEvent:e.origEvent,view:t.viewApi}),this.validMutation){let l=new o.a0(t,a.defs[n.defId],r?a.instances[r.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:a});let c={oldEvent:i,event:l,relatedEvents:(0,o.w)(a,t,r),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:s})}};t.emitter.trigger("eventResize",Object.assign(Object.assign({},c),{el:this.draggingSegEl,startDelta:this.validMutation.startDelta||(0,o.d)(0),endDelta:this.validMutation.endDelta||(0,o.d)(0),jsEvent:e.origEvent,view:t.viewApi})),t.emitter.trigger("eventChange",c)}else t.emitter.trigger("_noEventResize");this.draggingSeg=null,this.relevantEvents=null,this.validMutation=null};let{component:t}=e,n=this.dragging=new D(e.el);n.pointer.selector=".fc-event-resizer",n.touchScrollAllowed=!1,n.autoScroller.isEnabled=t.context.options.dragScroll;let r=this.hitDragging=new k(this.dragging,(0,o.bG)(e));r.emitter.on("pointerdown",this.handlePointerDown),r.emitter.on("dragstart",this.handleDragStart),r.emitter.on("hitupdate",this.handleHitUpdate),r.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}querySegEl(e){return(0,o.$)(e.subjectEl,".fc-event")}}function L(e,t,n,r){let i=e.context.dateEnv,s=e.dateSpan.range.start,a=t.dateSpan.range.start,l=(0,o.aA)(s,a,i,e.largeUnit);if(n){if(i.add(r.start,l)<r.end)return{startDelta:l}}else if(i.add(r.end,l)>r.start)return{endDelta:l};return null}class B{constructor(e){this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=e=>{e.jsEvent&&(this.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=e=>{let t=this.context.options.unselectCancel,n=(0,o.aR)(e.origEvent);this.matchesCancel=!!(0,o.$)(n,t),this.matchesEvent=!!(0,o.$)(n,O.SELECTOR)},this.onDocumentPointerUp=e=>{let{context:t}=this,{documentPointer:n}=this,r=t.getCurrentData();if(!n.wasTouchScroll){if(r.dateSelection&&!this.isRecentPointerDateSelect){let n=t.options.unselectAuto;!n||n&&this.matchesCancel||t.calendarApi.unselect(e)}r.eventSelection&&!this.matchesEvent&&t.dispatch({type:"UNSELECT_EVENT"})}this.isRecentPointerDateSelect=!1};let t=this.documentPointer=new h(document);t.shouldIgnoreMove=!0,t.shouldWatchScroll=!1,t.emitter.on("pointerdown",this.onDocumentPointerDown),t.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}destroy(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()}}const j={fixedMirrorParent:o.n},U={dateClick:o.n,eventDragStart:o.n,eventDragStop:o.n,eventDrop:o.n,eventResizeStart:o.n,eventResizeStop:o.n,eventResize:o.n,drop:o.n,eventReceive:o.n,eventLeave:o.n};o.bI.dataAttrPrefix="";o.bH;var a=(0,i.rx)({name:"@fullcalendar/interaction",componentInteractions:[R,M,O,P],calendarInteractions:[B],elementDraggingImpl:D,optionRefiners:j,listenerRefiners:U});r()}catch(l){r(l)}}))},46834:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{Z:function(){return a}});var i=n(95559),o=n(68322),s=e([i,o]);[i,o]=s.then?(await s)():s;const l={allDaySlot:Boolean};var a=(0,i.rx)({name:"@fullcalendar/timegrid",initialView:"timeGridWeek",optionRefiners:l,views:{timeGrid:{component:o.AN,usesMinMaxTime:!0,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},timeGridDay:{type:"timeGrid",duration:{days:1}},timeGridWeek:{type:"timeGrid",duration:{weeks:1}}}});r()}catch(l){r(l)}}))},68322:function(e,t,n){n.a(e,(async function(e,r){try{n.d(t,{AN:function(){return $}});n(52247),n(71695),n(92745),n(61893),n(19423),n(13334),n(47021);var i=n(66952),o=n(65054),s=n(35325),a=e([s,i]);[s,i]=a.then?(await a)():a;class c extends i.aY{getKeyInfo(){return{allDay:{},timed:{}}}getKeysForDateSpan(e){return e.allDay?["allDay"]:["timed"]}getKeysForEventDef(e){return e.allDay?(0,i.bP)(e)?["timed","allDay"]:["allDay"]:["timed"]}}const d=(0,i.x)({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"});function u(e){let t=["fc-timegrid-slot","fc-timegrid-slot-label",e.isLabeled?"fc-scrollgrid-shrink":"fc-timegrid-slot-minor"];return(0,o.az)(i.V.Consumer,null,(n=>{if(!e.isLabeled)return(0,o.az)("td",{className:t.join(" "),"data-time":e.isoTimeStr});let{dateEnv:r,options:s,viewApi:a}=n,l=null==s.slotLabelFormat?d:Array.isArray(s.slotLabelFormat)?(0,i.x)(s.slotLabelFormat[0]):(0,i.x)(s.slotLabelFormat),c={level:0,time:e.time,date:r.toDate(e.date),view:a,text:r.format(e.date,l)};return(0,o.az)(i.C,{elTag:"td",elClasses:t,elAttrs:{"data-time":e.isoTimeStr},renderProps:c,generatorName:"slotLabelContent",customGenerator:s.slotLabelContent,defaultGenerator:h,classNameGenerator:s.slotLabelClassNames,didMount:s.slotLabelDidMount,willUnmount:s.slotLabelWillUnmount},(e=>(0,o.az)("div",{className:"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame"},(0,o.az)(e,{elTag:"div",elClasses:["fc-timegrid-slot-label-cushion","fc-scrollgrid-shrink-cushion"]}))))}))}function h(e){return e.text}class f extends i.B{render(){return this.props.slatMetas.map((e=>(0,o.az)("tr",{key:e.key},(0,o.az)(u,Object.assign({},e)))))}}const g=(0,i.x)({week:"short"}),p=5;class m extends i.be{constructor(){super(...arguments),this.allDaySplitter=new c,this.headerElRef=(0,o.Vf)(),this.rootElRef=(0,o.Vf)(),this.scrollerElRef=(0,o.Vf)(),this.state={slatCoords:null},this.handleScrollTopRequest=e=>{let t=this.scrollerElRef.current;t&&(t.scrollTop=e)},this.renderHeadAxis=(e,t="")=>{let{options:n}=this.context,{dateProfile:r}=this.props,s=r.renderRange,a=1===(0,i.bl)(s.start,s.end)?(0,i.b0)(this.context,s.start,"week"):{};return n.weekNumbers&&"day"===e?(0,o.az)(i.cq,{elTag:"th",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},date:s.start,defaultFormat:g},(e=>(0,o.az)("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame","fc-timegrid-axis-frame-liquid"].join(" "),style:{height:t}},(0,o.az)(e,{elTag:"a",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"],elAttrs:a})))):(0,o.az)("th",{"aria-hidden":!0,className:"fc-timegrid-axis"},(0,o.az)("div",{className:"fc-timegrid-axis-frame",style:{height:t}}))},this.renderTableRowAxis=e=>{let{options:t,viewApi:n}=this.context,r={text:t.allDayText,view:n};return(0,o.az)(i.C,{elTag:"td",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},renderProps:r,generatorName:"allDayContent",customGenerator:t.allDayContent,defaultGenerator:v,classNameGenerator:t.allDayClassNames,didMount:t.allDayDidMount,willUnmount:t.allDayWillUnmount},(t=>(0,o.az)("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame",null==e?" fc-timegrid-axis-frame-liquid":""].join(" "),style:{height:e}},(0,o.az)(t,{elTag:"span",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"]}))))},this.handleSlatCoords=e=>{this.setState({slatCoords:e})}}renderSimpleLayout(e,t,n){let{context:r,props:s}=this,a=[],l=(0,i.cc)(r.options);return e&&a.push({type:"header",key:"header",isSticky:l,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),t&&(a.push({type:"body",key:"all-day",chunk:{content:t}}),a.push({type:"body",key:"all-day-divider",outerContent:(0,o.az)("tr",{role:"presentation",className:"fc-scrollgrid-section"},(0,o.az)("td",{className:"fc-timegrid-divider "+r.theme.getClass("tableCellShaded")}))})),a.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(r.options.expandRows),chunk:{scrollerElRef:this.scrollerElRef,content:n}}),(0,o.az)(i.ct,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:r.viewSpec},(0,o.az)(i.b$,{liquid:!s.isHeightAuto&&!s.forPrint,collapsibleWidth:s.forPrint,cols:[{width:"shrink"}],sections:a}))}renderHScrollLayout(e,t,n,r,s,a,l){let c=this.context.pluginHooks.scrollGridImpl;if(!c)throw new Error("No ScrollGrid implementation");let{context:d,props:u}=this,h=!u.forPrint&&(0,i.cc)(d.options),g=!u.forPrint&&(0,i.cb)(d.options),p=[];e&&p.push({type:"header",key:"header",isSticky:h,syncRowHeights:!0,chunks:[{key:"axis",rowContent:e=>(0,o.az)("tr",{role:"presentation"},this.renderHeadAxis("day",e.rowSyncHeights[0]))},{key:"cols",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),t&&(p.push({type:"body",key:"all-day",syncRowHeights:!0,chunks:[{key:"axis",rowContent:e=>(0,o.az)("tr",{role:"presentation"},this.renderTableRowAxis(e.rowSyncHeights[0]))},{key:"cols",content:t}]}),p.push({key:"all-day-divider",type:"body",outerContent:(0,o.az)("tr",{role:"presentation",className:"fc-scrollgrid-section"},(0,o.az)("td",{colSpan:2,className:"fc-timegrid-divider "+d.theme.getClass("tableCellShaded")}))}));let m=d.options.nowIndicator;return p.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(d.options.expandRows),chunks:[{key:"axis",content:e=>(0,o.az)("div",{className:"fc-timegrid-axis-chunk"},(0,o.az)("table",{"aria-hidden":!0,style:{height:e.expandRows?e.clientHeight:""}},e.tableColGroupNode,(0,o.az)("tbody",null,(0,o.az)(f,{slatMetas:a}))),(0,o.az)("div",{className:"fc-timegrid-now-indicator-container"},(0,o.az)(i.ch,{unit:m?"minute":"day"},(e=>{let t=m&&l&&l.safeComputeTop(e);return"number"==typeof t?(0,o.az)(i.ck,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:t},isAxis:!0,date:e}):null}))))},{key:"cols",scrollerElRef:this.scrollerElRef,content:n}]}),g&&p.push({key:"footer",type:"footer",isSticky:!0,chunks:[{key:"axis",content:i.ca},{key:"cols",content:i.ca}]}),(0,o.az)(i.ct,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:d.viewSpec},(0,o.az)(c,{liquid:!u.isHeightAuto&&!u.forPrint,forPrint:u.forPrint,collapsibleWidth:!1,colGroups:[{width:"shrink",cols:[{width:"shrink"}]},{cols:[{span:r,minWidth:s}]}],sections:p}))}getAllDayMaxEventProps(){let{dayMaxEvents:e,dayMaxEventRows:t}=this.context.options;return!0!==e&&!0!==t||(e=void 0,t=p),{dayMaxEvents:e,dayMaxEventRows:t}}}function v(e){return e.text}class y{constructor(e,t,n){this.positions=e,this.dateProfile=t,this.slotDuration=n}safeComputeTop(e){let{dateProfile:t}=this;if((0,i.H)(t.currentRange,e)){let n=(0,i.q)(e),r=e.valueOf()-n.valueOf();if(r>=(0,i.bs)(t.slotMinTime)&&r<(0,i.bs)(t.slotMaxTime))return this.computeTimeTop((0,i.d)(r))}return null}computeDateTop(e,t){return t||(t=(0,i.q)(e)),this.computeTimeTop((0,i.d)(e.valueOf()-t.valueOf()))}computeTimeTop(e){let t,n,{positions:r,dateProfile:o}=this,s=r.els.length,a=(e.milliseconds-(0,i.bs)(o.slotMinTime))/(0,i.bs)(this.slotDuration);return a=Math.max(0,a),a=Math.min(s,a),t=Math.floor(a),t=Math.min(t,s-1),n=a-t,r.tops[t]+r.getHeight(t)*n}}class b extends i.B{render(){let{props:e,context:t}=this,{options:n}=t,{slatElRefs:r}=e;return(0,o.az)("tbody",null,e.slatMetas.map(((s,a)=>{let l={time:s.time,date:t.dateEnv.toDate(s.date),view:t.viewApi};return(0,o.az)("tr",{key:s.key,ref:r.createRef(s.key)},e.axis&&(0,o.az)(u,Object.assign({},s)),(0,o.az)(i.C,{elTag:"td",elClasses:["fc-timegrid-slot","fc-timegrid-slot-lane",!s.isLabeled&&"fc-timegrid-slot-minor"],elAttrs:{"data-time":s.isoTimeStr},renderProps:l,generatorName:"slotLaneContent",customGenerator:n.slotLaneContent,classNameGenerator:n.slotLaneClassNames,didMount:n.slotLaneDidMount,willUnmount:n.slotLaneWillUnmount}))})))}}class w extends i.B{constructor(){super(...arguments),this.rootElRef=(0,o.Vf)(),this.slatElRefs=new i.cf}render(){let{props:e,context:t}=this;return(0,o.az)("div",{ref:this.rootElRef,className:"fc-timegrid-slots"},(0,o.az)("table",{"aria-hidden":!0,className:t.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},e.tableColGroupNode,(0,o.az)(b,{slatElRefs:this.slatElRefs,axis:e.axis,slatMetas:e.slatMetas})))}componentDidMount(){this.updateSizing()}componentDidUpdate(){this.updateSizing()}componentWillUnmount(){this.props.onCoords&&this.props.onCoords(null)}updateSizing(){let{context:e,props:t}=this;if(t.onCoords&&null!==t.clientWidth){this.rootElRef.current.offsetHeight&&t.onCoords(new y(new i.ba(this.rootElRef.current,(n=this.slatElRefs.currentMap,t.slatMetas.map((e=>n[e.key]))),!1,!0),this.props.dateProfile,e.options.slotDuration))}var n}}function E(e,t){let n,r=[];for(n=0;n<t;n+=1)r.push([]);if(e)for(n=0;n<e.length;n+=1)r[e[n].col].push(e[n]);return r}function A(e,t){let n=[];if(e){for(let r=0;r<t;r+=1)n[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(let t of e.segs)n[t.col].segs.push(t)}else for(let r=0;r<t;r+=1)n[r]=null;return n}class x extends i.B{render(){let{props:e}=this;return(0,o.az)(i.cr,{elClasses:["fc-timegrid-more-link"],elStyle:{top:e.top,bottom:e.bottom},allDayDate:null,moreCnt:e.hiddenSegs.length,allSegs:e.hiddenSegs,hiddenSegs:e.hiddenSegs,extraDateSpan:e.extraDateSpan,dateProfile:e.dateProfile,todayRange:e.todayRange,popoverContent:()=>B(e.hiddenSegs,e),defaultGenerator:D,forceTimed:!0},(e=>(0,o.az)(e,{elTag:"div",elClasses:["fc-timegrid-more-link-inner","fc-sticky"]})))}}function D(e){return e.shortText}function T(e,t,n){let r=new i.bA;null!=t&&(r.strictOrder=t),null!=n&&(r.maxStackCnt=n);let o=r.addSegs(e),s=(0,i.bE)(o),a=S(r);return a=M(a,1),{segRects:I(a),hiddenGroups:s}}function S(e){const{entriesByLevel:t}=e,n=N(((e,t)=>e+":"+t),((r,i)=>{let o=k(R(e,r,i),n),s=t[r][i];return[Object.assign(Object.assign({},s),{nextLevelNodes:o[0]}),s.thickness+o[1]]}));return k(t.length?{level:0,lateralStart:0,lateralEnd:t[0].length}:null,n)[0]}function k(e,t){if(!e)return[[],0];let{level:n,lateralStart:r,lateralEnd:i}=e,o=r,s=[];for(;o<i;)s.push(t(n,o)),o+=1;return s.sort(C),[s.map(_),s[0][1]]}function C(e,t){return t[1]-e[1]}function _(e){return e[0]}function R(e,t,n){let{levelCoords:r,entriesByLevel:o}=e,s=o[t][n],a=r[t]+s.thickness,l=r.length,c=t;for(;c<l&&r[c]<a;c+=1);for(;c<l;c+=1){let e,t=o[c],n=(0,i.bD)(t,s.span.start,i.bC),r=n[0]+n[1],a=r;for(;(e=t[a])&&e.span.start<s.span.end;)a+=1;if(r<a)return{level:c,lateralStart:r,lateralEnd:a}}return null}function M(e,t){const n=N(((e,t,n)=>(0,i.bB)(e)),((e,r,i)=>{let o,{nextLevelNodes:s,thickness:a}=e,l=a+i,c=a/l,d=[];if(s.length)for(let t of s)if(void 0===o){let e=n(t,r,l);o=e[0],d.push(e[1])}else{let e=n(t,o,0);d.push(e[1])}else o=t;let u=(o-r)*c;return[o-u,Object.assign(Object.assign({},e),{thickness:u,nextLevelNodes:d})]}));return e.map((e=>n(e,0,0)[1]))}function I(e){let t=[];const n=N(((e,t,n)=>(0,i.bB)(e)),((e,n,i)=>{let o=Object.assign(Object.assign({},e),{levelCoord:n,stackDepth:i,stackForward:0});return t.push(o),o.stackForward=r(e.nextLevelNodes,n+e.thickness,i+1)+1}));function r(e,t,r){let i=0;for(let o of e)i=Math.max(n(o,t,r),i);return i}return r(e,0,0),t}function N(e,t){const n={};return(...r)=>{let i=e(...r);return i in n?n[i]:n[i]=t(...r)}}function O(e,t,n=null,r=0){let i=[];if(n)for(let o=0;o<e.length;o+=1){let s=e[o],a=n.computeDateTop(s.start,t),l=Math.max(a+(r||0),n.computeDateTop(s.end,t));i.push({start:Math.round(a),end:Math.round(l)})}return i}function z(e,t,n,r){let i=[],o=[];for(let c=0;c<e.length;c+=1){let n=t[c];n?i.push({index:c,thickness:1,span:n}):o.push(e[c])}let{segRects:s,hiddenGroups:a}=T(i,n,r),l=[];for(let c of s)l.push({seg:e[c.index],rect:c});for(let c of o)l.push({seg:c,rect:null});return{segPlacements:l,hiddenGroups:a}}const H=(0,i.x)({hour:"numeric",minute:"2-digit",meridiem:!1});class P extends i.B{render(){return(0,o.az)(i.cj,Object.assign({},this.props,{elClasses:["fc-timegrid-event","fc-v-event",this.props.isShort&&"fc-timegrid-event-short"],defaultTimeFormat:H}))}}class L extends i.B{constructor(){super(...arguments),this.sortEventSegs=(0,i.z)(i.bR)}render(){let{props:e,context:t}=this,{options:n}=t,r=n.selectMirror,s=e.eventDrag&&e.eventDrag.segs||e.eventResize&&e.eventResize.segs||r&&e.dateSelectionSegs||[],a=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{},l=this.sortEventSegs(e.fgEventSegs,n.eventOrder);return(0,o.az)(i.cl,{elTag:"td",elRef:e.elRef,elClasses:["fc-timegrid-col",...e.extraClassNames||[]],elAttrs:Object.assign({role:"gridcell"},e.extraDataAttrs),date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,extraRenderProps:e.extraRenderProps},(t=>(0,o.az)("div",{className:"fc-timegrid-col-frame"},(0,o.az)("div",{className:"fc-timegrid-col-bg"},this.renderFillSegs(e.businessHourSegs,"non-business"),this.renderFillSegs(e.bgEventSegs,"bg-event"),this.renderFillSegs(e.dateSelectionSegs,"highlight")),(0,o.az)("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(l,a,!1,!1,!1)),(0,o.az)("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(s,{},Boolean(e.eventDrag),Boolean(e.eventResize),Boolean(r),"mirror")),(0,o.az)("div",{className:"fc-timegrid-now-indicator-container"},this.renderNowIndicator(e.nowIndicatorSegs)),(0,i.cm)(n)&&(0,o.az)(t,{elTag:"div",elClasses:["fc-timegrid-col-misc"]}))))}renderFgSegs(e,t,n,r,i,o){let{props:s}=this;return s.forPrint?B(e,s):this.renderPositionedFgSegs(e,t,n,r,i,o)}renderPositionedFgSegs(e,t,n,r,s,a){let{eventMaxStack:l,eventShortHeight:c,eventOrderStrict:d,eventMinHeight:u}=this.context.options,{date:h,slatCoords:f,eventSelection:g,todayRange:p,nowDate:m}=this.props,v=n||r||s,y=O(e,h,f,u),{segPlacements:b,hiddenGroups:w}=z(e,y,d,l);return(0,o.az)(o.HY,null,this.renderHiddenGroups(w,e),b.map((e=>{let{seg:l,rect:d}=e,u=l.eventRange.instance.instanceId,h=v||Boolean(!t[u]&&d),f=j(d&&d.span),y=!v&&d?this.computeSegHStyle(d):{left:0,right:0},b=Boolean(d)&&d.stackForward>0,w=Boolean(d)&&d.span.end-d.span.start<c;return(0,o.az)("div",{className:"fc-timegrid-event-harness"+(b?" fc-timegrid-event-harness-inset":""),key:a||u,style:Object.assign(Object.assign({visibility:h?"":"hidden"},f),y)},(0,o.az)(P,Object.assign({seg:l,isDragging:n,isResizing:r,isDateSelecting:s,isSelected:u===g,isShort:w},(0,i.bS)(l,p,m))))})))}renderHiddenGroups(e,t){let{extraDateSpan:n,dateProfile:r,todayRange:s,nowDate:a,eventSelection:l,eventDrag:c,eventResize:d}=this.props;return(0,o.az)(o.HY,null,e.map((e=>{let u=j(e.span),h=(f=e.entries,g=t,f.map((e=>g[e.index])));var f,g;return(0,o.az)(x,{key:(0,i.bw)((0,i.cs)(h)),hiddenSegs:h,top:u.top,bottom:u.bottom,extraDateSpan:n,dateProfile:r,todayRange:s,nowDate:a,eventSelection:l,eventDrag:c,eventResize:d})})))}renderFillSegs(e,t){let{props:n,context:r}=this,s=O(e,n.date,n.slatCoords,r.options.eventMinHeight).map(((r,s)=>{let a=e[s];return(0,o.az)("div",{key:(0,i.bT)(a.eventRange),className:"fc-timegrid-bg-harness",style:j(r)},"bg-event"===t?(0,o.az)(i.cp,Object.assign({seg:a},(0,i.bS)(a,n.todayRange,n.nowDate))):(0,i.co)(t))}));return(0,o.az)(o.HY,null,s)}renderNowIndicator(e){let{slatCoords:t,date:n}=this.props;return t?e.map(((e,r)=>(0,o.az)(i.ck,{key:r,elClasses:["fc-timegrid-now-indicator-line"],elStyle:{top:t.computeDateTop(e.start,n)},isAxis:!1,date:n}))):null}computeSegHStyle(e){let t,n,{isRtl:r,options:i}=this.context,o=i.slotEventOverlap,s=e.levelCoord,a=e.levelCoord+e.thickness;o&&(a=Math.min(1,s+2*(a-s))),r?(t=1-a,n=s):(t=s,n=1-a);let l={zIndex:e.stackDepth+1,left:100*t+"%",right:100*n+"%"};return o&&!e.stackForward&&(l[r?"marginLeft":"marginRight"]=20),l}}function B(e,{todayRange:t,nowDate:n,eventSelection:r,eventDrag:s,eventResize:a}){let l=(s?s.affectedInstances:null)||(a?a.affectedInstances:null)||{};return(0,o.az)(o.HY,null,e.map((e=>{let s=e.eventRange.instance.instanceId;return(0,o.az)("div",{key:s,style:{visibility:l[s]?"hidden":""}},(0,o.az)(P,Object.assign({seg:e,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:s===r,isShort:!1},(0,i.bS)(e,t,n))))})))}function j(e){return e?{top:e.start,bottom:-e.end}:{top:"",bottom:""}}class U extends i.B{constructor(){super(...arguments),this.splitFgEventSegs=(0,i.z)(E),this.splitBgEventSegs=(0,i.z)(E),this.splitBusinessHourSegs=(0,i.z)(E),this.splitNowIndicatorSegs=(0,i.z)(E),this.splitDateSelectionSegs=(0,i.z)(E),this.splitEventDrag=(0,i.z)(A),this.splitEventResize=(0,i.z)(A),this.rootElRef=(0,o.Vf)(),this.cellElRefs=new i.cf}render(){let{props:e,context:t}=this,n=t.options.nowIndicator&&e.slatCoords&&e.slatCoords.safeComputeTop(e.nowDate),r=e.cells.length,s=this.splitFgEventSegs(e.fgEventSegs,r),a=this.splitBgEventSegs(e.bgEventSegs,r),l=this.splitBusinessHourSegs(e.businessHourSegs,r),c=this.splitNowIndicatorSegs(e.nowIndicatorSegs,r),d=this.splitDateSelectionSegs(e.dateSelectionSegs,r),u=this.splitEventDrag(e.eventDrag,r),h=this.splitEventResize(e.eventResize,r);return(0,o.az)("div",{className:"fc-timegrid-cols",ref:this.rootElRef},(0,o.az)("table",{role:"presentation",style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,(0,o.az)("tbody",{role:"presentation"},(0,o.az)("tr",{role:"row"},e.axis&&(0,o.az)("td",{"aria-hidden":!0,className:"fc-timegrid-col fc-timegrid-axis"},(0,o.az)("div",{className:"fc-timegrid-col-frame"},(0,o.az)("div",{className:"fc-timegrid-now-indicator-container"},"number"==typeof n&&(0,o.az)(i.ck,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:n},isAxis:!0,date:e.nowDate})))),e.cells.map(((t,n)=>(0,o.az)(L,{key:t.key,elRef:this.cellElRefs.createRef(t.key),dateProfile:e.dateProfile,date:t.date,nowDate:e.nowDate,todayRange:e.todayRange,extraRenderProps:t.extraRenderProps,extraDataAttrs:t.extraDataAttrs,extraClassNames:t.extraClassNames,extraDateSpan:t.extraDateSpan,fgEventSegs:s[n],bgEventSegs:a[n],businessHourSegs:l[n],nowIndicatorSegs:c[n],dateSelectionSegs:d[n],eventDrag:u[n],eventResize:h[n],slatCoords:e.slatCoords,eventSelection:e.eventSelection,forPrint:e.forPrint})))))))}componentDidMount(){this.updateCoords()}componentDidUpdate(){this.updateCoords()}updateCoords(){let{props:e}=this;var t;e.onColCoords&&null!==e.clientWidth&&e.onColCoords(new i.ba(this.rootElRef.current,(t=this.cellElRefs.currentMap,e.cells.map((e=>t[e.key]))),!0,!1))}}class W extends i.be{constructor(){super(...arguments),this.processSlotOptions=(0,i.z)(V),this.state={slatCoords:null},this.handleRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e,isHitComboAllowed:this.props.isHitComboAllowed}):this.context.unregisterInteractiveComponent(this)},this.handleScrollRequest=e=>{let{onScrollTopRequest:t}=this.props,{slatCoords:n}=this.state;if(t&&n){if(e.time){let r=n.computeTimeTop(e.time);r=Math.ceil(r),r&&(r+=1),t(r)}return!0}return!1},this.handleColCoords=e=>{this.colCoords=e},this.handleSlatCoords=e=>{this.setState({slatCoords:e}),this.props.onSlatCoords&&this.props.onSlatCoords(e)}}render(){let{props:e,state:t}=this;return(0,o.az)("div",{className:"fc-timegrid-body",ref:this.handleRootEl,style:{width:e.clientWidth,minWidth:e.tableMinWidth}},(0,o.az)(w,{axis:e.axis,dateProfile:e.dateProfile,slatMetas:e.slatMetas,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,tableColGroupNode:e.axis?e.tableColGroupNode:null,onCoords:this.handleSlatCoords}),(0,o.az)(U,{cells:e.cells,axis:e.axis,dateProfile:e.dateProfile,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,todayRange:e.todayRange,nowDate:e.nowDate,nowIndicatorSegs:e.nowIndicatorSegs,clientWidth:e.clientWidth,tableMinWidth:e.tableMinWidth,tableColGroupNode:e.tableColGroupNode,slatCoords:t.slatCoords,onColCoords:this.handleColCoords,forPrint:e.forPrint}))}componentDidMount(){this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)}componentDidUpdate(e){this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)}componentWillUnmount(){this.scrollResponder.detach()}queryHit(e,t){let{dateEnv:n,options:r}=this.context,{colCoords:o}=this,{dateProfile:s}=this.props,{slatCoords:a}=this.state,{snapDuration:l,snapsPerSlot:c}=this.processSlotOptions(this.props.slotDuration,r.snapDuration),d=o.leftToIndex(e),u=a.positions.topToIndex(t);if(null!=d&&null!=u){let e=this.props.cells[d],r=a.positions.tops[u],h=a.positions.getHeight(u),f=(t-r)/h,g=u*c+Math.floor(f*c),p=this.props.cells[d].date,m=(0,i.bp)(s.slotMinTime,(0,i.bo)(l,g)),v=n.add(p,m),y=n.add(v,l);return{dateProfile:s,dateSpan:Object.assign({range:{start:v,end:y},allDay:!1},e.extraDateSpan),dayEl:o.els[d],rect:{left:o.lefts[d],right:o.rights[d],top:r,bottom:r+h},layer:0}}return null}}function V(e,t){let n=t||e,r=(0,i.bt)(e,n);return null===r&&(n=e,r=1),{snapDuration:n,snapsPerSlot:r}}class F extends i.bW{sliceRange(e,t){let n=[];for(let r=0;r<t.length;r+=1){let o=(0,i.o)(e,t[r]);o&&n.push({start:o.start,end:o.end,isStart:o.start.valueOf()===e.start.valueOf(),isEnd:o.end.valueOf()===e.end.valueOf(),col:r})}return n}}class G extends i.be{constructor(){super(...arguments),this.buildDayRanges=(0,i.z)(Y),this.slicer=new F,this.timeColsRef=(0,o.Vf)()}render(){let{props:e,context:t}=this,{dateProfile:n,dayTableModel:r}=e,{nowIndicator:s,nextDayThreshold:a}=t.options,l=this.buildDayRanges(r,n,t.dateEnv);return(0,o.az)(i.ch,{unit:s?"minute":"day"},((i,c)=>(0,o.az)(W,Object.assign({ref:this.timeColsRef},this.slicer.sliceProps(e,n,null,t,l),{forPrint:e.forPrint,axis:e.axis,dateProfile:n,slatMetas:e.slatMetas,slotDuration:e.slotDuration,cells:r.cells[0],tableColGroupNode:e.tableColGroupNode,tableMinWidth:e.tableMinWidth,clientWidth:e.clientWidth,clientHeight:e.clientHeight,expandRows:e.expandRows,nowDate:i,nowIndicatorSegs:s&&this.slicer.sliceNowDate(i,n,a,t,l),todayRange:c,onScrollTopRequest:e.onScrollTopRequest,onSlatCoords:e.onSlatCoords}))))}}function Y(e,t,n){let r=[];for(let i of e.headerDates)r.push({start:n.add(i,t.slotMinTime),end:n.add(i,t.slotMaxTime)});return r}const Q=[{hours:1},{minutes:30},{minutes:15},{seconds:30},{seconds:15}];function q(e,t,n,r,o){let s=new Date(0),a=e,l=(0,i.d)(0),c=n||Z(r),d=[];for(;(0,i.bs)(a)<(0,i.bs)(t);){let e=o.add(s,a),t=null!==(0,i.bt)(l,c);d.push({date:e,time:a,key:e.toISOString(),isoTimeStr:(0,i.bu)(e),isLabeled:t}),a=(0,i.bp)(a,r),l=(0,i.bp)(l,r)}return d}function Z(e){let t,n,r;for(t=Q.length-1;t>=0;t-=1)if(n=(0,i.d)(Q[t]),r=(0,i.bt)(n,e),null!==r&&r>1)return n;return e}class $ extends m{constructor(){super(...arguments),this.buildTimeColsModel=(0,i.z)(X),this.buildSlatMetas=(0,i.z)(q)}render(){let{options:e,dateEnv:t,dateProfileGenerator:n}=this.context,{props:r}=this,{dateProfile:a}=r,l=this.buildTimeColsModel(a,n),c=this.allDaySplitter.splitProps(r),d=this.buildSlatMetas(a.slotMinTime,a.slotMaxTime,e.slotLabelInterval,e.slotDuration,t),{dayMinWidth:u}=e,h=!u,f=u,g=e.dayHeaders&&(0,o.az)(i.bK,{dates:l.headerDates,dateProfile:a,datesRepDistinctDays:!0,renderIntro:h?this.renderHeadAxis:null}),p=!1!==e.allDaySlot&&(t=>(0,o.az)(s.iz,Object.assign({},c.allDay,{dateProfile:a,dayTableModel:l,nextDayThreshold:e.nextDayThreshold,tableMinWidth:t.tableMinWidth,colGroupNode:t.tableColGroupNode,renderRowIntro:h?this.renderTableRowAxis:null,showWeekNumbers:!1,expandRows:!1,headerAlignElRef:this.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:r.forPrint},this.getAllDayMaxEventProps()))),m=t=>(0,o.az)(G,Object.assign({},c.timed,{dayTableModel:l,dateProfile:a,axis:h,slotDuration:e.slotDuration,slatMetas:d,forPrint:r.forPrint,tableColGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,clientWidth:t.clientWidth,clientHeight:t.clientHeight,onSlatCoords:this.handleSlatCoords,expandRows:t.expandRows,onScrollTopRequest:this.handleScrollTopRequest}));return f?this.renderHScrollLayout(g,p,m,l.colCnt,u,d,this.state.slatCoords):this.renderSimpleLayout(g,p,m)}}function X(e,t){let n=new i.bO(e.renderRange,t);return new i.bV(n,!1)}(0,i.cw)('.fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:"\\00a0"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:"\\00a0-\\00a0"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-arrow,.fc .fc-timegrid-now-indicator-line{pointer-events:none}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}'),r()}catch(l){r(l)}}))},55428:function(e,t,n){n.d(t,{j:function(){return i}});let r={};function i(){return r}},18492:function(e,t,n){n.d(t,{d:function(){return i}});n(39527),n(67670),n(13334);var r=n(53907);function i(e,...t){const n=r.L.bind(null,e||t.find((e=>"object"==typeof e)));return t.map(n)}},32424:function(e,t,n){n.d(t,{E:function(){return o}});var r=n(53907),i=n(18112);function o(e,t,n){const o=(0,i.Q)(e,null==n?void 0:n.in);return isNaN(t)?(0,r.L)((null==n?void 0:n.in)||e,NaN):t?(o.setDate(o.getDate()+t),o):o}},76808:function(e,t,n){n.d(t,{I7:function(){return a},dP:function(){return i},jE:function(){return r},vh:function(){return s},yJ:function(){return o}});const r=6048e5,i=864e5,o=6e4,s=36e5,a=Symbol.for("constructDateFrom")},53907:function(e,t,n){n.d(t,{L:function(){return i}});var r=n(76808);function i(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&r.I7 in e?e[r.I7](t):e instanceof Date?new e.constructor(t):new Date(t)}},97836:function(e,t,n){n.d(t,{K:function(){return o}});n(71695),n(47021);var r=n(18492),i=n(7591);function o(e,t,n){const[o,s]=(0,r.d)(null==n?void 0:n.in,e,t);return+(0,i.b)(o)==+(0,i.b)(s)}},28866:function(e,t,n){n.d(t,{x:function(){return o}});n(71695),n(47021);var r=n(18492),i=n(29558);function o(e,t,n){const[o,s]=(0,r.d)(null==n?void 0:n.in,e,t);return+(0,i.z)(o,n)==+(0,i.z)(s,n)}},63454:function(e,t,n){n.d(t,{O:()=>s});var r=n("32424"),i=n("18112");function o(e,t){return(0,i.Q)(e,null==t?void 0:t.in).getDay()}function s(e,t,n){let i=t-o(e,n);return i<=0&&(i+=7),(0,r.E)(e,i,n)}},7591:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(18112);function i(e,t){const n=(0,r.Q)(e,null==t?void 0:t.in);return n.setHours(0,0,0,0),n}},29558:function(e,t,n){n.d(t,{z:function(){return o}});var r=n(55428),i=n(18112);function o(e,t){var n,o,s,a,l,c;const d=(0,r.j)(),u=null!==(n=null!==(o=null!==(s=null!==(a=null==t?void 0:t.weekStartsOn)&&void 0!==a?a:null==t||null===(l=t.locale)||void 0===l||null===(l=l.options)||void 0===l?void 0:l.weekStartsOn)&&void 0!==s?s:d.weekStartsOn)&&void 0!==o?o:null===(c=d.locale)||void 0===c||null===(c=c.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==n?n:0,h=(0,i.Q)(e,null==t?void 0:t.in),f=h.getDay(),g=(f<u?7:0)+f-u;return h.setDate(h.getDate()-g),h.setHours(0,0,0,0),h}},18112:function(e,t,n){n.d(t,{Q:function(){return i}});var r=n(53907);function i(e,t){return(0,r.L)(t||e,e)}}}]);
//# sourceMappingURL=5536.8d4f57637b4c69ae.js.map