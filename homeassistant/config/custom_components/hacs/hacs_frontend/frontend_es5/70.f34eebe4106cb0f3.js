"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["70"],{14995:function(i,e,t){t.r(e),t.d(e,{HaImagecropperDialog:function(){return _}});var a=t(73577),o=(t(71695),t(19134),t(97003),t(47021),t(72700),t(8038),t(71513),t(75656),t(50100),t(18084),t(31622),t(65509)),r=t.n(o),s=t(93528),c=t(57243),n=t(50778),p=t(35359),l=(t(44118),t(66193));let d,h,u,m=i=>i,_=(0,a.Z)([(0,n.Mo)("image-cropper-dialog")],(function(i,e){return{F:class extends e{constructor(...e){super(...e),i(this)}},d:[{kind:"field",decorators:[(0,n.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,n.SB)()],key:"_params",value:void 0},{kind:"field",decorators:[(0,n.SB)()],key:"_open",value(){return!1}},{kind:"field",decorators:[(0,n.IO)("img",!0)],key:"_image",value:void 0},{kind:"field",key:"_cropper",value:void 0},{kind:"field",decorators:[(0,n.SB)()],key:"_isTargetAspectRatio",value:void 0},{kind:"method",key:"showDialog",value:function(i){this._params=i,this._open=!0}},{kind:"method",key:"closeDialog",value:function(){var i;this._open=!1,this._params=void 0,null===(i=this._cropper)||void 0===i||i.destroy(),this._cropper=void 0,this._isTargetAspectRatio=!1}},{kind:"method",key:"updated",value:function(i){i.has("_params")&&this._params&&(this._cropper?this._cropper.replace(URL.createObjectURL(this._params.file)):(this._image.src=URL.createObjectURL(this._params.file),this._cropper=new(r())(this._image,{aspectRatio:this._params.options.aspectRatio,viewMode:1,dragMode:"move",minCropBoxWidth:50,ready:()=>{this._isTargetAspectRatio=this._checkMatchAspectRatio(),URL.revokeObjectURL(this._image.src)}})))}},{kind:"method",key:"_checkMatchAspectRatio",value:function(){var i;const e=null===(i=this._params)||void 0===i?void 0:i.options.aspectRatio;if(!e)return!0;const t=this._cropper.getImageData();if(t.aspectRatio===e)return!0;if(t.naturalWidth>t.naturalHeight){const i=t.naturalWidth/e;return Math.abs(i-t.naturalHeight)<=1}const a=t.naturalHeight*e;return Math.abs(a-t.naturalWidth)<=1}},{kind:"method",key:"render",value:function(){var i;return(0,c.dy)(d||(d=m`<ha-dialog @closed="${0}" scrimClickAction escapeKeyAction .open="${0}"> <div class="container ${0}"> <img alt="${0}"> </div> <mwc-button slot="secondaryAction" @click="${0}"> ${0} </mwc-button> ${0} <mwc-button slot="primaryAction" @click="${0}"> ${0} </mwc-button> </ha-dialog>`),this.closeDialog,this._open,(0,p.$)({round:Boolean(null===(i=this._params)||void 0===i?void 0:i.options.round)}),this.hass.localize("ui.dialogs.image_cropper.crop_image"),this.closeDialog,this.hass.localize("ui.common.cancel"),this._isTargetAspectRatio?(0,c.dy)(h||(h=m`<mwc-button slot="primaryAction" @click="${0}"> ${0} </mwc-button>`),this._useOriginal,this.hass.localize("ui.dialogs.image_cropper.use_original")):c.Ld,this._cropImage,this.hass.localize("ui.dialogs.image_cropper.crop"))}},{kind:"method",key:"_cropImage",value:function(){this._cropper.getCroppedCanvas().toBlob((i=>{if(!i)return;const e=new File([i],this._params.file.name,{type:this._params.options.type||this._params.file.type});this._params.croppedCallback(e),this.closeDialog()}),this._params.options.type||this._params.file.type,this._params.options.quality)}},{kind:"method",key:"_useOriginal",value:function(){this._params.croppedCallback(this._params.file),this.closeDialog()}},{kind:"get",static:!0,key:"styles",value:function(){return[l.yu,(0,c.iv)(u||(u=m`${0} .container{max-width:640px}img{max-width:100%}.container.round .cropper-face,.container.round .cropper-view-box{border-radius:50%}.cropper-line,.cropper-point,.cropper-point.point-se::before{background-color:var(--primary-color)}`),(0,c.$m)(s))]}}]}}),c.oi)}}]);
//# sourceMappingURL=70.f34eebe4106cb0f3.js.map