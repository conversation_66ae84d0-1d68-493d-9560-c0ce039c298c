"use strict";(self.webpackChunkhacs_frontend=self.webpackChunkhacs_frontend||[]).push([["6752"],{65417:function(e,t,i){i.a(e,(async function(e,a){try{i.d(t,{WB:function(){return c},p6:function(){return d}});i(63434),i(96829),i(39527),i(67670);var o=i(16485),n=i(27486),r=i(50177),s=i(70691),l=e([o,s]);[o,s]=l.then?(await l)():l;(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{weekday:"long",month:"long",day:"numeric",timeZone:(0,s.f)(e.time_zone,t)})));const d=(e,t,i)=>u(t,i.time_zone).format(e),u=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",month:"long",day:"numeric",timeZone:(0,s.f)(e.time_zone,t)}))),c=((0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",month:"short",day:"numeric",timeZone:(0,s.f)(e.time_zone,t)}))),(e,t,i)=>{var a,o,n,s;const l=h(t,i.time_zone);if(t.date_format===r.t6.language||t.date_format===r.t6.system)return l.format(e);const d=l.formatToParts(e),u=null===(a=d.find((e=>"literal"===e.type)))||void 0===a?void 0:a.value,c=null===(o=d.find((e=>"day"===e.type)))||void 0===o?void 0:o.value,m=null===(n=d.find((e=>"month"===e.type)))||void 0===n?void 0:n.value,f=null===(s=d.find((e=>"year"===e.type)))||void 0===s?void 0:s.value,p=d.at(d.length-1);let v="literal"===(null==p?void 0:p.type)?null==p?void 0:p.value:"";"bg"===t.language&&t.date_format===r.t6.YMD&&(v="");return{[r.t6.DMY]:`${c}${u}${m}${u}${f}${v}`,[r.t6.MDY]:`${m}${u}${c}${u}${f}${v}`,[r.t6.YMD]:`${f}${u}${m}${u}${c}${v}`}[t.date_format]}),h=(0,n.Z)(((e,t)=>{const i=e.date_format===r.t6.system?void 0:e.language;return e.date_format===r.t6.language||(e.date_format,r.t6.system),new Intl.DateTimeFormat(i,{year:"numeric",month:"numeric",day:"numeric",timeZone:(0,s.f)(e.time_zone,t)})}));(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{day:"numeric",month:"short",timeZone:(0,s.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{month:"long",year:"numeric",timeZone:(0,s.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{month:"long",timeZone:(0,s.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",timeZone:(0,s.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{weekday:"long",timeZone:(0,s.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{weekday:"short",timeZone:(0,s.f)(e.time_zone,t)})));a()}catch(d){a(d)}}))},69027:function(e,t,i){i.a(e,(async function(e,a){try{i.d(t,{o0:function(){return c}});var o=i(16485),n=i(27486),r=i(65417),s=i(823),l=i(70691),d=i(51873),u=e([o,r,s,l]);[o,r,s,l]=u.then?(await u)():u;const c=(e,t,i)=>h(t,i.time_zone).format(e),h=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",month:"long",day:"numeric",hour:(0,d.y)(e)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,d.y)(e)?"h12":"h23",timeZone:(0,l.f)(e.time_zone,t)})));(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",month:"short",day:"numeric",hour:(0,d.y)(e)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,d.y)(e)?"h12":"h23",timeZone:(0,l.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{month:"short",day:"numeric",hour:(0,d.y)(e)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,d.y)(e)?"h12":"h23",timeZone:(0,l.f)(e.time_zone,t)}))),(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{year:"numeric",month:"long",day:"numeric",hour:(0,d.y)(e)?"numeric":"2-digit",minute:"2-digit",second:"2-digit",hourCycle:(0,d.y)(e)?"h12":"h23",timeZone:(0,l.f)(e.time_zone,t)})));a()}catch(c){a(c)}}))},823:function(e,t,i){i.a(e,(async function(e,a){try{i.d(t,{Vu:function(){return c},Zs:function(){return p},mr:function(){return d},xO:function(){return m}});var o=i(16485),n=i(27486),r=i(70691),s=i(51873),l=e([o,r]);[o,r]=l.then?(await l)():l;const d=(e,t,i)=>u(t,i.time_zone).format(e),u=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{hour:"numeric",minute:"2-digit",hourCycle:(0,s.y)(e)?"h12":"h23",timeZone:(0,r.f)(e.time_zone,t)}))),c=(e,t,i)=>h(t,i.time_zone).format(e),h=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{hour:(0,s.y)(e)?"numeric":"2-digit",minute:"2-digit",second:"2-digit",hourCycle:(0,s.y)(e)?"h12":"h23",timeZone:(0,r.f)(e.time_zone,t)}))),m=(e,t,i)=>f(t,i.time_zone).format(e),f=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat(e.language,{weekday:"long",hour:(0,s.y)(e)?"numeric":"2-digit",minute:"2-digit",hourCycle:(0,s.y)(e)?"h12":"h23",timeZone:(0,r.f)(e.time_zone,t)}))),p=(e,t,i)=>v(t,i.time_zone).format(e),v=(0,n.Z)(((e,t)=>new Intl.DateTimeFormat("en-GB",{hour:"numeric",minute:"2-digit",hour12:!1,timeZone:(0,r.f)(e.time_zone,t)})));a()}catch(d){a(d)}}))},70691:function(e,t,i){i.a(e,(async function(e,a){try{i.d(t,{f:function(){return h}});var o,n,r,s=i(16485),l=i(50177),d=e([s]);s=(d.then?(await d)():d)[0];const u=null===(o=Intl.DateTimeFormat)||void 0===o||null===(n=(r=o.call(Intl)).resolvedOptions)||void 0===n?void 0:n.call(r).timeZone,c=null!=u?u:"UTC",h=(e,t)=>e===l.c_.local&&u?c:t;a()}catch(u){a(u)}}))},51873:function(e,t,i){i.d(t,{y:function(){return n}});i(19083),i(61006);var a=i(27486),o=i(50177);const n=(0,a.Z)((e=>{if(e.time_format===o.zt.language||e.time_format===o.zt.system){const t=e.time_format===o.zt.language?e.language:void 0;return new Date("January 1, 2023 22:00:00").toLocaleString(t).includes("10")}return e.time_format===o.zt.am_pm}))},17031:function(e,t,i){i.d(t,{F:function(){return a}});i(52247),i(71695),i(40251),i(13334),i(47021);const a=async e=>{if(!e.parentNode)throw new Error("Cannot setup Leaflet map on disconnected element");const t=(await Promise.all([i.e("9525"),i.e("6438")]).then(i.t.bind(i,94010,23))).default;t.Icon.Default.imagePath="/static/images/leaflet/images/";const a=t.map(e),n=document.createElement("link");n.setAttribute("href","/static/images/leaflet/leaflet.css"),n.setAttribute("rel","stylesheet"),e.parentNode.appendChild(n),a.setView([52.3731339,4.8903147],13);return[a,t,o(t).addTo(a)]},o=e=>e.tileLayer("https://basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}"+(e.Browser.retina?"@2x.png":".png"),{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>, &copy; <a href="https://carto.com/attributions">CARTO</a>',subdomains:"abcd",minZoom:0,maxZoom:20})},20663:function(e,t,i){var a=i(73577),o=(i(71695),i(47021),i(57243)),n=i(50778);let r,s,l=e=>e;(0,a.Z)([(0,n.Mo)("ha-input-helper-text")],(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"method",key:"render",value:function(){return(0,o.dy)(r||(r=l`<slot></slot>`))}},{kind:"field",static:!0,key:"styles",value(){return(0,o.iv)(s||(s=l`:host{display:block;color:var(--mdc-text-field-label-ink-color,rgba(0,0,0,.6));font-size:.75rem;padding-left:16px;padding-right:16px;padding-inline-start:16px;padding-inline-end:16px}`))}}]}}),o.oi)},68313:function(e,t,i){i.a(e,(async function(e,a){try{i.r(t),i.d(t,{HaLocationSelector:function(){return f}});var o=i(73577),n=(i(71695),i(19423),i(47021),i(57243)),r=i(50778),s=i(27486),l=i(11297),d=i(3506),u=(i(42877),e([d]));d=(u.then?(await u)():u)[0];let c,h,m=e=>e,f=(0,o.Z)([(0,r.Mo)("ha-selector-location")],(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"selector",value:void 0},{kind:"field",decorators:[(0,r.Cb)({type:Object})],key:"value",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"label",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"helper",value:void 0},{kind:"field",decorators:[(0,r.Cb)({type:Boolean,reflect:!0})],key:"disabled",value(){return!1}},{kind:"field",key:"_schema",value(){return(0,s.Z)(((e,t)=>[{name:"",type:"grid",schema:[{name:"latitude",required:!0,selector:{number:{step:"any"}}},{name:"longitude",required:!0,selector:{number:{step:"any"}}}]},...e?[{name:"radius",required:!0,default:1e3,disabled:!!t,selector:{number:{min:0,step:1,mode:"box"}}}]:[]]))}},{kind:"method",key:"willUpdate",value:function(){var e;this.value||(this.value={latitude:this.hass.config.latitude,longitude:this.hass.config.longitude,radius:null!==(e=this.selector.location)&&void 0!==e&&e.radius?1e3:void 0})}},{kind:"method",key:"render",value:function(){var e,t;return(0,n.dy)(c||(c=m` <p>${0}</p> <ha-locations-editor class="flex" .hass="${0}" .helper="${0}" .locations="${0}" @location-updated="${0}" @radius-updated="${0}" pin-on-click></ha-locations-editor> <ha-form .hass="${0}" .schema="${0}" .data="${0}" .computeLabel="${0}" .disabled="${0}" @value-changed="${0}"></ha-form> `),this.label?this.label:"",this.hass,this.helper,this._location(this.selector,this.value),this._locationChanged,this._radiusChanged,this.hass,this._schema(null===(e=this.selector.location)||void 0===e?void 0:e.radius,null===(t=this.selector.location)||void 0===t?void 0:t.radius_readonly),this.value,this._computeLabel,this.disabled,this._valueChanged)}},{kind:"field",key:"_location",value(){return(0,s.Z)(((e,t)=>{var i,a,o,n,r,s;const l=getComputedStyle(this),d=null!==(i=e.location)&&void 0!==i&&i.radius?l.getPropertyValue("--zone-radius-color")||l.getPropertyValue("--accent-color"):void 0;return[{id:"location",latitude:!t||isNaN(t.latitude)?this.hass.config.latitude:t.latitude,longitude:!t||isNaN(t.longitude)?this.hass.config.longitude:t.longitude,radius:null!==(a=e.location)&&void 0!==a&&a.radius?(null==t?void 0:t.radius)||1e3:void 0,radius_color:d,icon:null!==(o=e.location)&&void 0!==o&&o.icon||null!==(n=e.location)&&void 0!==n&&n.radius?"mdi:map-marker-radius":"mdi:map-marker",location_editable:!0,radius_editable:!(null===(r=e.location)||void 0===r||!r.radius||null!==(s=e.location)&&void 0!==s&&s.radius_readonly)}]}))}},{kind:"method",key:"_locationChanged",value:function(e){const[t,i]=e.detail.location;(0,l.B)(this,"value-changed",{value:Object.assign(Object.assign({},this.value),{},{latitude:t,longitude:i})})}},{kind:"method",key:"_radiusChanged",value:function(e){const t=Math.round(e.detail.radius);(0,l.B)(this,"value-changed",{value:Object.assign(Object.assign({},this.value),{},{radius:t})})}},{kind:"method",key:"_valueChanged",value:function(e){var t,i;e.stopPropagation();const a=e.detail.value,o=Math.round(e.detail.value.radius);(0,l.B)(this,"value-changed",{value:Object.assign({latitude:a.latitude,longitude:a.longitude},null===(t=this.selector.location)||void 0===t||!t.radius||null!==(i=this.selector.location)&&void 0!==i&&i.radius_readonly?{}:{radius:o})})}},{kind:"field",key:"_computeLabel",value(){return e=>e.name?this.hass.localize(`ui.components.selectors.location.${e.name}`):""}},{kind:"field",static:!0,key:"styles",value(){return(0,n.iv)(h||(h=m`ha-locations-editor{display:block;height:400px;margin-bottom:16px}p{margin-top:0}`))}}]}}),n.oi);a()}catch(c){a(c)}}))},13270:function(e,t,i){i.a(e,(async function(e,t){try{var a=i(73577),o=(i(71695),i(47021),i(57243)),n=i(50778),r=i(31050),s=i(43420),l=i(92014),d=(i(10508),e([l]));l=(d.then?(await d)():d)[0];let u,c,h,m,f=e=>e;(0,a.Z)([(0,n.Mo)("ha-state-icon")],(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"field",decorators:[(0,n.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:!1})],key:"stateObj",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:!1})],key:"stateValue",value:void 0},{kind:"field",decorators:[(0,n.Cb)()],key:"icon",value:void 0},{kind:"method",key:"render",value:function(){var e,t;const i=this.icon||this.stateObj&&(null===(e=this.hass)||void 0===e||null===(e=e.entities[this.stateObj.entity_id])||void 0===e?void 0:e.icon)||(null===(t=this.stateObj)||void 0===t?void 0:t.attributes.icon);if(i)return(0,o.dy)(u||(u=f`<ha-icon .icon="${0}"></ha-icon>`),i);if(!this.stateObj)return o.Ld;if(!this.hass)return this._renderFallback();const a=(0,l.gD)(this.hass,this.stateObj,this.stateValue).then((e=>e?(0,o.dy)(c||(c=f`<ha-icon .icon="${0}"></ha-icon>`),e):this._renderFallback()));return(0,o.dy)(h||(h=f`${0}`),(0,r.C)(a))}},{kind:"method",key:"_renderFallback",value:function(){const e=(0,s.N)(this.stateObj);return(0,o.dy)(m||(m=f` <ha-svg-icon .path="${0}"></ha-svg-icon> `),l.Ls[e]||l.Rb)}}]}}),o.oi);t()}catch(u){t(u)}}))},1155:function(e,t,i){i.a(e,(async function(e,t){try{var a=i(73577),o=(i(71695),i(47021),i(57243)),n=i(50778),r=i(46799),s=i(11297),l=i(13270),d=e([l]);l=(d.then?(await d)():d)[0];let u,c,h,m,f=e=>e,p=(0,a.Z)(null,(function(e,t){return{F:class extends t{constructor(...t){super(...t),e(this)}},d:[{kind:"field",decorators:[(0,n.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:"entity-id",reflect:!0})],key:"entityId",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:"entity-name"})],key:"entityName",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:"entity-picture"})],key:"entityPicture",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:"entity-color"})],key:"entityColor",value:void 0},{kind:"field",decorators:[(0,n.Cb)({attribute:"show-icon",type:Boolean})],key:"showIcon",value(){return!1}},{kind:"method",key:"render",value:function(){var e;return(0,o.dy)(u||(u=f` <div class="marker ${0}" style="${0}" @click="${0}"> ${0} </div> `),this.entityPicture?"picture":"",(0,r.V)({"border-color":this.entityColor}),this._badgeTap,this.entityPicture?(0,o.dy)(c||(c=f`<div class="entity-picture" style="${0}"></div>`),(0,r.V)({"background-image":`url(${this.entityPicture})`})):this.showIcon&&this.entityId?(0,o.dy)(h||(h=f`<ha-state-icon .hass="${0}" .stateObj="${0}"></ha-state-icon>`),this.hass,null===(e=this.hass)||void 0===e?void 0:e.states[this.entityId]):this.entityName)}},{kind:"method",key:"_badgeTap",value:function(e){e.stopPropagation(),this.entityId&&(0,s.B)(this,"hass-more-info",{entityId:this.entityId})}},{kind:"get",static:!0,key:"styles",value:function(){return(0,o.iv)(m||(m=f`.marker{display:flex;justify-content:center;text-align:center;align-items:center;box-sizing:border-box;width:48px;height:48px;font-size:var(--ha-marker-font-size, 1.5em);border-radius:var(--ha-marker-border-radius,50%);border:1px solid var(--ha-marker-color,var(--primary-color));color:var(--primary-text-color);background-color:var(--card-background-color)}.marker.picture{overflow:hidden}.entity-picture{background-size:cover;height:100%;width:100%}`))}}]}}),o.oi);customElements.define("ha-entity-marker",p),t()}catch(u){t(u)}}))},3506:function(e,t,i){i.a(e,(async function(e,t){try{var a=i(73577),o=i(72621),n=(i(71695),i(92745),i(40251),i(39527),i(99790),i(41360),i(13334),i(47021),i(57243)),r=i(50778),s=i(27486),l=i(11297),d=(i(20663),i(48612)),u=e([d]);d=(u.then?(await u)():u)[0];let c,h,m,f=e=>e;(0,a.Z)([(0,r.Mo)("ha-locations-editor")],(function(e,t){class a extends t{constructor(){super(),e(this),this._loadPromise=Promise.all([i.e("9525"),i.e("6438")]).then(i.t.bind(i,94010,23)).then((e=>i.e("6090").then(i.t.bind(i,39135,23)).then((()=>(this.Leaflet=e.default,this._updateMarkers(),this.updateComplete.then((()=>this.fitMap())))))))}}return{F:a,d:[{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,r.Cb)({attribute:!1})],key:"locations",value:void 0},{kind:"field",decorators:[(0,r.Cb)()],key:"helper",value:void 0},{kind:"field",decorators:[(0,r.Cb)({attribute:"auto-fit",type:Boolean})],key:"autoFit",value(){return!1}},{kind:"field",decorators:[(0,r.Cb)({type:Number})],key:"zoom",value(){return 16}},{kind:"field",decorators:[(0,r.Cb)({attribute:"theme-mode",type:String})],key:"themeMode",value(){return"auto"}},{kind:"field",decorators:[(0,r.Cb)({type:Boolean,attribute:"pin-on-click"})],key:"pinOnClick",value(){return!1}},{kind:"field",decorators:[(0,r.SB)()],key:"_locationMarkers",value:void 0},{kind:"field",decorators:[(0,r.SB)()],key:"_circles",value(){return{}}},{kind:"field",decorators:[(0,r.IO)("ha-map",!0)],key:"map",value:void 0},{kind:"field",key:"Leaflet",value:void 0},{kind:"field",key:"_loadPromise",value:void 0},{kind:"method",key:"fitMap",value:function(e){this.map.fitMap(e)}},{kind:"method",key:"fitBounds",value:function(e,t){this.map.fitBounds(e,t)}},{kind:"method",key:"fitMarker",value:async function(e,t){if(this.Leaflet||await this._loadPromise,!this.map.leafletMap||!this._locationMarkers)return;const i=this._locationMarkers[e];if(i)if("getBounds"in i)this.map.leafletMap.fitBounds(i.getBounds()),i.bringToFront();else{const a=this._circles[e];a?this.map.leafletMap.fitBounds(a.getBounds()):this.map.leafletMap.setView(i.getLatLng(),(null==t?void 0:t.zoom)||this.zoom)}}},{kind:"method",key:"render",value:function(){return(0,n.dy)(c||(c=f` <ha-map .hass="${0}" .layers="${0}" .zoom="${0}" .autoFit="${0}" .themeMode="${0}" .clickable="${0}" @map-clicked="${0}"></ha-map> ${0} `),this.hass,this._getLayers(this._circles,this._locationMarkers),this.zoom,this.autoFit,this.themeMode,this.pinOnClick,this._mapClicked,this.helper?(0,n.dy)(h||(h=f`<ha-input-helper-text>${0}</ha-input-helper-text>`),this.helper):"")}},{kind:"field",key:"_getLayers",value(){return(0,s.Z)(((e,t)=>{const i=[];return Array.prototype.push.apply(i,Object.values(e)),t&&Array.prototype.push.apply(i,Object.values(t)),i}))}},{kind:"method",key:"willUpdate",value:function(e){(0,o.Z)(a,"willUpdate",this,3)([e]),this.Leaflet&&e.has("locations")&&this._updateMarkers()}},{kind:"method",key:"updated",value:function(e){if(this.Leaflet&&e.has("locations")){var t;const a=e.get("locations"),o=null===(t=this.locations)||void 0===t?void 0:t.filter(((e,t)=>{var i,o;return!a[t]||(e.latitude!==a[t].latitude||e.longitude!==a[t].longitude)&&(null===(i=this.map.leafletMap)||void 0===i?void 0:i.getBounds().contains({lat:a[t].latitude,lng:a[t].longitude}))&&!(null!==(o=this.map.leafletMap)&&void 0!==o&&o.getBounds().contains({lat:e.latitude,lng:e.longitude}))}));var i;if(1===(null==o?void 0:o.length))null===(i=this.map.leafletMap)||void 0===i||i.panTo({lat:o[0].latitude,lng:o[0].longitude})}}},{kind:"method",key:"_normalizeLongitude",value:function(e){return Math.abs(e)>180?(e%360+540)%360-180:e}},{kind:"method",key:"_updateLocation",value:function(e){const t=e.target,i=t.getLatLng(),a=[i.lat,this._normalizeLongitude(i.lng)];(0,l.B)(this,"location-updated",{id:t.id,location:a},{bubbles:!1})}},{kind:"method",key:"_updateRadius",value:function(e){const t=e.target,i=this._locationMarkers[t.id];(0,l.B)(this,"radius-updated",{id:t.id,radius:i.getRadius()},{bubbles:!1})}},{kind:"method",key:"_markerClicked",value:function(e){const t=e.target;(0,l.B)(this,"marker-clicked",{id:t.id},{bubbles:!1})}},{kind:"method",key:"_mapClicked",value:function(e){if(this.pinOnClick&&this._locationMarkers){const i=Object.keys(this._locationMarkers)[0],a=[e.detail.location[0],this._normalizeLongitude(e.detail.location[1])];var t;if((0,l.B)(this,"location-updated",{id:i,location:a},{bubbles:!1}),a[1]!==e.detail.location[1])null===(t=this.map.leafletMap)||void 0===t||t.panTo({lat:a[0],lng:a[1]})}}},{kind:"method",key:"_updateMarkers",value:function(){if(!this.locations||!this.locations.length)return this._circles={},void(this._locationMarkers=void 0);const e={},t={},i=getComputedStyle(this).getPropertyValue("--accent-color");this.locations.forEach((a=>{let o;if(a.icon||a.iconPath){const e=document.createElement("div");let t;e.className="named-icon",void 0!==a.name&&(e.innerText=a.name),a.icon?(t=document.createElement("ha-icon"),t.setAttribute("icon",a.icon)):(t=document.createElement("ha-svg-icon"),t.setAttribute("path",a.iconPath)),e.prepend(t),o=this.Leaflet.divIcon({html:e.outerHTML,iconSize:[24,24],className:"light"})}if(a.radius){const n=this.Leaflet.circle([a.latitude,a.longitude],{color:a.radius_color||i,radius:a.radius});a.radius_editable||a.location_editable?(n.editing.enable(),n.addEventListener("add",(()=>{const e=n.editing._moveMarker,t=n.editing._resizeMarkers[0];o&&e.setIcon(o),t.id=e.id=a.id,e.addEventListener("dragend",(e=>this._updateLocation(e))).addEventListener("click",(e=>this._markerClicked(e))),a.radius_editable?t.addEventListener("dragend",(e=>this._updateRadius(e))):t.remove()})),e[a.id]=n):t[a.id]=n}if(!a.radius||!a.radius_editable&&!a.location_editable){const t={title:a.name,draggable:a.location_editable};o&&(t.icon=o);const i=this.Leaflet.marker([a.latitude,a.longitude],t).addEventListener("dragend",(e=>this._updateLocation(e))).addEventListener("click",(e=>this._markerClicked(e)));i.id=a.id,e[a.id]=i}})),this._circles=t,this._locationMarkers=e,(0,l.B)(this,"markers-updated")}},{kind:"get",static:!0,key:"styles",value:function(){return(0,n.iv)(m||(m=f`ha-map{display:block;height:100%}`))}}]}}),n.oi);t()}catch(c){t(c)}}))},48612:function(e,t,i){i.a(e,(async function(e,t){try{var a=i(73577),o=i(72621),n=i(31948),r=(i(71695),i(92745),i(40251),i(39527),i(41360),i(13334),i(47021),i(91155)),s=i(57243),l=i(50778),d=i(11297),u=i(69027),c=i(823),h=i(17031),m=i(43420),f=i(73525),p=i(21234),v=(i(59897),i(1155)),y=e([n,v,u,c]);[n,v,u,c]=y.then?(await y)():y;let k,g=e=>e;const b=e=>"string"==typeof e?e:e.entity_id;(0,a.Z)([(0,l.Mo)("ha-map")],(function(e,t){class i extends t{constructor(...t){super(...t),e(this)}}return{F:i,d:[{kind:"field",decorators:[(0,l.Cb)({attribute:!1})],key:"hass",value:void 0},{kind:"field",decorators:[(0,l.Cb)({attribute:!1})],key:"entities",value:void 0},{kind:"field",decorators:[(0,l.Cb)({attribute:!1})],key:"paths",value:void 0},{kind:"field",decorators:[(0,l.Cb)({attribute:!1})],key:"layers",value:void 0},{kind:"field",decorators:[(0,l.Cb)({type:Boolean})],key:"clickable",value(){return!1}},{kind:"field",decorators:[(0,l.Cb)({attribute:"auto-fit",type:Boolean})],key:"autoFit",value(){return!1}},{kind:"field",decorators:[(0,l.Cb)({attribute:"render-passive",type:Boolean})],key:"renderPassive",value(){return!1}},{kind:"field",decorators:[(0,l.Cb)({attribute:"interactive-zones",type:Boolean})],key:"interactiveZones",value(){return!1}},{kind:"field",decorators:[(0,l.Cb)({attribute:"fit-zones",type:Boolean})],key:"fitZones",value(){return!1}},{kind:"field",decorators:[(0,l.Cb)({attribute:"theme-mode",type:String})],key:"themeMode",value(){return"auto"}},{kind:"field",decorators:[(0,l.Cb)({type:Number})],key:"zoom",value(){return 14}},{kind:"field",decorators:[(0,l.SB)()],key:"_loaded",value(){return!1}},{kind:"field",key:"leafletMap",value:void 0},{kind:"field",key:"Leaflet",value:void 0},{kind:"field",key:"_resizeObserver",value:void 0},{kind:"field",key:"_mapItems",value(){return[]}},{kind:"field",key:"_mapFocusItems",value(){return[]}},{kind:"field",key:"_mapZones",value(){return[]}},{kind:"field",key:"_mapFocusZones",value(){return[]}},{kind:"field",key:"_mapPaths",value(){return[]}},{kind:"field",key:"_clickCount",value(){return 0}},{kind:"method",key:"connectedCallback",value:function(){(0,o.Z)(i,"connectedCallback",this,3)([]),this._loadMap(),this._attachObserver()}},{kind:"method",key:"disconnectedCallback",value:function(){(0,o.Z)(i,"disconnectedCallback",this,3)([]),this.leafletMap&&(this.leafletMap.remove(),this.leafletMap=void 0,this.Leaflet=void 0),this._loaded=!1,this._resizeObserver&&this._resizeObserver.unobserve(this)}},{kind:"method",key:"update",value:function(e){var t,a;if((0,o.Z)(i,"update",this,3)([e]),!this._loaded)return;let n=!1;const r=e.get("hass");if(e.has("_loaded")||e.has("entities"))this._drawEntities(),n=!0;else if(this._loaded&&r&&this.entities)for(const i of this.entities)if(r.states[b(i)]!==this.hass.states[b(i)]){this._drawEntities(),n=!0;break}(e.has("_loaded")||e.has("paths"))&&this._drawPaths(),(e.has("_loaded")||e.has("layers"))&&(this._drawLayers(e.get("layers")),n=!0),(e.has("_loaded")||this.autoFit&&n)&&this.fitMap(),e.has("zoom")&&this.leafletMap.setZoom(this.zoom),(e.has("themeMode")||e.has("hass")&&(!r||(null===(t=r.themes)||void 0===t?void 0:t.darkMode)!==(null===(a=this.hass.themes)||void 0===a?void 0:a.darkMode)))&&this._updateMapStyle()}},{kind:"get",key:"_darkMode",value:function(){return"dark"===this.themeMode||"auto"===this.themeMode&&Boolean(this.hass.themes.darkMode)}},{kind:"method",key:"_updateMapStyle",value:function(){const e=this.renderRoot.querySelector("#map");e.classList.toggle("clickable",this.clickable),e.classList.toggle("dark",this._darkMode),e.classList.toggle("forced-dark","dark"===this.themeMode),e.classList.toggle("forced-light","light"===this.themeMode)}},{kind:"field",key:"_loading",value(){return!1}},{kind:"method",key:"_loadMap",value:async function(){if(this._loading)return;let e=this.shadowRoot.getElementById("map");e||(e=document.createElement("div"),e.id="map",this.shadowRoot.append(e)),this._loading=!0;try{[this.leafletMap,this.Leaflet]=await(0,h.F)(e),this._updateMapStyle(),this.leafletMap.on("click",(e=>{0===this._clickCount&&setTimeout((()=>{1===this._clickCount&&(0,d.B)(this,"map-clicked",{location:[e.latlng.lat,e.latlng.lng]}),this._clickCount=0}),250),this._clickCount++})),this._loaded=!0}finally{this._loading=!1}}},{kind:"method",key:"fitMap",value:function(e){var t,i,a,o;if(!this.leafletMap||!this.Leaflet||!this.hass)return;if(!(this._mapFocusItems.length||this._mapFocusZones.length||null!==(t=this.layers)&&void 0!==t&&t.length))return void this.leafletMap.setView(new this.Leaflet.LatLng(this.hass.config.latitude,this.hass.config.longitude),(null==e?void 0:e.zoom)||this.zoom);let n=this.Leaflet.latLngBounds(this._mapFocusItems?this._mapFocusItems.map((e=>e.getLatLng())):[]);null===(i=this._mapFocusZones)||void 0===i||i.forEach((e=>{n.extend("getBounds"in e?e.getBounds():e.getLatLng())})),null===(a=this.layers)||void 0===a||a.forEach((e=>{n.extend("getBounds"in e?e.getBounds():e.getLatLng())})),n=n.pad(null!==(o=null==e?void 0:e.pad)&&void 0!==o?o:.5),this.leafletMap.fitBounds(n,{maxZoom:(null==e?void 0:e.zoom)||this.zoom})}},{kind:"method",key:"fitBounds",value:function(e,t){var i;if(!this.leafletMap||!this.Leaflet||!this.hass)return;const a=this.Leaflet.latLngBounds(e).pad(null!==(i=null==t?void 0:t.pad)&&void 0!==i?i:.5);this.leafletMap.fitBounds(a,{maxZoom:(null==t?void 0:t.zoom)||this.zoom})}},{kind:"method",key:"_drawLayers",value:function(e){if(e&&e.forEach((e=>e.remove())),!this.layers)return;const t=this.leafletMap;this.layers.forEach((e=>{t.addLayer(e)}))}},{kind:"method",key:"_computePathTooltip",value:function(e,t){let i;return i=e.fullDatetime?(0,u.o0)(t.timestamp,this.hass.locale,this.hass.config):(0,r.z)(t.timestamp)?(0,c.Vu)(t.timestamp,this.hass.locale,this.hass.config):(0,c.xO)(t.timestamp,this.hass.locale,this.hass.config),`${e.name}<br>${i}`}},{kind:"method",key:"_drawPaths",value:function(){const e=this.hass,t=this.leafletMap,i=this.Leaflet;if(!e||!t||!i)return;if(this._mapPaths.length&&(this._mapPaths.forEach((e=>e.remove())),this._mapPaths=[]),!this.paths)return;const a=getComputedStyle(this).getPropertyValue("--dark-primary-color");this.paths.forEach((e=>{let o,n;e.gradualOpacity&&(o=e.gradualOpacity/(e.points.length-2),n=1-e.gradualOpacity);for(let t=0;t<e.points.length-1;t++){const r=e.gradualOpacity?n+t*o:void 0;this._mapPaths.push(i.circleMarker(e.points[t].point,{radius:p.T?8:3,color:e.color||a,opacity:r,fillOpacity:r,interactive:!0}).bindTooltip(this._computePathTooltip(e,e.points[t]),{direction:"top"})),this._mapPaths.push(i.polyline([e.points[t].point,e.points[t+1].point],{color:e.color||a,opacity:r,interactive:!1}))}const r=e.points.length-1;if(r>=0){const t=e.gradualOpacity?n+r*o:void 0;this._mapPaths.push(i.circleMarker(e.points[r].point,{radius:p.T?8:3,color:e.color||a,opacity:t,fillOpacity:t,interactive:!0}).bindTooltip(this._computePathTooltip(e,e.points[r]),{direction:"top"}))}this._mapPaths.forEach((e=>t.addLayer(e)))}))}},{kind:"method",key:"_drawEntities",value:function(){const e=this.hass,t=this.leafletMap,i=this.Leaflet;if(!e||!t||!i)return;if(this._mapItems.length&&(this._mapItems.forEach((e=>e.remove())),this._mapItems=[],this._mapFocusItems=[]),this._mapZones.length&&(this._mapZones.forEach((e=>e.remove())),this._mapZones=[],this._mapFocusZones=[]),!this.entities)return;const a=getComputedStyle(this),o=a.getPropertyValue("--accent-color"),n=a.getPropertyValue("--secondary-text-color"),r=a.getPropertyValue("--dark-primary-color"),s=this._darkMode?"dark":"light";for(const l of this.entities){const t=e.states[b(l)];if(!t)continue;const a="string"!=typeof l?l.name:void 0,d=null!=a?a:(0,f.C)(t),{latitude:u,longitude:c,passive:h,icon:p,radius:v,entity_picture:y,gps_accuracy:k}=t.attributes;if(!u||!c)continue;if("zone"===(0,m.N)(t)){if(h&&!this.renderPassive)continue;let e="";if(p){const t=document.createElement("ha-icon");t.setAttribute("icon",p),e=t.outerHTML}else{const t=document.createElement("span");t.innerHTML=d,e=t.outerHTML}this._mapZones.push(i.marker([u,c],{icon:i.divIcon({html:e,iconSize:[24,24],className:s}),interactive:this.interactiveZones,title:d}));const t=i.circle([u,c],{interactive:!1,color:h?n:o,radius:v});this._mapZones.push(t),!this.fitZones||"string"!=typeof l&&!1===l.focus||this._mapFocusZones.push(t);continue}const g="string"!=typeof l&&"state"===l.label_mode?this.hass.formatEntityState(t):null!=a?a:d.split(" ").map((e=>e[0])).join("").substr(0,3),_=document.createElement("ha-entity-marker");_.hass=this.hass,_.showIcon="string"!=typeof l&&"icon"===l.label_mode,_.entityId=b(l),_.entityName=g,_.entityPicture=!y||"string"!=typeof l&&l.label_mode?"":this.hass.hassUrl(y),"string"!=typeof l&&(_.entityColor=l.color);const M=i.marker([u,c],{icon:i.divIcon({html:_,iconSize:[48,48],className:""}),title:d});this._mapItems.push(M),"string"!=typeof l&&!1===l.focus||this._mapFocusItems.push(M),k&&this._mapItems.push(i.circle([u,c],{interactive:!1,color:r,radius:k}))}this._mapItems.forEach((e=>t.addLayer(e))),this._mapZones.forEach((e=>t.addLayer(e)))}},{kind:"method",key:"_attachObserver",value:async function(){this._resizeObserver||(this._resizeObserver=new ResizeObserver((()=>{var e;null===(e=this.leafletMap)||void 0===e||e.invalidateSize({debounceMoveend:!0})}))),this._resizeObserver.observe(this)}},{kind:"get",static:!0,key:"styles",value:function(){return(0,s.iv)(k||(k=g`:host{display:block;height:300px}#map{height:100%}#map.clickable{cursor:pointer}#map.dark{background:#090909}#map.forced-dark{color:#fff;--map-filter:invert(0.9) hue-rotate(170deg) brightness(1.5) contrast(1.2) saturate(0.3)}#map.forced-light{background:#fff;color:#000;--map-filter:invert(0)}#map.clickable:active,#map:active{cursor:grabbing;cursor:-moz-grabbing;cursor:-webkit-grabbing}.leaflet-tile-pane{filter:var(--map-filter)}.dark .leaflet-bar a{background-color:#1c1c1c;color:#fff}.dark .leaflet-bar a:hover{background-color:#313131}.leaflet-marker-draggable{cursor:move!important}.leaflet-edit-resize{border-radius:50%;cursor:nesw-resize!important}.named-icon{display:flex;align-items:center;justify-content:center;flex-direction:column;text-align:center;color:var(--primary-text-color)}.leaflet-pane{z-index:0!important}.leaflet-bottom,.leaflet-control,.leaflet-top{z-index:1!important}.leaflet-tooltip{padding:8px;font-size:90%;background:rgba(80,80,80,.9)!important;color:#fff!important;border-radius:4px;box-shadow:none!important;text-align:center}`))}}]}}),s.fl);t()}catch(k){t(k)}}))},21234:function(e,t,i){i.d(t,{T:function(){return a}});const a="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0}}]);
//# sourceMappingURL=6752.8b9af4482e4cf2ce.js.map