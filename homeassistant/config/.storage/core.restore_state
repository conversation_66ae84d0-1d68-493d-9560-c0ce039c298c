{"version": 1, "minor_version": 1, "key": "core.restore_state", "data": [{"state": {"entity_id": "conversation.home_assistant", "state": "unknown", "attributes": {"friendly_name": "Home Assistant", "supported_features": 1}, "last_changed": "2025-08-26T17:29:47.635689+00:00", "last_reported": "2025-08-26T17:29:47.635689+00:00", "last_updated": "2025-08-26T17:29:47.635689+00:00", "context": {"id": "01K3KPVHBK16F82V0YKRKG0T6B", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.backup_automatic_backup", "state": "2025-08-25T21:17:13.276+00:00", "attributes": {"event_types": ["completed", "failed", "in_progress"], "event_type": "completed", "backup_stage": null, "failed_reason": null, "friendly_name": "Backup Automatic backup"}, "last_changed": "2025-08-26T17:29:47.660286+00:00", "last_reported": "2025-08-26T17:29:47.660286+00:00", "last_updated": "2025-08-26T17:29:47.660286+00:00", "context": {"id": "01K3KPVHCC4JS8GZHX45QYEBTN", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "completed", "last_event_attributes": {"backup_stage": null, "failed_reason": null}}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "person.karl_li", "state": "home", "attributes": {"editable": true, "id": "karl_li", "device_trackers": ["device_tracker.karls_iphone_16_pro", "device_tracker.ipad_mini"], "latitude": 25.127997676562536, "longitude": 121.79795142624778, "gps_accuracy": 19, "source": "device_tracker.ipad_mini", "user_id": "9b1cbb01e5804f8d8d27eeb071638aab", "friendly_name": "<PERSON>"}, "last_changed": "2025-08-26T17:29:47.681794+00:00", "last_reported": "2025-08-26T17:29:58.332451+00:00", "last_updated": "2025-08-26T17:29:58.332451+00:00", "context": {"id": "01K3KPVVSWH52CX984DKX943SQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "person.phibie", "state": "home", "attributes": {"editable": true, "id": "phi<PERSON>", "device_trackers": ["device_tracker.phibz_iphone15"], "latitude": 25.128126816192463, "longitude": 121.79735966073359, "gps_accuracy": 20, "source": "device_tracker.phibz_iphone15", "user_id": "c752771c5ce34b52b02cb076cd9db031", "friendly_name": "<PERSON><PERSON>"}, "last_changed": "2025-08-26T17:29:47.681878+00:00", "last_reported": "2025-08-26T17:29:58.332592+00:00", "last_updated": "2025-08-26T17:29:58.332592+00:00", "context": {"id": "01K3KPVVSWSKN746JVZHFG4APF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "timer.garage_light_off_delay", "state": "idle", "attributes": {"duration": "0:02:00", "editable": false, "icon": "mdi:timer-outline", "friendly_name": "車庫燈倒數"}, "last_changed": "2025-08-26T17:29:47.681959+00:00", "last_reported": "2025-08-26T17:29:47.681959+00:00", "last_updated": "2025-08-26T17:29:47.681959+00:00", "context": {"id": "01K3KPVHD195Z9AD9VCZ0A1Z47", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "input_boolean.mailbox", "state": "on", "attributes": {"editable": true, "icon": "mdi:mailbox", "friendly_name": "Mailbox"}, "last_changed": "2025-08-26T17:29:47.682015+00:00", "last_reported": "2025-08-26T17:29:47.682015+00:00", "last_updated": "2025-08-26T17:29:47.682015+00:00", "context": {"id": "01K3KPVHD2KRBCMZ8S95J0BYNZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "binary_sensor.karls_iphone_16_pro_focus", "state": "off", "attributes": {"icon": "mdi:moon-waning-crescent", "friendly_name": "<PERSON>’s iPhone 16 Pro Focus"}, "last_changed": "2025-08-26T17:29:50.153401+00:00", "last_reported": "2025-08-26T17:29:50.153401+00:00", "last_updated": "2025-08-26T17:29:50.153401+00:00", "context": {"id": "01K3KPVKT9AY7YQY16Q1GT9RG9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "device_tracker.karls_iphone_16_pro", "state": "home", "attributes": {"source_type": "gps", "battery_level": 85, "latitude": 25.12804601953881, "longitude": 121.79791384700641, "gps_accuracy": 20, "altitude": 54.88430354547725, "vertical_accuracy": 30, "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro"}, "last_changed": "2025-08-26T17:29:50.153616+00:00", "last_reported": "2025-08-26T17:29:50.153616+00:00", "last_updated": "2025-08-26T17:29:50.153616+00:00", "context": {"id": "01K3KPVKT9J131WDMZMNZGXD6A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_activity", "state": "Unknown", "attributes": {"Confidence": "High", "Types": ["Unknown"], "icon": "mdi:help-circle", "friendly_name": "Karl’s iPhone 16 Pro Activity"}, "last_changed": "2025-08-26T17:29:50.153874+00:00", "last_reported": "2025-08-26T17:29:50.153874+00:00", "last_updated": "2025-08-26T17:29:50.153874+00:00", "context": {"id": "01K3KPVKT9JSZC0Q36YK729D4H", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Unknown", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_floors_ascended", "state": "10", "attributes": {"unit_of_measurement": "floors", "icon": "mdi:stairs-up", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Floors Ascended"}, "last_changed": "2025-08-26T17:29:50.153938+00:00", "last_reported": "2025-08-26T17:29:50.153938+00:00", "last_updated": "2025-08-26T17:29:50.153938+00:00", "context": {"id": "01K3KPVKT94AVSS09WXETATMWF", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 10, "native_unit_of_measurement": "floors"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_distance", "state": "4646", "attributes": {"unit_of_measurement": "m", "icon": "mdi:hiking", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Distance"}, "last_changed": "2025-08-26T17:29:50.153996+00:00", "last_reported": "2025-08-26T17:29:50.153996+00:00", "last_updated": "2025-08-26T17:29:50.153996+00:00", "context": {"id": "01K3KPVKT9RCTETT396PF7BY7S", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 4646, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_floors_descended", "state": "10", "attributes": {"unit_of_measurement": "floors", "icon": "mdi:stairs-down", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Floors Descended"}, "last_changed": "2025-08-26T17:29:50.154050+00:00", "last_reported": "2025-08-26T17:29:50.154050+00:00", "last_updated": "2025-08-26T17:29:50.154050+00:00", "context": {"id": "01K3KPVKTARMJT0745RKDZG8DZ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 10, "native_unit_of_measurement": "floors"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_battery_level", "state": "85", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery-80", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Battery Level"}, "last_changed": "2025-08-26T17:29:50.154096+00:00", "last_reported": "2025-08-26T17:29:50.154096+00:00", "last_updated": "2025-08-26T17:29:50.154096+00:00", "context": {"id": "01K3KPVKTAVWFTMDBDWD8BTJ7M", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 85, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_steps", "state": "7508", "attributes": {"unit_of_measurement": "steps", "icon": "mdi:walk", "friendly_name": "<PERSON>’s iPhone 16 Pro Steps"}, "last_changed": "2025-08-26T17:29:50.154155+00:00", "last_reported": "2025-08-26T17:29:50.154155+00:00", "last_updated": "2025-08-26T17:29:50.154155+00:00", "context": {"id": "01K3KPVKTADBGC4WN36GENCCME", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 7508, "native_unit_of_measurement": "steps"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_average_active_pace", "state": "1", "attributes": {"unit_of_measurement": "m/s", "icon": "mdi:speedometer", "friendly_name": "<PERSON>’s iPhone 16 Pro Average Active Pace"}, "last_changed": "2025-08-26T17:29:50.154202+00:00", "last_reported": "2025-08-26T17:29:50.154202+00:00", "last_updated": "2025-08-26T17:29:50.154202+00:00", "context": {"id": "01K3KPVKTAAGJ9D4PYSR72J714", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 1, "native_unit_of_measurement": "m/s"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_battery_state", "state": "Not Charging", "attributes": {"Low Power Mode": false, "icon": "mdi:battery-80", "friendly_name": "<PERSON>’s iPhone 16 Pro Battery State"}, "last_changed": "2025-08-26T17:29:50.154240+00:00", "last_reported": "2025-08-26T17:29:50.154240+00:00", "last_updated": "2025-08-26T17:29:50.154240+00:00", "context": {"id": "01K3KPVKTA62YG7BDKPGJA8G3G", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Charging", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_storage", "state": "7.94", "attributes": {"Available": "11.26 GB", "Available (Important)": "50.94 GB", "Available (Opportunistic)": "10.12 GB", "Total": "127.42 GB", "unit_of_measurement": "% available", "icon": "mdi:database", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Storage"}, "last_changed": "2025-08-26T17:29:50.154302+00:00", "last_reported": "2025-08-26T17:29:50.154302+00:00", "last_updated": "2025-08-26T17:29:50.154302+00:00", "context": {"id": "01K3KPVKTA71BS2SR99W3PSEQ1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "7.94", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_ssid", "state": "PKDKLF", "attributes": {"icon": "mdi:wifi", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro SSID"}, "last_changed": "2025-08-26T17:29:50.154342+00:00", "last_reported": "2025-08-26T17:29:50.154342+00:00", "last_updated": "2025-08-26T17:29:50.154342+00:00", "context": {"id": "01K3KPVKTAFHCVGGKW50PASNCS", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "PKDKLF", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_bssid", "state": "c:ea:14:25:5e:c3", "attributes": {"icon": "mdi:wifi-star", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro BSSID"}, "last_changed": "2025-08-26T17:29:50.154377+00:00", "last_reported": "2025-08-26T17:29:50.154377+00:00", "last_updated": "2025-08-26T17:29:50.154377+00:00", "context": {"id": "01K3KPVKTAPNXAVR55NTFVWPPE", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "c:ea:14:25:5e:c3", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_connection_type", "state": "Wi-Fi", "attributes": {"icon": "mdi:wifi", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Connection Type"}, "last_changed": "2025-08-26T17:29:50.154418+00:00", "last_reported": "2025-08-26T17:29:50.154418+00:00", "last_updated": "2025-08-26T17:29:50.154418+00:00", "context": {"id": "01K3KPVKTAAERV208Y4TZAQTZT", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Wi-Fi", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_sim_1", "state": "--", "attributes": {"Allows VoIP": true, "Carrier ID": "0000000100000001", "Carrier Name": "--", "Current Radio Technology": "Long-Term Evolution (LTE)", "ISO Country Code": "--", "Mobile Country Code": "65535", "Mobile Network Code": "65535", "icon": "mdi:sim", "friendly_name": "<PERSON>’s iPhone 16 Pro SIM 1"}, "last_changed": "2025-08-26T17:29:50.154458+00:00", "last_reported": "2025-08-26T17:29:50.154458+00:00", "last_updated": "2025-08-26T17:29:50.154458+00:00", "context": {"id": "01K3KPVKTANMK54Q6CAS3GR4QX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "--", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_sim_2", "state": "--", "attributes": {"Allows VoIP": true, "Carrier ID": "0000000100000002", "Carrier Name": "--", "ISO Country Code": "--", "Mobile Country Code": "65535", "Mobile Network Code": "65535", "icon": "mdi:sim", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro SIM 2"}, "last_changed": "2025-08-26T17:29:50.154497+00:00", "last_reported": "2025-08-26T17:29:50.154497+00:00", "last_updated": "2025-08-26T17:29:50.154497+00:00", "context": {"id": "01K3KPVKTAM0RD3D7AH600R72Q", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "--", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_geocoded_location", "state": "台灣\n202012 基隆市 中正區\n調和街266巷23弄1–3號", "attributes": {"Administrative Area": "基隆市", "Areas Of Interest": ["臺灣島"], "Country": "台灣", "Inland Water": "N/A", "ISO Country Code": "TW", "Locality": "中正區", "Location": [25.12804601953881, 121.79791384700641], "Name": "調和街266巷23弄1–3號", "Ocean": "N/A", "Postal Code": "202012", "Sub Administrative Area": "基隆市", "Sub Locality": "砂子里", "Sub Thoroughfare": "1–3", "Thoroughfare": "調和街266巷23弄", "Time Zone": "Asia/Taipei", "Zones": ["PKDKLF"], "icon": "mdi:map", "friendly_name": "<PERSON>’s iPhone 16 Pro Geocoded Location"}, "last_changed": "2025-08-26T17:29:50.154538+00:00", "last_reported": "2025-08-26T17:29:50.154538+00:00", "last_updated": "2025-08-26T17:29:50.154538+00:00", "context": {"id": "01K3KPVKTA38PACDFEHTKV9T24", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "台灣\n202012 基隆市 中正區\n調和街266巷23弄1–3號", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_last_update_trigger", "state": "Significant Location Change", "attributes": {"icon": "mdi:cellphone-wireless", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Last Update Trigger"}, "last_changed": "2025-08-26T17:29:50.154583+00:00", "last_reported": "2025-08-26T17:29:50.154583+00:00", "last_updated": "2025-08-26T17:29:50.154583+00:00", "context": {"id": "01K3KPVKTA8W6GECT4HMS884ZF", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Significant Location Change", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro App Version"}, "last_changed": "2025-08-26T17:29:50.154615+00:00", "last_reported": "2025-08-26T17:29:50.154615+00:00", "last_updated": "2025-08-26T17:29:50.154615+00:00", "context": {"id": "01K3KPVKTA8C234GRYEJS63KYA", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "<PERSON>’s iPhone 16 Pro Location permission"}, "last_changed": "2025-08-26T17:29:50.154644+00:00", "last_reported": "2025-08-26T17:29:50.154644+00:00", "last_updated": "2025-08-26T17:29:50.154644+00:00", "context": {"id": "01K3KPVKTAMTPM180VF4F8DBNE", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.karls_iphone_16_pro_audio_output", "state": "Built-in Speaker", "attributes": {"icon": "mdi:volume-high", "friendly_name": "<PERSON><PERSON>s iPhone 16 Pro Audio Output"}, "last_changed": "2025-08-26T17:29:50.154675+00:00", "last_reported": "2025-08-26T17:29:50.154675+00:00", "last_updated": "2025-08-26T17:29:50.154675+00:00", "context": {"id": "01K3KPVKTAZCFTEAXY2WDEYSR3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Built-in Speaker", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "device_tracker.phibz_iphone15", "state": "home", "attributes": {"source_type": "gps", "battery_level": 85, "latitude": 25.128126816192463, "longitude": 121.79735966073359, "gps_accuracy": 20, "altitude": 59.22300359895385, "vertical_accuracy": 30, "friendly_name": "Phibz iPhone15"}, "last_changed": "2025-08-26T17:29:50.154920+00:00", "last_reported": "2025-08-26T17:29:50.154920+00:00", "last_updated": "2025-08-26T17:29:50.154920+00:00", "context": {"id": "01K3KPVKTA430MCC432ZWF69QX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "Phibz iPhone15 App Version"}, "last_changed": "2025-08-26T17:29:50.155103+00:00", "last_reported": "2025-08-26T17:29:50.155103+00:00", "last_updated": "2025-08-26T17:29:50.155103+00:00", "context": {"id": "01K3KPVKTBH1T86W8VSAE6EHAG", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_ssid", "state": "Not Connected", "attributes": {"icon": "mdi:wifi-off", "friendly_name": "Phibz iPhone15 SSID"}, "last_changed": "2025-08-26T17:29:50.155144+00:00", "last_reported": "2025-08-26T17:29:50.155144+00:00", "last_updated": "2025-08-26T17:29:50.155144+00:00", "context": {"id": "01K3KPVKTBMBJZA5Z8AQJP159K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Connected", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_bssid", "state": "Not Connected", "attributes": {"icon": "mdi:wifi-off", "friendly_name": "Phibz iPhone15 BSSID"}, "last_changed": "2025-08-26T17:29:50.155201+00:00", "last_reported": "2025-08-26T17:29:50.155201+00:00", "last_updated": "2025-08-26T17:29:50.155201+00:00", "context": {"id": "01K3KPVKTB614BFXWB9R4XV0N3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Connected", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_battery_level", "state": "85", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery-80", "friendly_name": "Phibz iPhone15 Battery Level"}, "last_changed": "2025-08-26T17:29:50.155259+00:00", "last_reported": "2025-08-26T17:29:50.155259+00:00", "last_updated": "2025-08-26T17:29:50.155259+00:00", "context": {"id": "01K3KPVKTBQT78X3M51Z90BR6Z", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 85, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "Phibz iPhone15 Location permission"}, "last_changed": "2025-08-26T17:29:50.155292+00:00", "last_reported": "2025-08-26T17:29:50.155292+00:00", "last_updated": "2025-08-26T17:29:50.155292+00:00", "context": {"id": "01K3KPVKTBR2H7HBJNYK0FVFMR", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_audio_output", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Audio Output"}, "last_changed": "2025-08-26T17:29:50.155322+00:00", "last_reported": "2025-08-26T17:29:50.155322+00:00", "last_updated": "2025-08-26T17:29:50.155322+00:00", "context": {"id": "01K3KPVKTB50WKHHESEAQ6YNW7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_sim_2", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 SIM 2"}, "last_changed": "2025-08-26T17:29:50.155356+00:00", "last_reported": "2025-08-26T17:29:50.155356+00:00", "last_updated": "2025-08-26T17:29:50.155356+00:00", "context": {"id": "01K3KPVKTBRQZCTGGNQ64F0V7F", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_sim_1", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 SIM 1"}, "last_changed": "2025-08-26T17:29:50.155400+00:00", "last_reported": "2025-08-26T17:29:50.155400+00:00", "last_updated": "2025-08-26T17:29:50.155400+00:00", "context": {"id": "01K3KPVKTB8BGFJM42XVD8WXC4", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_storage", "state": "unavailable", "attributes": {"unit_of_measurement": "% available", "icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Storage"}, "last_changed": "2025-08-26T17:29:50.155437+00:00", "last_reported": "2025-08-26T17:29:50.155437+00:00", "last_updated": "2025-08-26T17:29:50.155437+00:00", "context": {"id": "01K3KPVKTB57GSPR3WV9Q6VY5V", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_geocoded_location", "state": "Taiwan\n202012 Keelung City Zhongzheng District\nNo. 12 調和街266巷57弄", "attributes": {"Administrative Area": "Keelung City", "Areas Of Interest": ["Taiwan"], "Country": "Taiwan", "Inland Water": "N/A", "ISO Country Code": "TW", "Locality": "Zhongzheng District", "Location": [25.128126816192463, 121.79735966073359], "Name": "No. 12 調和街266巷57弄", "Ocean": "N/A", "Postal Code": "202012", "Sub Administrative Area": "Keelung City", "Sub Locality": "<PERSON><PERSON><PERSON>", "Sub Thoroughfare": "No. 12", "Thoroughfare": "調和街266巷57弄", "Time Zone": "Asia/Taipei", "Zones": ["PKDKLF"], "icon": "mdi:map", "friendly_name": "Phibz iPhone15 Geocoded Location"}, "last_changed": "2025-08-26T17:29:50.155468+00:00", "last_reported": "2025-08-26T17:29:50.155468+00:00", "last_updated": "2025-08-26T17:29:50.155468+00:00", "context": {"id": "01K3KPVKTBPQ7VNNDS3QC5SD3N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Taiwan\n202012 Keelung City Zhongzheng District\nNo. 12 調和街266巷57弄", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_connection_type", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Connection Type"}, "last_changed": "2025-08-26T17:29:50.155509+00:00", "last_reported": "2025-08-26T17:29:50.155509+00:00", "last_updated": "2025-08-26T17:29:50.155509+00:00", "context": {"id": "01K3KPVKTBBSPP54VA8N1969MF", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_battery_state", "state": "Not Charging", "attributes": {"Low Power Mode": false, "icon": "mdi:battery-80", "friendly_name": "Phibz iPhone15 Battery State"}, "last_changed": "2025-08-26T17:29:50.155545+00:00", "last_reported": "2025-08-26T17:29:50.155545+00:00", "last_updated": "2025-08-26T17:29:50.155545+00:00", "context": {"id": "01K3KPVKTBGS0PFB79SHBQBBH7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Charging", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.phibz_iphone15_last_update_trigger", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "Phibz iPhone15 Last Update Trigger"}, "last_changed": "2025-08-26T17:29:50.155581+00:00", "last_reported": "2025-08-26T17:29:50.155581+00:00", "last_updated": "2025-08-26T17:29:50.155581+00:00", "context": {"id": "01K3KPVKTBVYN1VSCCMF69CQM7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "binary_sensor.ipad_mini_focus", "state": "off", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Focus"}, "last_changed": "2025-08-26T17:29:50.155734+00:00", "last_reported": "2025-08-26T17:29:50.155734+00:00", "last_updated": "2025-08-26T17:29:50.155734+00:00", "context": {"id": "01K3KPVKTB9H24XK1DTAEX8WMD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "device_tracker.ipad_mini", "state": "home", "attributes": {"source_type": "gps", "battery_level": 100, "latitude": 25.127997676562536, "longitude": 121.79795142624778, "gps_accuracy": 19, "altitude": 55.87594032287598, "vertical_accuracy": 30, "friendly_name": "iPad mini"}, "last_changed": "2025-08-26T17:29:50.155836+00:00", "last_reported": "2025-08-26T17:29:50.155836+00:00", "last_updated": "2025-08-26T17:29:50.155836+00:00", "context": {"id": "01K3KPVKTB71QSTBYPHHDQ0ZP3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_activity", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Activity"}, "last_changed": "2025-08-26T17:29:50.155977+00:00", "last_reported": "2025-08-26T17:29:50.155977+00:00", "last_updated": "2025-08-26T17:29:50.155977+00:00", "context": {"id": "01K3KPVKTBEDMJJTW7AHF0RTN4", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_ssid", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini SSID"}, "last_changed": "2025-08-26T17:29:50.156026+00:00", "last_reported": "2025-08-26T17:29:50.156026+00:00", "last_updated": "2025-08-26T17:29:50.156026+00:00", "context": {"id": "01K3KPVKTCB6HEYVZ513Y3S11E", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_bssid", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini BSSID"}, "last_changed": "2025-08-26T17:29:50.156058+00:00", "last_reported": "2025-08-26T17:29:50.156058+00:00", "last_updated": "2025-08-26T17:29:50.156058+00:00", "context": {"id": "01K3KPVKTCJFGAQ0A8B0X776CJ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_connection_type", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Connection Type"}, "last_changed": "2025-08-26T17:29:50.156089+00:00", "last_reported": "2025-08-26T17:29:50.156089+00:00", "last_updated": "2025-08-26T17:29:50.156089+00:00", "context": {"id": "01K3KPVKTCZ77B7ENQ4785CPQW", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_storage", "state": "unavailable", "attributes": {"unit_of_measurement": "% available", "icon": "mdi:dots-square", "friendly_name": "iPad mini Storage"}, "last_changed": "2025-08-26T17:29:50.156132+00:00", "last_reported": "2025-08-26T17:29:50.156132+00:00", "last_updated": "2025-08-26T17:29:50.156132+00:00", "context": {"id": "01K3KPVKTCWNFNMD2ZJC4CY5Q1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": "% available"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_battery_level", "state": "100", "attributes": {"unit_of_measurement": "%", "device_class": "battery", "icon": "mdi:battery", "friendly_name": "iPad mini Battery Level"}, "last_changed": "2025-08-26T17:29:50.156182+00:00", "last_reported": "2025-08-26T17:29:50.156182+00:00", "last_updated": "2025-08-26T17:29:50.156182+00:00", "context": {"id": "01K3KPVKTCCJ4Z116DNE5DYR7K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": 100, "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_battery_state", "state": "Not Charging", "attributes": {"Low Power Mode": false, "icon": "mdi:battery", "friendly_name": "iPad mini Battery State"}, "last_changed": "2025-08-26T17:29:50.156221+00:00", "last_reported": "2025-08-26T17:29:50.156221+00:00", "last_updated": "2025-08-26T17:29:50.156221+00:00", "context": {"id": "01K3KPVKTC6CE2Y22WYTV94BX2", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Not Charging", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_geocoded_location", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Geocoded Location"}, "last_changed": "2025-08-26T17:29:50.156260+00:00", "last_reported": "2025-08-26T17:29:50.156260+00:00", "last_updated": "2025-08-26T17:29:50.156260+00:00", "context": {"id": "01K3KPVKTCYYX2HREW2VN023XD", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_last_update_trigger", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Last Update Trigger"}, "last_changed": "2025-08-26T17:29:50.156287+00:00", "last_reported": "2025-08-26T17:29:50.156287+00:00", "last_updated": "2025-08-26T17:29:50.156287+00:00", "context": {"id": "01K3KPVKTCGKT8C675M2W0MT7J", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_app_version", "state": "2025.7.2", "attributes": {"icon": "mdi:cellphone", "friendly_name": "iPad mini App Version"}, "last_changed": "2025-08-26T17:29:50.156329+00:00", "last_reported": "2025-08-26T17:29:50.156329+00:00", "last_updated": "2025-08-26T17:29:50.156329+00:00", "context": {"id": "01K3KPVKTC3YX446W9VWFV448N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2025.7.2", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_audio_output", "state": "unavailable", "attributes": {"icon": "mdi:dots-square", "friendly_name": "iPad mini Audio Output"}, "last_changed": "2025-08-26T17:29:50.156364+00:00", "last_reported": "2025-08-26T17:29:50.156364+00:00", "last_updated": "2025-08-26T17:29:50.156364+00:00", "context": {"id": "01K3KPVKTC9H88QCHJWSHX49P1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "unavailable", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.ipad_mini_location_permission", "state": "Authorized Always", "attributes": {"icon": "mdi:map", "friendly_name": "iPad mini Location permission"}, "last_changed": "2025-08-26T17:29:50.156395+00:00", "last_reported": "2025-08-26T17:29:50.156395+00:00", "last_updated": "2025-08-26T17:29:50.156395+00:00", "context": {"id": "01K3KPVKTC29V2JVE9D4PS9B78", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "Authorized Always", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_sleep_mode_qk_cblc5", "state": "off", "attributes": {"icon": "mdi:sleep", "friendly_name": "Adaptive Lighting Sleep Mode: QK-CBLC5"}, "last_changed": "2025-08-26T17:29:50.297602+00:00", "last_reported": "2025-08-26T17:29:50.297602+00:00", "last_updated": "2025-08-26T17:29:50.297602+00:00", "context": {"id": "01K3KPVKYSQKG32Q5Y0HMY589T", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_adapt_color_qk_cblc5", "state": "on", "attributes": {"icon": "mdi:sun-thermometer", "friendly_name": "Adaptive Lighting Adapt Color: QK-CBLC5"}, "last_changed": "2025-08-26T17:29:50.297662+00:00", "last_reported": "2025-08-26T17:29:50.297662+00:00", "last_updated": "2025-08-26T17:29:50.297662+00:00", "context": {"id": "01K3KPVKYSAG0NYCR0KFN300F3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_adapt_brightness_qk_cblc5", "state": "on", "attributes": {"icon": "mdi:brightness-4", "friendly_name": "Adaptive Lighting Adapt Brightness: QK-CBLC5"}, "last_changed": "2025-08-26T17:29:50.297696+00:00", "last_reported": "2025-08-26T17:29:50.297696+00:00", "last_updated": "2025-08-26T17:29:50.297696+00:00", "context": {"id": "01K3KPVKYSYHTPD8NKA3SYY9FX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "switch.adaptive_lighting_qk_cblc5", "state": "on", "attributes": {"configuration": {}, "manual_control": [], "brightness_pct": 26.393693757623517, "color_temp_kelvin": 2000, "color_temp_mired": 500, "rgb_color": [255, 136.86832541739756, 13.904070298965905], "xy_color": [0.598, 0.383], "hs_color": [30.504, 93.333], "sun_position": -0.8659565440279586, "force_rgb_color": false, "autoreset_time_remaining": {}, "icon": "mdi:theme-light-dark", "friendly_name": "Adaptive Lighting: QK-CBLC5"}, "last_changed": "2025-08-26T17:29:50.298065+00:00", "last_reported": "2025-08-26T17:57:10.769091+00:00", "last_updated": "2025-08-26T17:57:09.335036+00:00", "context": {"id": "01K3KRDMJQ5TCHPHDWY3BSQJQ5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.webrtc_camera_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.6.1", "in_progress": false, "latest_version": "v3.6.1", "release_summary": null, "release_url": "https://github.com/AlexxIT/WebRTC/releases/v3.6.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/webrtc/icon.png", "friendly_name": "WebRTC Camera update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.308800+00:00", "last_reported": "2025-08-26T17:29:50.308800+00:00", "last_updated": "2025-08-26T17:29:50.308800+00:00", "context": {"id": "01K3KPVKZ4DKG9R0KMZMHWQS33", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.aqara_gateway_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "0.3.2", "in_progress": false, "latest_version": "0.3.2", "release_summary": null, "release_url": "https://github.com/niceboygithub/AqaraGateway/releases/0.3.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/aqara_gateway/icon.png", "friendly_name": "Aqara Gateway update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.308866+00:00", "last_reported": "2025-08-26T17:29:50.308866+00:00", "last_updated": "2025-08-26T17:29:50.308866+00:00", "context": {"id": "01K3KPVKZ41SWSQYNQZH7G0JXK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.tapo_controller_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "3.2.1", "in_progress": false, "latest_version": "3.2.1", "release_summary": null, "release_url": "https://github.com/petretiandrea/home-assistant-tapo-p100/releases/3.2.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo/icon.png", "friendly_name": "Tapo Controller update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.308930+00:00", "last_reported": "2025-08-26T17:29:50.308930+00:00", "last_updated": "2025-08-26T17:29:50.308930+00:00", "context": {"id": "01K3KPVKZ4VN2M3SBBWFB71KGK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.sonos_cloud_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "0.3.5", "in_progress": false, "latest_version": "0.3.5", "release_summary": null, "release_url": "https://github.com/jjlawren/sonos_cloud/releases/0.3.5", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/sonos_cloud/icon.png", "friendly_name": "Sonos Cloud update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.308976+00:00", "last_reported": "2025-08-26T17:29:50.308976+00:00", "last_updated": "2025-08-26T17:29:50.308976+00:00", "context": {"id": "01K3KPVKZ4ZEMHRRTKACW0RPNC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.bambu_lab_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v2.1.27", "in_progress": false, "latest_version": "v2.1.27", "release_summary": null, "release_url": "https://github.com/greghesp/ha-bambulab/releases/v2.1.27", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/bambu_lab/icon.png", "friendly_name": "Bambu Lab update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309024+00:00", "last_reported": "2025-08-26T17:29:50.309024+00:00", "last_updated": "2025-08-26T17:29:50.309024+00:00", "context": {"id": "01K3KPVKZ50AGB754FGPEG94FE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.mushroom_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v4.5.0", "in_progress": false, "latest_version": "v4.5.0", "release_summary": null, "release_url": "https://github.com/piitaya/lovelace-mushroom/releases/v4.5.0", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Mushroom update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309063+00:00", "last_reported": "2025-08-26T17:29:50.309063+00:00", "last_updated": "2025-08-26T17:29:50.309063+00:00", "context": {"id": "01K3KPVKZ5ZPPY3GB0W0VWA79K", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.tapo_cameras_control_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "7.0.0", "in_progress": false, "latest_version": "7.0.0", "release_summary": null, "release_url": "https://github.com/JurajNyiri/HomeAssistant-Tapo-Control/releases/7.0.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo: Cameras Control update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309096+00:00", "last_reported": "2025-08-26T17:29:50.309096+00:00", "last_updated": "2025-08-26T17:29:50.309096+00:00", "context": {"id": "01K3KPVKZ598014DQ0T2MKR655", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.panasonic_smart_app_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v2.12.0", "in_progress": false, "latest_version": "v2.12.0", "release_summary": null, "release_url": "https://github.com/osk2/panasonic_smart_app/releases/v2.12.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/panasonic_smart_app/icon.png", "friendly_name": "Panasonic Smart App update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309127+00:00", "last_reported": "2025-08-26T17:29:50.309127+00:00", "last_updated": "2025-08-26T17:29:50.309127+00:00", "context": {"id": "01K3KPVKZ55VVPNEDVFZMD1C9Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.xiaomi_gateway_3_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v4.1.2", "in_progress": false, "latest_version": "v4.1.2", "release_summary": null, "release_url": "https://github.com/AlexxIT/XiaomiGateway3/releases/v4.1.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/xiaomi_gateway3/icon.png", "friendly_name": "Xiaomi Gateway 3 update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309163+00:00", "last_reported": "2025-08-26T17:29:50.309163+00:00", "last_updated": "2025-08-26T17:29:50.309163+00:00", "context": {"id": "01K3KPVKZ5DFM0X2W5S8PY8PMS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.local_tuya_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v5.2.4", "in_progress": false, "latest_version": "v5.2.4", "release_summary": null, "release_url": "https://github.com/rospogrigio/localtuya/releases/v5.2.4", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/localtuya/icon.png", "friendly_name": "Local Tuya update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309195+00:00", "last_reported": "2025-08-26T17:29:50.309195+00:00", "last_updated": "2025-08-26T17:29:50.309195+00:00", "context": {"id": "01K3KPVKZ50VR8EAW1H85WPSQJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.hacs_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "2.0.5", "in_progress": false, "latest_version": "2.0.5", "release_summary": null, "release_url": "https://github.com/hacs/integration/releases/2.0.5", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/hacs/icon.png", "friendly_name": "HACS update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309242+00:00", "last_reported": "2025-08-26T17:29:50.309242+00:00", "last_updated": "2025-08-26T17:29:50.309242+00:00", "context": {"id": "01K3KPVKZ5MNYGW48WQPATGHG3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.tuya_local_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "2025.7.1", "in_progress": false, "latest_version": "2025.7.1", "release_summary": null, "release_url": "https://github.com/make-all/tuya-local/releases/2025.7.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tuya_local/icon.png", "friendly_name": "Tuya Local update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309295+00:00", "last_reported": "2025-08-26T17:29:50.309295+00:00", "last_updated": "2025-08-26T17:29:50.309295+00:00", "context": {"id": "01K3KPVKZ5H1KWAPQSK7T1MRT3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.kiosk_mode_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v7.2.0", "in_progress": false, "latest_version": "v7.2.0", "release_summary": null, "release_url": "https://github.com/NemesisRE/kiosk-mode/releases/v7.2.0", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Kiosk Mode update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309346+00:00", "last_reported": "2025-08-26T17:29:50.309346+00:00", "last_updated": "2025-08-26T17:29:50.309346+00:00", "context": {"id": "01K3KPVKZ5MZSH9DRJX1SX95BA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.smartthinq_lge_sensors_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v0.41.1", "in_progress": false, "latest_version": "v0.41.1", "release_summary": null, "release_url": "https://github.com/ollo69/ha-smartthinq-sensors/releases/v0.41.1", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/smartthinq_sensors/icon.png", "friendly_name": "SmartThinQ LGE Sensors update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309379+00:00", "last_reported": "2025-08-26T17:29:50.309379+00:00", "last_updated": "2025-08-26T17:29:50.309379+00:00", "context": {"id": "01K3KPVKZ50AYMKZVQZHHXBQ1W", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.electrolux_wellbeing_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.3.9", "in_progress": false, "latest_version": "v1.3.9", "release_summary": null, "release_url": "https://github.com/JohNan/homeassistant-wellbeing/releases/v1.3.9", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/wellbeing/icon.png", "friendly_name": "Electrolux Wellbeing update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309410+00:00", "last_reported": "2025-08-26T17:29:50.309410+00:00", "last_updated": "2025-08-26T17:29:50.309410+00:00", "context": {"id": "01K3KPVKZ5HS0AQJ8CXGBR0PGD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.dyson_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.7.0", "in_progress": false, "latest_version": "v1.7.0", "release_summary": null, "release_url": "https://github.com/libdyson-wg/ha-dyson/releases/v1.7.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/dyson_local/icon.png", "friendly_name": "Dyson update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309438+00:00", "last_reported": "2025-08-26T17:29:50.309438+00:00", "last_updated": "2025-08-26T17:29:50.309438+00:00", "context": {"id": "01K3KPVKZ5WHVJZYTR95C8XE2A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.tesla_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.25.2", "in_progress": false, "latest_version": "v3.25.2", "release_summary": null, "release_url": "https://github.com/alandtse/tesla/releases/v3.25.2", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tesla_custom/icon.png", "friendly_name": "Tesla update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309468+00:00", "last_reported": "2025-08-26T17:29:50.309468+00:00", "last_updated": "2025-08-26T17:29:50.309468+00:00", "context": {"id": "01K3KPVKZ5YES0ZGJE8ATQB131", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.adaptive_lighting_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v1.26.0", "in_progress": false, "latest_version": "v1.26.0", "release_summary": null, "release_url": "https://github.com/basnijholt/adaptive-lighting/releases/v1.26.0", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/adaptive_lighting/icon.png", "friendly_name": "Adaptive Lighting update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309497+00:00", "last_reported": "2025-08-26T17:29:50.309497+00:00", "last_updated": "2025-08-26T17:29:50.309497+00:00", "context": {"id": "01K3KPVKZ5WZCSJ0QVFCYF9WM0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.samsungtv_smart_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v0.14.4", "in_progress": false, "latest_version": "v0.14.4", "release_summary": null, "release_url": "https://github.com/ollo69/ha-samsungtv-smart/releases/v0.14.4", "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/samsungtv_smart/icon.png", "friendly_name": "SamsungTV Smart update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309525+00:00", "last_reported": "2025-08-26T17:29:50.309525+00:00", "last_updated": "2025-08-26T17:29:50.309525+00:00", "context": {"id": "01K3KPVKZ5PXZMDAVYJ3BC84B8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.bubble_card_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "v3.0.3", "in_progress": false, "latest_version": "v3.0.3", "release_summary": null, "release_url": "https://github.com/Clooos/Bubble-Card/releases/v3.0.3", "skipped_version": null, "title": null, "update_percentage": null, "friendly_name": "Bubble Card update", "supported_features": 23}, "last_changed": "2025-08-26T17:29:50.309558+00:00", "last_reported": "2025-08-26T17:29:50.309558+00:00", "last_updated": "2025-08-26T17:29:50.309558+00:00", "context": {"id": "01K3KPVKZ5N8FV0GTSB42GZ4YC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_safe_mode_boot", "state": "unknown", "attributes": {"device_class": "restart", "icon": "mdi:restart-alert", "friendly_name": "Climate-H 91e0ec Safe Mode Boot"}, "last_changed": "2025-08-26T17:29:50.892537+00:00", "last_reported": "2025-08-26T17:29:50.892537+00:00", "last_updated": "2025-08-26T17:29:50.892537+00:00", "context": {"id": "01K3KPVMHCH5ZTW17WKKT0C182", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_restart", "state": "unknown", "attributes": {"device_class": "restart", "icon": "mdi:restart", "friendly_name": "Climate-H 91e0ec Restart"}, "last_changed": "2025-08-26T17:29:50.892489+00:00", "last_reported": "2025-08-26T17:29:50.892489+00:00", "last_updated": "2025-08-26T17:29:50.892489+00:00", "context": {"id": "01K3KPVMHCH9N939B3JYKY5S22", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.climate_h_91e0ec_get_info", "state": "unknown", "attributes": {"icon": "mdi:button-pointer", "friendly_name": "Climate-H 91e0ec Get Info"}, "last_changed": "2025-08-26T17:29:50.892526+00:00", "last_reported": "2025-08-26T17:29:50.892526+00:00", "last_updated": "2025-08-26T17:29:50.892526+00:00", "context": {"id": "01K3KPVMHC3V7VENN1D9ZNBJYH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "binary_sensor.mailbox", "state": "on", "attributes": {"icon": "mdi:mailbox", "friendly_name": "Mailbox"}, "last_changed": "2025-08-26T17:29:58.334204+00:00", "last_reported": "2025-08-26T17:29:58.334204+00:00", "last_updated": "2025-08-26T17:29:58.334204+00:00", "context": {"id": "01K3KPVVSY59RYRFXS9X7EE9SQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "sensor.bubble_card_modules", "state": "saved", "attributes": {"modules": {"home-assistant-default": {"id": "home-assistant-default", "name": "Home Assistant default styling", "version": "v1.3", "creator": "C<PERSON><PERSON>", "link": "https://github.com/Clooos/Bubble-Card/discussions/1230", "description": "This module applies Home Assistant's default styling to Bubble Card. To set it as the default, move it under <code>default:</code> in the <code>bubble-modules.yaml</code> file.", "code": ":host {\n    --bubble-button-accent-color: rgba(0,140,255,0.3); /* Edit this color if needed */\n    --bubble-main-background-color: var(--ha-card-background, var(--card-background-color, #fff));\n    --bubble-border-radius: var(--ha-card-border-radius, 12px);\n    --bubble-icon-border-radius: 32px;\n    --bubble-button-border-radius: var(--bubble-border-radius);\n    --bubble-climate-button-background-color: var(--bubble-icon-background-color);\n    --bubble-border: var(--ha-card-border-width, 1px) solid var(--ha-card-border-color, var(--divider-color, #e0e0e0));\n    --bubble-secondary-background-color: transparent;\n}\n\n.bubble-container {\n    -webkit-backdrop-filter: var(--ha-card-backdrop-filter, none);\n    backdrop-filter: var(--ha-card-backdrop-filter, none);\n    box-shadow: var(--ha-card-box-shadow, none);\n    box-sizing: border-box;\n}\n\n.bubble-icon-container, \n.large .bubble-icon-container {\n    --mdc-icon-size: 22px;\n    min-width: 36px !important;\n    min-height: 36px !important;\n}\n\n.large .bubble-cover-card-container > .bubble-buttons {\n    --bubble-cover-main-background-color: none;\n}\n\n.bubble-range-fill {\n    --bubble-accent-color: var(--bubble-button-accent-color);\n}\n\n.bubble-sub-button.background-on::before,\n.bubble-sub-button.background-off::before,\n.bubble-temperature-container::before,\n.bubble-icon-container::before {\n    content: \"\";\n    position: absolute;\n    top: 0; \n    left: 0;\n    width: 100%; \n    height: 100%;  \n    opacity: var(--control-number-buttons-background-opacity, .2);\n    border-radius: var(--bubble-border-radius);\n    background: var(--control-number-buttons-background-color, var(--disabled-color));\n}\n\n.is-on {\n    --bubble-icon-background-color: var(--view-background,var(--lovelace-background,var(--primary-background-color)));\n    transition: all ease-in 0.3s !important;\n}\n\n.bubble-icon-container::before {\n    background: var(--state-inactive-color);\n    border-radius: var(--bubble-icon-border-radius);\n}\n\n.bubble-sub-button {\n    border: 0px solid transparent !important;\n}\n\n.no-icon-select-arrow {\n    right: 4px !important;\n}\n\n.bubble-select.bubble-wrapper {\n    margin: 0 -2px;\n}\n\n.large .bubble-icon-container {\n     margin-left: 9px;\n}\n\n.bubble-state {\n    opacity: 1;\n    font-weight: 400;\n    font-size: 12px;\n    letter-spacing: .4px;\n}\n\n:not(.bubble-separator) > .bubble-name {\n    font-weight: 500;\n    font-size: 14px;\n    letter-spacing: 0.1px;\n}\n\n.bubble-pop-up-background { \n    filter: brightness(0.96); /* Improve pop-up background contrast */\n    --bubble-pop-up-border-radius: calc(var(--ha-card-border-radius, 12px) * 1.4);\n}\n\n.bubble-header-container {\n    --bubble-secondary-background-color: var(--background-color-2); \n}\n\nha-select {\n    --bubble-list-item-accent-color: none !important;\n    --mdc-theme-surface: var(--card-background-color);\n}\n\nmwc-list-item[selected] {\n    color: inherit !important;\n    --mdc-ripple-press-opacity: 0 !important;\n}\n\nmwc-list-item[selected]::before {\n    content: \"\";\n    position: absolute;\n    top: 0; \n    left: 0;\n    width: 100%; \n    height: 100%;  \n    background-color: var(--primary-color);\n    opacity: 0.24;\n}\n"}, "get_state_attribute": {"id": "get_state_attribute", "name": "Advanced example: Get state/attribute from other entities", "version": "v1.3", "creator": "C<PERSON><PERSON>", "link": "https://github.com/Clooos/Bubble-Card/discussions/1232", "supported": ["pop-up", "cover", "button", "media-player", "climate", "select"], "description": "Get state/attribute from other entities and replace the default state/attribute field.\n<img class=\"example\" src=\"https://github.com/Clooos/Bubble-Card/blob/main/img/get_state_template_example.png?raw=true\" />\nConfigure this module via the editor or in YAML, for example:\n<br><br>\n<code-block><pre>\nget_state_attribute:\n    - entity: weather.home\n    - entity: sensor.weather_station\n      attribute: humidity\n    - entity: sensor.weather_station\n      attribute: temperature\n</pre></code-block>\n<br>\n<b>If it doesn't work, make sure at least one of \"Show state\" or \"Show attribute\" is turned on in your card configuration.</b>\n", "code": "${(() => {\n  // Retrieve the configuration or use an empty array by default\n  const config = this.config.get_state_attribute || [];\n\n  // Format the retrieved value from the entity for each entry\n  const values = config\n    .map(cfg => {\n      const entity = hass.states[cfg.entity];\n      if (entity) {\n        let rawValue;\n        if (cfg.attribute) {\n          rawValue = entity.attributes[cfg.attribute];\n          if (rawValue !== undefined && rawValue !== 'unknown' && rawValue !== 'unavailable' && rawValue !== 'null' && rawValue !== '') {\n            return hass.formatEntityAttributeValue(entity, cfg.attribute);\n          }\n        } else {\n          rawValue = entity.state;\n          if (rawValue !== undefined && rawValue !== 'unknown' && rawValue !== 'unavailable' && rawValue !== 'null' && rawValue !== '') {\n            return hass.formatEntityState(entity);\n          }\n        }\n      }\n      return null;\n    })\n    // Remove null values and empty strings or strings with only spaces\n    .filter(value => value !== null && value !== \"\" && value.trim() !== \"\");\n\n  // Update the DOM element with the class 'bubble-state'\n  // displaying values separated by ' • '\n  card.querySelector('.bubble-state').innerText = values.join(' • ');\n})()}\n", "editor": [{"type": "expandable", "title": "Select entities and attributes", "icon": "mdi:list-box-outline", "schema": [{"name": "0", "type": "expandable", "title": "Entity 1", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "1", "type": "expandable", "title": "Entity 2", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "2", "type": "expandable", "title": "Entity 3", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}, {"name": "3", "type": "expandable", "title": "Entity 4", "schema": [{"name": "entity", "label": "Entity", "selector": {"entity": {}}}, {"name": "attribute", "label": "Attribute", "selector": {"attribute": {}}}]}]}]}}, "last_updated": "2025-04-29T15:17:07.819Z", "friendly_name": "Bubble Card Modules"}, "last_changed": "2025-08-26T17:29:50.453579+00:00", "last_reported": "2025-08-26T17:29:50.453579+00:00", "last_updated": "2025-08-26T17:29:50.453579+00:00", "context": {"id": "01K3KPVM3NKR52K564ARY4055R", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "saved", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_pause_printing", "state": "unavailable", "attributes": {"icon": "mdi:pause", "friendly_name": "A1MINI_0309CA4C1901915 Pause printing"}, "last_changed": "2025-08-26T17:29:50.624691+00:00", "last_reported": "2025-08-26T17:29:55.788900+00:00", "last_updated": "2025-08-26T17:29:50.624691+00:00", "context": {"id": "01K3KPVM90RJ739XMC0BQGM1ZY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_resume_printing", "state": "unavailable", "attributes": {"icon": "mdi:play", "friendly_name": "A1MINI_0309CA4C1901915 Resume printing"}, "last_changed": "2025-08-26T17:29:50.624742+00:00", "last_reported": "2025-08-26T17:29:55.789002+00:00", "last_updated": "2025-08-26T17:29:50.624742+00:00", "context": {"id": "01K3KPVM90DZQN6Z6FRYK81WW4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_stop_printing", "state": "unavailable", "attributes": {"icon": "mdi:stop", "friendly_name": "A1MINI_0309CA4C1901915 Stop printing"}, "last_changed": "2025-08-26T17:29:50.624790+00:00", "last_reported": "2025-08-26T17:29:55.789060+00:00", "last_updated": "2025-08-26T17:29:50.624790+00:00", "context": {"id": "01K3KPVM90YEKZ8SFASJK8VD6S", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.a1mini_0309ca4c1901915_force_refresh_data", "state": "unknown", "attributes": {"icon": "mdi:refresh", "friendly_name": "A1MINI_0309CA4C1901915 Force refresh data"}, "last_changed": "2025-08-26T17:29:50.624834+00:00", "last_reported": "2025-08-26T17:29:55.789096+00:00", "last_updated": "2025-08-26T17:29:50.624834+00:00", "context": {"id": "01K3KPVM90J854M9WBW3PFZW3A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.3f_zi_dong_lou_ti_deng", "state": "on", "attributes": {"id": "1740766955668", "last_triggered": "2025-08-26T15:31:52.191012+00:00", "mode": "restart", "current": 0, "friendly_name": "3F 自動樓梯燈"}, "last_changed": "2025-08-26T17:29:50.888386+00:00", "last_reported": "2025-08-26T17:29:50.888386+00:00", "last_updated": "2025-08-26T17:29:50.888386+00:00", "context": {"id": "01K3KPVMH8JWZ2XAM5GAJH9NFT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.ke_ting_kan_deng", "state": "on", "attributes": {"id": "1740984869662", "last_triggered": "2025-08-26T15:39:18.349566+00:00", "mode": "restart", "current": 0, "friendly_name": "客廳崁燈"}, "last_changed": "2025-08-26T17:29:50.888462+00:00", "last_reported": "2025-08-26T17:29:50.888462+00:00", "last_updated": "2025-08-26T17:29:50.888462+00:00", "context": {"id": "01K3KPVMH886HSF1T58V2YNFWW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.ke_ting_diao_deng", "state": "on", "attributes": {"id": "1740984964318", "last_triggered": "2025-08-25T16:41:30.518158+00:00", "mode": "restart", "current": 0, "friendly_name": "客廳吊燈"}, "last_changed": "2025-08-26T17:29:50.888507+00:00", "last_reported": "2025-08-26T17:29:50.888507+00:00", "last_updated": "2025-08-26T17:29:50.888507+00:00", "context": {"id": "01K3KPVMH8CDSK0JV0HWHA9DKC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.qing_kong_deng", "state": "on", "attributes": {"id": "1740987782030", "last_triggered": "2025-08-26T15:39:13.712040+00:00", "mode": "restart", "current": 0, "friendly_name": "晴空燈"}, "last_changed": "2025-08-26T17:29:50.888548+00:00", "last_reported": "2025-08-26T17:29:50.888548+00:00", "last_updated": "2025-08-26T17:29:50.888548+00:00", "context": {"id": "01K3KPVMH80SJFKHRBT170SPPS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.ri_luo_zi_dong_kai_lou_ti_bi_deng", "state": "on", "attributes": {"id": "1741240299234", "last_triggered": "2025-08-26T10:02:35.857785+00:00", "mode": "single", "current": 0, "friendly_name": "自動樓梯壁燈"}, "last_changed": "2025-08-26T17:29:50.888579+00:00", "last_reported": "2025-08-26T17:29:50.888579+00:00", "last_updated": "2025-08-26T17:29:50.888579+00:00", "context": {"id": "01K3KPVMH88WC6G0G4EQ0SPKWV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.zi_dong_kai_guan_che_ku_men", "state": "on", "attributes": {"id": "1741241552349", "last_triggered": "2025-08-26T11:49:13.735307+00:00", "mode": "single", "current": 0, "friendly_name": "自動開關車庫門"}, "last_changed": "2025-08-26T17:29:50.888607+00:00", "last_reported": "2025-08-26T17:29:50.888607+00:00", "last_updated": "2025-08-26T17:29:50.888607+00:00", "context": {"id": "01K3KPVMH81YXZJ60FW3CVYEPB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.mailbox", "state": "on", "attributes": {"id": "1741539922722", "last_triggered": "2025-08-25T02:39:29.590343+00:00", "mode": "single", "current": 0, "friendly_name": "Mailbox"}, "last_changed": "2025-08-26T17:29:50.888635+00:00", "last_reported": "2025-08-26T17:29:50.888635+00:00", "last_updated": "2025-08-26T17:29:50.888635+00:00", "context": {"id": "01K3KPVMH8S9ZSVVTT9TXF3XN5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.men_kai_qi_hou_kai_deng_2", "state": "on", "attributes": {"id": "1741834329889", "last_triggered": "2025-08-26T15:33:42.732487+00:00", "mode": "single", "current": 0, "friendly_name": "門開啟後開燈"}, "last_changed": "2025-08-26T17:29:50.888661+00:00", "last_reported": "2025-08-26T17:29:50.888661+00:00", "last_updated": "2025-08-26T17:29:50.888661+00:00", "context": {"id": "01K3KPVMH83SEFZAWACGP3AXE8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.can_ting_deng", "state": "on", "attributes": {"id": "1742129441705", "last_triggered": "2025-08-25T14:54:01.119862+00:00", "mode": "restart", "current": 0, "friendly_name": "餐廳燈"}, "last_changed": "2025-08-26T17:29:50.888686+00:00", "last_reported": "2025-08-26T17:29:50.888686+00:00", "last_updated": "2025-08-26T17:29:50.888686+00:00", "context": {"id": "01K3KPVMH8E5R58GRDBTFCHTSZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.zi_dong_guan_rong_zhu_deng", "state": "on", "attributes": {"id": "1746689048551", "last_triggered": "2025-08-26T15:35:11.039477+00:00", "mode": "single", "current": 0, "friendly_name": "自動關融燭燈"}, "last_changed": "2025-08-26T17:29:50.888719+00:00", "last_reported": "2025-08-26T17:29:50.888719+00:00", "last_updated": "2025-08-26T17:29:50.888719+00:00", "context": {"id": "01K3KPVMH8FVXPEECCGN0VGAPB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.zi_dong_xuan_guan_deng", "state": "on", "attributes": {"id": "1747027764081", "last_triggered": "2025-08-26T15:36:36.600405+00:00", "mode": "restart", "current": 0, "friendly_name": "自動玄關燈"}, "last_changed": "2025-08-26T17:29:50.888746+00:00", "last_reported": "2025-08-26T17:29:50.888746+00:00", "last_updated": "2025-08-26T17:29:50.888746+00:00", "context": {"id": "01K3KPVMH8EBW50BC7RRWMQNYC", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.wo_shi_deng", "state": "on", "attributes": {"id": "1748529345949", "last_triggered": "2025-08-26T15:35:11.220706+00:00", "mode": "restart", "current": 0, "friendly_name": "臥室燈"}, "last_changed": "2025-08-26T17:29:50.888773+00:00", "last_reported": "2025-08-26T17:29:50.888773+00:00", "last_updated": "2025-08-26T17:29:50.888773+00:00", "context": {"id": "01K3KPVMH82MPWMR2S55HQRJ5C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.che_ku_men_guan_bi_hou_yan_chi_guan_deng", "state": "on", "attributes": {"id": "1717518265000", "last_triggered": "2025-08-26T15:38:27.000918+00:00", "mode": "restart", "current": 0, "friendly_name": "車庫門關閉後延遲關燈"}, "last_changed": "2025-08-26T17:29:50.888798+00:00", "last_reported": "2025-08-26T17:29:50.888798+00:00", "last_updated": "2025-08-26T17:29:50.888798+00:00", "context": {"id": "01K3KPVMH8K90BTJZDADPD11HE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.da_men_deng", "state": "on", "attributes": {"id": "1750523301886", "last_triggered": "2025-08-26T12:30:00.416509+00:00", "mode": "single", "current": 0, "friendly_name": "大門燈"}, "last_changed": "2025-08-26T17:29:50.888833+00:00", "last_reported": "2025-08-26T17:29:50.888833+00:00", "last_updated": "2025-08-26T17:29:50.888833+00:00", "context": {"id": "01K3KPVMH87XXCE83R15NPWN4J", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "automation.mail_notifier", "state": "on", "attributes": {"id": "1754650959484", "last_triggered": "2025-08-25T02:39:29.592578+00:00", "mode": "single", "current": 0, "friendly_name": "Mail notifier"}, "last_changed": "2025-08-26T17:29:50.888865+00:00", "last_reported": "2025-08-26T17:29:50.888865+00:00", "last_updated": "2025-08-26T17:29:50.888865+00:00", "context": {"id": "01K3KPVMH8VGND3M2PQD5JCTK2", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.hong_yi_ji_error", "state": "unknown", "attributes": {"event_types": ["no_filter_error", "door_open_error", "motor_lock_error", "door_sensor_error", "temperature_sensor_error", "empty_water_alert_error", "door_lock_error", "high_power_supply_error", "drainmotor_error", "compressor_error", "high_temperature_detection_error"], "event_type": null, "friendly_name": "烘衣機 Error"}, "last_changed": "2025-08-26T17:29:51.507034+00:00", "last_reported": "2025-08-26T17:29:51.507034+00:00", "last_updated": "2025-08-26T17:29:51.507034+00:00", "context": {"id": "01K3KPVN4KC457CNZTD1N1RZHT", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.hong_yi_ji_notification", "state": "2025-08-23T13:59:13.778+00:00", "attributes": {"event_types": ["drying_is_complete", "drying_failed"], "event_type": "drying_is_complete", "friendly_name": "烘衣機 Notification"}, "last_changed": "2025-08-26T17:29:51.507246+00:00", "last_reported": "2025-08-26T17:29:51.507246+00:00", "last_updated": "2025-08-26T17:29:51.507246+00:00", "context": {"id": "01K3KPVN4KN57AMY11T8MS7S7P", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "drying_is_complete", "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.xi_yi_ji_error", "state": "2025-08-12T14:04:13.841+00:00", "attributes": {"event_types": ["unable_to_lock_error", "water_drain_error", "water_supply_error", "out_of_balance_error", "overfill_error", "door_open_error", "water_level_sensor_error", "power_fail_error", "temperature_sensor_error", "locked_motor_error"], "event_type": "door_open_error", "friendly_name": "洗衣機 Error"}, "last_changed": "2025-08-26T17:29:51.507407+00:00", "last_reported": "2025-08-26T17:29:51.507407+00:00", "last_updated": "2025-08-26T17:29:51.507407+00:00", "context": {"id": "01K3KPVN4KZDC2W4121YGCFKE5", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "door_open_error", "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.xi_yi_ji_notification", "state": "2025-08-23T11:21:07.807+00:00", "attributes": {"event_types": ["washing_is_complete", "error_during_washing"], "event_type": "washing_is_complete", "friendly_name": "洗衣機 Notification"}, "last_changed": "2025-08-26T17:29:51.507520+00:00", "last_reported": "2025-08-26T17:29:51.507520+00:00", "last_updated": "2025-08-26T17:29:51.507520+00:00", "context": {"id": "01K3KPVN4K6KG2E85MAC0XX2YR", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "washing_is_complete", "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.bing_xiang_notification", "state": "unavailable", "attributes": {"event_types": ["time_to_change_filter", "frozen_is_complete", "door_is_open", "time_to_change_water_filter"], "friendly_name": "冰箱 Notification"}, "last_changed": "2025-08-26T17:29:51.507627+00:00", "last_reported": "2025-08-26T17:29:51.507627+00:00", "last_updated": "2025-08-26T17:29:51.507627+00:00", "context": {"id": "01K3KPVN4K4JMSZVHW8WWV8AH9", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": "time_to_change_water_filter", "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.xi_ji_error", "state": "unavailable", "attributes": {"event_types": ["water_level_sensor_error", "overfill_error", "door_open_error", "locked_motor_error", "unable_to_lock_error", "temperature_sensor_error", "water_supply_error", "water_drain_error", "power_fail_error", "out_of_balance_error"], "friendly_name": "洗機 Error"}, "last_changed": "2025-08-26T17:29:51.507737+00:00", "last_reported": "2025-08-26T17:29:51.507737+00:00", "last_updated": "2025-08-26T17:29:51.507737+00:00", "context": {"id": "01K3KPVN4KW5NGHJ3PXX1XNA3Z", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.xi_ji_notification", "state": "unavailable", "attributes": {"event_types": ["error_during_washing", "washing_is_complete"], "friendly_name": "洗機 Notification"}, "last_changed": "2025-08-26T17:29:51.507838+00:00", "last_reported": "2025-08-26T17:29:51.507838+00:00", "last_updated": "2025-08-26T17:29:51.507838+00:00", "context": {"id": "01K3KPVN4KT40JERT3E8RGVEBN", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.dian_zi_yi_chu_error", "state": "unknown", "attributes": {"event_types": ["door_open_error", "need_water_drain", "need_water_replenishment", "le2_error", "le_error", "water_leaks_error", "water_level_sensor_error", "steam_heat_error", "door_close_error", "temperature_sensor_error"], "event_type": null, "friendly_name": "電子衣櫥 Error"}, "last_changed": "2025-08-26T17:29:51.507934+00:00", "last_reported": "2025-08-26T17:29:51.507934+00:00", "last_updated": "2025-08-26T17:29:51.507934+00:00", "context": {"id": "01K3KPVN4KNB9E9EWRVANMGMV5", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.dian_zi_yi_chu_notification", "state": "unknown", "attributes": {"event_types": ["styling_is_complete", "error_has_occurred"], "event_type": null, "friendly_name": "電子衣櫥 Notification"}, "last_changed": "2025-08-26T17:29:51.508060+00:00", "last_reported": "2025-08-26T17:29:51.508060+00:00", "last_updated": "2025-08-26T17:29:51.508060+00:00", "context": {"id": "01K3KPVN4M2Q3N7RFTQCC0TGGK", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "event.chu_shi_ji_notification", "state": "unknown", "attributes": {"event_types": ["water_is_full"], "event_type": null, "friendly_name": "除濕機 Notification"}, "last_changed": "2025-08-26T17:29:51.508220+00:00", "last_reported": "2025-08-26T17:29:51.508220+00:00", "last_updated": "2025-08-26T17:29:51.508220+00:00", "context": {"id": "01K3KPVN4MYNW6KB9YWEWB610D", "parent_id": null, "user_id": null}}, "extra_data": {"last_event_type": null, "last_event_attributes": null}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_horn", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:bullhorn", "friendly_name": "老鴨號 Horn"}, "last_changed": "2025-08-26T17:29:51.952916+00:00", "last_reported": "2025-08-26T17:29:51.952916+00:00", "last_updated": "2025-08-26T17:29:51.952916+00:00", "context": {"id": "01K3KPVNJGVMDZSY1P134GG2WS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_flash_lights", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:car-light-high", "friendly_name": "老鴨號 Flash lights"}, "last_changed": "2025-08-26T17:29:51.952987+00:00", "last_reported": "2025-08-26T17:29:51.952987+00:00", "last_updated": "2025-08-26T17:29:51.952987+00:00", "context": {"id": "01K3KPVNJGV1MW5E138APC8VRD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_wake_up", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:moon-waning-crescent", "friendly_name": "老鴨號 Wake up"}, "last_changed": "2025-08-26T17:29:51.953059+00:00", "last_reported": "2025-08-26T17:29:51.953059+00:00", "last_updated": "2025-08-26T17:29:51.953059+00:00", "context": {"id": "01K3KPVNJH28806AGMZ30W7XYQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_force_data_update", "state": "2025-08-25T07:41:11.748639+00:00", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:database-sync", "friendly_name": "老鴨號 Force data update"}, "last_changed": "2025-08-26T17:29:51.953120+00:00", "last_reported": "2025-08-26T17:29:51.953120+00:00", "last_updated": "2025-08-26T17:29:51.953120+00:00", "context": {"id": "01K3KPVNJH6XK6PMBVYSJX0F9Y", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.lao_ya_hao_remote_start", "state": "unknown", "attributes": {"attribution": "Data provided by Tesla", "icon": "mdi:power", "friendly_name": "老鴨號 Remote start"}, "last_changed": "2025-08-26T17:29:51.953196+00:00", "last_reported": "2025-08-26T17:29:51.953196+00:00", "last_updated": "2025-08-26T17:29:51.953196+00:00", "context": {"id": "01K3KPVNJHDXAG5K6JGRHGQJPG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.lao_ya_hao_software_update", "state": "unknown", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": null, "in_progress": false, "latest_version": null, "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "attribution": "Data provided by Tesla", "entity_picture": "https://brands.home-assistant.io/_/tesla_custom/icon.png", "friendly_name": "老鴨號 Software update", "supported_features": 4}, "last_changed": "2025-08-26T17:29:51.953702+00:00", "last_reported": "2025-08-26T17:29:51.953702+00:00", "last_updated": "2025-08-26T17:29:51.953702+00:00", "context": {"id": "01K3KPVNJHFD8K6XV1SD0S7BZS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_C210_18A8 Reboot"}, "last_changed": "2025-08-26T17:29:52.733209+00:00", "last_reported": "2025-08-26T17:29:52.733209+00:00", "last_updated": "2025-08-26T17:29:52.733209+00:00", "context": {"id": "01K3KPVPAXMNFZQGG49T874Y82", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_C210_18A8 Format SD Card"}, "last_changed": "2025-08-26T17:29:52.733352+00:00", "last_reported": "2025-08-26T17:29:52.733352+00:00", "last_updated": "2025-08-26T17:29:52.733352+00:00", "context": {"id": "01K3KPVPAX9GQJP741B57VR2YY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_C210_18A8 Manual Alarm Start"}, "last_changed": "2025-08-26T17:29:52.733443+00:00", "last_reported": "2025-08-26T17:29:52.733443+00:00", "last_updated": "2025-08-26T17:29:52.733443+00:00", "context": {"id": "01K3KPVPAXMD6DXQA1J3S96789", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_C210_18A8 Manual Alarm Stop"}, "last_changed": "2025-08-26T17:29:52.733522+00:00", "last_reported": "2025-08-26T17:29:52.733522+00:00", "last_updated": "2025-08-26T17:29:52.733522+00:00", "context": {"id": "01K3KPVPAX43M9A5WE0J6PAFD4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-27 01:56:32", "seconds_from_1970": 1756230992}, "dst_data": {"enabled": "0", "synced": "0", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_C210_18A8 Sync Time"}, "last_changed": "2025-08-26T17:30:23.973862+00:00", "last_reported": "2025-08-26T17:56:34.217547+00:00", "last_updated": "2025-08-26T17:56:34.217547+00:00", "context": {"id": "01K3KRCJ99KTD412QM53HKX8SH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_C210_18A8 Calibrate"}, "last_changed": "2025-08-26T17:29:52.733679+00:00", "last_reported": "2025-08-26T17:29:52.733679+00:00", "last_updated": "2025-08-26T17:29:52.733679+00:00", "context": {"id": "01K3KPVPAX1N8JRV3HBJ6J994R", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_C210_18A8 Move Up"}, "last_changed": "2025-08-26T17:29:52.733749+00:00", "last_reported": "2025-08-26T17:29:52.733749+00:00", "last_updated": "2025-08-26T17:29:52.733749+00:00", "context": {"id": "01K3KPVPAX01GWD942NWD42W96", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_C210_18A8 Move Down"}, "last_changed": "2025-08-26T17:29:52.733814+00:00", "last_reported": "2025-08-26T17:29:52.733814+00:00", "last_updated": "2025-08-26T17:29:52.733814+00:00", "context": {"id": "01K3KPVPAXCG2HH6YF0KND6F8R", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_C210_18A8 Move Right"}, "last_changed": "2025-08-26T17:29:52.733881+00:00", "last_reported": "2025-08-26T17:29:52.733881+00:00", "last_updated": "2025-08-26T17:29:52.733881+00:00", "context": {"id": "01K3KPVPAXFYM2GRQES8ZMZEB7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.tapo_c210_18a8_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_C210_18A8 Move Left"}, "last_changed": "2025-08-26T17:29:52.733942+00:00", "last_reported": "2025-08-26T17:29:52.733942+00:00", "last_updated": "2025-08-26T17:29:52.733942+00:00", "context": {"id": "01K3KPVPAXSBC5GKVRFCS9D8CK", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "number.tapo_c210_18a8_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_C210_18A8 Movement Angle"}, "last_changed": "2025-08-26T17:29:52.734286+00:00", "last_reported": "2025-08-26T17:29:52.734286+00:00", "last_updated": "2025-08-26T17:29:52.734286+00:00", "context": {"id": "01K3KPVPAY65NV39D0DQGSAXQJ", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "update.tapo_c210_18a8_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.4.7 Build 250625 Rel.58841n", "in_progress": false, "latest_version": "1.4.7 Build 250625 Rel.58841n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_C210_18A8 Update", "supported_features": 21}, "last_changed": "2025-08-26T17:29:52.736173+00:00", "last_reported": "2025-08-26T17:29:52.736173+00:00", "last_updated": "2025-08-26T17:29:52.736173+00:00", "context": {"id": "01K3KPVPB0ZC3X05R1ZF31HBV5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.washtower_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "WashTower Remote Start"}, "last_changed": "2025-08-26T17:29:54.785606+00:00", "last_reported": "2025-08-26T17:29:54.785606+00:00", "last_updated": "2025-08-26T17:29:54.785606+00:00", "context": {"id": "01K3KPVRB1GHEA4B1KAEYDWK1V", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.washtower_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "Wash<PERSON>ow<PERSON> Pause"}, "last_changed": "2025-08-26T17:29:54.785661+00:00", "last_reported": "2025-08-26T17:29:54.785661+00:00", "last_updated": "2025-08-26T17:29:54.785661+00:00", "context": {"id": "01K3KPVRB1D6PNYBES4DXQ9EV5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.xi_yi_ji_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "洗衣機 Remote Start"}, "last_changed": "2025-08-26T17:29:54.785711+00:00", "last_reported": "2025-08-26T17:29:54.785711+00:00", "last_updated": "2025-08-26T17:29:54.785711+00:00", "context": {"id": "01K3KPVRB1782375J6HSNVJM3N", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.xi_yi_ji_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "洗衣機 Pause"}, "last_changed": "2025-08-26T17:29:54.785757+00:00", "last_reported": "2025-08-26T17:29:54.785757+00:00", "last_updated": "2025-08-26T17:29:54.785757+00:00", "context": {"id": "01K3KPVRB1476717Z3WMG1J82D", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.xi_ji_remote_start", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:play-circle-outline", "friendly_name": "洗機 Remote Start"}, "last_changed": "2025-08-26T17:29:54.785797+00:00", "last_reported": "2025-08-26T17:29:54.785797+00:00", "last_updated": "2025-08-26T17:29:54.785797+00:00", "context": {"id": "01K3KPVRB10G1G3XKFACBT6XT4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.xi_ji_pause", "state": "unavailable", "attributes": {"device_class": "update", "icon": "mdi:pause-circle-outline", "friendly_name": "洗機 Pause"}, "last_changed": "2025-08-26T17:29:54.785847+00:00", "last_reported": "2025-08-26T17:29:54.785847+00:00", "last_updated": "2025-08-26T17:29:54.785847+00:00", "context": {"id": "01K3KPVRB1ER3YQPVDNR6EHXY1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.s8_pro_ultra_quan_wu_qing_jie", "state": "unknown", "attributes": {"friendly_name": "S8 Pro Ultra 全屋清潔"}, "last_changed": "2025-08-26T17:29:56.454661+00:00", "last_reported": "2025-08-26T17:29:56.454661+00:00", "last_updated": "2025-08-26T17:29:56.454661+00:00", "context": {"id": "01K3KPVSZ6F5YQBFWKBWZW0QZW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:57:24.409477+00:00"}, {"state": {"entity_id": "button.dsm_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "DSM Reboot"}, "last_changed": "2025-08-25T10:31:39.364464+00:00", "last_reported": "2025-08-25T10:31:39.364464+00:00", "last_updated": "2025-08-25T10:31:39.364464+00:00", "context": {"id": "01K3GCH6342AQ7X0ZSWH0CYGGS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.dsm_shutdown", "state": "unknown", "attributes": {"icon": "mdi:power", "friendly_name": "DSM Shutdown"}, "last_changed": "2025-08-25T10:31:39.364637+00:00", "last_reported": "2025-08-25T10:31:39.364637+00:00", "last_updated": "2025-08-25T10:31:39.364637+00:00", "context": {"id": "01K3GCH634QGKMEVHR0TNDNKNB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.dsm_dsm_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "DSM 7.2.2-72806 Update 4", "in_progress": false, "latest_version": "DSM 7.2.2-72806 Update 4", "release_summary": null, "release_url": null, "skipped_version": null, "title": "Synology DSM", "update_percentage": null, "attribution": "Data provided by Synology", "entity_picture": "https://brands.home-assistant.io/_/synology_dsm/icon.png", "friendly_name": "DSM DSM update", "supported_features": 0}, "last_changed": "2025-08-26T10:34:03.012059+00:00", "last_reported": "2025-08-26T10:34:03.012059+00:00", "last_updated": "2025-08-26T10:34:03.012059+00:00", "context": {"id": "01K3JZ29C4A9QBYAY77C92BT67", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.zigbee2mqtt_bridge_connection_state", "state": "on", "attributes": {"device_class": "connectivity", "friendly_name": "Zigbee2MQTT Bridge Connection state"}, "last_changed": "2025-08-25T10:31:42.117999+00:00", "last_reported": "2025-08-25T10:31:42.117999+00:00", "last_updated": "2025-08-25T10:31:42.117999+00:00", "context": {"id": "01K3GCH8S540BWBWB0S7NMWTYX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138d20c4e154d_presence", "state": "on", "attributes": {"device_class": "presence", "friendly_name": "玄關人在感應 Presence"}, "last_changed": "2025-08-25T10:32:21.215998+00:00", "last_reported": "2025-08-25T10:32:21.215998+00:00", "last_updated": "2025-08-25T10:32:21.215998+00:00", "context": {"id": "01K3GCJEYZPC4RZA5A2Z71TRNM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0x842712fffe7cec33_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "玄關動作感應 Occupancy"}, "last_changed": "2025-08-26T15:37:40.392509+00:00", "last_reported": "2025-08-26T15:37:40.392509+00:00", "last_updated": "2025-08-26T15:37:40.392509+00:00", "context": {"id": "01K3KGE7S81NRTJ2KA7SJWJMFH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138ba2d3d0757_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱門感應 Door"}, "last_changed": "2025-08-25T10:32:21.277529+00:00", "last_reported": "2025-08-25T10:32:21.277529+00:00", "last_updated": "2025-08-25T10:32:21.277529+00:00", "context": {"id": "01K3GCJF0X1AP3XFKA9MBSNMQB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.garage_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫門感應 Door"}, "last_changed": "2025-08-26T11:52:41.378409+00:00", "last_reported": "2025-08-26T11:52:41.378409+00:00", "last_updated": "2025-08-26T11:52:41.378409+00:00", "context": {"id": "01K3K3J952CTK5BFW7GT46AYM9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.garage_side_door", "state": "off", "attributes": {"device_class": "door", "friendly_name": "車庫側門感應 Door"}, "last_changed": "2025-08-26T15:36:27.608941+00:00", "last_reported": "2025-08-26T15:36:27.608941+00:00", "last_updated": "2025-08-26T15:36:27.608941+00:00", "context": {"id": "01K3KGC0PRXXDWVHENY9KYCF3A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0x00158d000708748f_contact", "state": "off", "attributes": {"device_class": "door", "friendly_name": "信箱感應 Door"}, "last_changed": "2025-08-25T10:32:21.289468+00:00", "last_reported": "2025-08-25T10:32:21.289468+00:00", "last_updated": "2025-08-25T10:32:21.289468+00:00", "context": {"id": "01K3GCJF197S78KE0PWGVQENEA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0x842712fffe7ce6d3_occupancy", "state": "off", "attributes": {"device_class": "occupancy", "friendly_name": "3F 動作感應 Occupancy"}, "last_changed": "2025-08-26T15:32:53.187047+00:00", "last_reported": "2025-08-26T15:32:53.187047+00:00", "last_updated": "2025-08-26T15:32:53.187047+00:00", "context": {"id": "01K3KG5FA3R7A347A9ZC39YFXY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.3f_ren_zai_gan_ying_presence", "state": "off", "attributes": {"device_class": "presence", "friendly_name": "3F 人在感應 Presence"}, "last_changed": "2025-08-26T15:34:32.498599+00:00", "last_reported": "2025-08-26T15:34:32.498599+00:00", "last_updated": "2025-08-26T15:34:32.498599+00:00", "context": {"id": "01K3KG8G9J14AXWHCEVVMGRYXR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "binary_sensor.0xa4c138b04b602c88_presence", "state": "on", "attributes": {"device_class": "presence", "friendly_name": "0xa4c138b04b602c88 Presence"}, "last_changed": "2025-08-26T15:39:58.581609+00:00", "last_reported": "2025-08-26T15:39:58.581609+00:00", "last_updated": "2025-08-26T15:39:58.581609+00:00", "context": {"id": "01K3KGJEQN1Q4ZQ35PJTKG0840", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.zigbee2mqtt_bridge_restart", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Zigbee2MQTT Bridge Restart"}, "last_changed": "2025-08-25T10:31:42.108512+00:00", "last_reported": "2025-08-25T10:31:42.108512+00:00", "last_updated": "2025-08-25T10:31:42.108512+00:00", "context": {"id": "01K3GCH8RWDJ798BDDYSHXE40F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.0xf84477fffe4d19bd_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾右 Factory reset"}, "last_changed": "2025-08-25T10:31:42.116917+00:00", "last_reported": "2025-08-25T10:31:42.116917+00:00", "last_updated": "2025-08-25T10:31:42.116917+00:00", "context": {"id": "01K3GCH8S4TCYYKQWS2YME09C3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.0xf84477fffe40747a_factory_reset", "state": "unknown", "attributes": {"friendly_name": "窗簾左 Factory reset"}, "last_changed": "2025-08-25T10:31:42.118087+00:00", "last_reported": "2025-08-25T10:31:42.118087+00:00", "last_updated": "2025-08-25T10:31:42.118087+00:00", "context": {"id": "01K3GCH8S6H82JB1GZG74X90TJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.4f_bathroom", "state": "on", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": "color_temp", "brightness": 217, "color_temp_kelvin": 2000, "color_temp": 500, "hs_color": [30.601, 94.547], "rgb_color": [255, 137, 14], "xy_color": [0.598, 0.383], "friendly_name": "4F 浴室燈", "supported_features": 44}, "last_changed": "2025-08-25T10:32:21.330890+00:00", "last_reported": "2025-08-25T10:32:21.330890+00:00", "last_updated": "2025-08-25T10:32:21.330890+00:00", "context": {"id": "01K3GCJF2JZ3NTAYH6BFENQX3C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138ec6626cadd", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳中間崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.122945+00:00", "last_reported": "2025-08-25T14:54:01.122945+00:00", "last_updated": "2025-08-25T14:54:01.122945+00:00", "context": {"id": "01K3GVHJX238NEEBDQR7SMKECY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13844a96c99c6", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳右崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.117712+00:00", "last_reported": "2025-08-25T14:54:01.117712+00:00", "last_updated": "2025-08-25T14:54:01.117712+00:00", "context": {"id": "01K3GVHJWX0YDEBCRCW0G9CRWT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0x70b3d52b601209fa", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左吊燈", "supported_features": 44}, "last_changed": "2025-08-25T16:41:30.520078+00:00", "last_reported": "2025-08-25T16:41:31.474104+00:00", "last_updated": "2025-08-25T16:41:30.520078+00:00", "context": {"id": "01K3H1PD4RJ2REEFJXV68ZNMTJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13814b628056b", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右吊燈", "supported_features": 44}, "last_changed": "2025-08-25T16:41:30.495211+00:00", "last_reported": "2025-08-25T16:41:30.495211+00:00", "last_updated": "2025-08-25T16:41:30.495211+00:00", "context": {"id": "01K3H1PD3Z5BN13F7QYT2JVBS1", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138a6755b559d", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳左崁燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.125090+00:00", "last_reported": "2025-08-25T14:54:01.125090+00:00", "last_updated": "2025-08-25T14:54:01.125090+00:00", "context": {"id": "01K3GVHJX5S52FAEXNC0Y4TJZY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138385587e524", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左下崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.579555+00:00", "last_reported": "2025-08-26T15:39:18.579555+00:00", "last_updated": "2025-08-26T15:39:18.579555+00:00", "context": {"id": "01K3KGH7NKEJEWBRD5VNZQM9QW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c13893935668b4", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳左上崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.581791+00:00", "last_reported": "2025-08-26T15:39:18.581791+00:00", "last_updated": "2025-08-26T15:39:18.581791+00:00", "context": {"id": "01K3KGH7NNAPQZ9PP6M30KW5KB", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c1380a172e5a6a", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右上崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.580755+00:00", "last_reported": "2025-08-26T15:39:18.580755+00:00", "last_updated": "2025-08-26T15:39:18.580755+00:00", "context": {"id": "01K3KGH7NM542Q7JN178719AYW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.0xa4c138d449095da3", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳右下崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.577199+00:00", "last_reported": "2025-08-26T15:39:18.577199+00:00", "last_updated": "2025-08-26T15:39:18.577199+00:00", "context": {"id": "01K3KGH7NHRR2F0EN02JCVWZVQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.ceiling", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳吊燈", "supported_features": 44}, "last_changed": "2025-08-25T16:41:30.517076+00:00", "last_reported": "2025-08-25T16:44:47.557704+00:00", "last_updated": "2025-08-25T16:41:30.517076+00:00", "context": {"id": "01K3H1PD2C0ABVPS7BAKRVHMBM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.dining", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "餐廳燈", "supported_features": 44}, "last_changed": "2025-08-25T14:54:01.118829+00:00", "last_reported": "2025-08-25T14:54:01.118829+00:00", "last_updated": "2025-08-25T14:54:01.118829+00:00", "context": {"id": "01K3GVHJNTP394YP1BPZYA7HKZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "light.downlight", "state": "off", "attributes": {"min_color_temp_kelvin": 2000, "max_color_temp_kelvin": 6535, "min_mireds": 153, "max_mireds": 500, "effect_list": ["blink", "breathe", "okay", "channel_change", "finish_effect", "stop_effect", "colorloop", "stop_colorloop"], "supported_color_modes": ["color_temp", "xy"], "effect": null, "color_mode": null, "brightness": null, "color_temp_kelvin": null, "color_temp": null, "hs_color": null, "rgb_color": null, "xy_color": null, "friendly_name": "客廳崁燈", "supported_features": 44}, "last_changed": "2025-08-26T15:39:18.578075+00:00", "last_reported": "2025-08-26T15:39:18.578075+00:00", "last_updated": "2025-08-26T15:39:18.578075+00:00", "context": {"id": "01K3KGH7ECMGS5MR5G2EKP0P3F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_large_motion_detection_sensitivity", "state": "2", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Large motion detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215395+00:00", "last_reported": "2025-08-25T10:32:21.215395+00:00", "last_updated": "2025-08-25T10:32:21.215395+00:00", "context": {"id": "01K3GCJEYZ7SKFTQV55CZ3W9FH", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 2}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_large_motion_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Large motion detection distance"}, "last_changed": "2025-08-25T10:32:21.215518+00:00", "last_reported": "2025-08-25T10:32:21.215518+00:00", "last_updated": "2025-08-25T10:32:21.215518+00:00", "context": {"id": "01K3GCJEYZ7BP3G77EFXGMF5D1", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_fading_time", "state": "60", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "玄關人在感應 Fading time"}, "last_changed": "2025-08-25T10:32:21.215790+00:00", "last_reported": "2025-08-25T10:32:21.215790+00:00", "last_updated": "2025-08-25T10:32:21.215790+00:00", "context": {"id": "01K3GCJEYZM8XYB5S15SH2SA0N", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_medium_motion_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Medium motion detection distance"}, "last_changed": "2025-08-25T10:32:21.215721+00:00", "last_reported": "2025-08-25T10:32:21.215721+00:00", "last_updated": "2025-08-25T10:32:21.215721+00:00", "context": {"id": "01K3GCJEYZ6Y9VYXFRE0199MC7", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_medium_motion_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Medium motion detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215921+00:00", "last_reported": "2025-08-25T10:32:21.215921+00:00", "last_updated": "2025-08-25T10:32:21.215921+00:00", "context": {"id": "01K3GCJEYZF2S26B6PGGFTWX6S", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_small_detection_distance", "state": "3", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Small detection distance"}, "last_changed": "2025-08-25T10:32:21.215126+00:00", "last_reported": "2025-08-25T10:32:21.215126+00:00", "last_updated": "2025-08-25T10:32:21.215126+00:00", "context": {"id": "01K3GCJEYZYDANET3CVDJHDBRX", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 3}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138d20c4e154d_small_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "玄關人在感應 Small detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.215578+00:00", "last_reported": "2025-08-25T10:32:21.215578+00:00", "last_updated": "2025-08-25T10:32:21.215578+00:00", "context": {"id": "01K3GCJEYZ07EK1P6SC64F2J0E", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.xuan_guan_ren_zai_gan_ying_minimum_range", "state": "unknown", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "玄關人在感應 Minimum range"}, "last_changed": "2025-08-25T10:31:42.116182+00:00", "last_reported": "2025-08-25T10:31:42.116182+00:00", "last_updated": "2025-08-25T10:31:42.116182+00:00", "context": {"id": "01K3GCH8S4F6VKVN44VTHZ0YTT", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x842712fffe7cec33_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "玄關動作感應 Motion timeout"}, "last_changed": "2025-08-25T10:31:42.119079+00:00", "last_reported": "2025-08-25T10:31:42.119079+00:00", "last_updated": "2025-08-25T10:31:42.119079+00:00", "context": {"id": "01K3GCH8S7MJC0JRP0ZVTQ2HA5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x6cfd22fffe19abea_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l1"}, "last_changed": "2025-08-25T10:32:21.236213+00:00", "last_reported": "2025-08-25T10:32:21.236213+00:00", "last_updated": "2025-08-25T10:32:21.236213+00:00", "context": {"id": "01K3GCJEZMTHZ65NXRTWQ04FHX", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x6cfd22fffe19abea_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "車庫燈開關 Countdown l2"}, "last_changed": "2025-08-25T10:32:21.235941+00:00", "last_reported": "2025-08-25T10:32:21.235941+00:00", "last_updated": "2025-08-25T10:32:21.235941+00:00", "context": {"id": "01K3GCJEZK5KHQYVNFF0W7TEA7", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138ba2d3d0757_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "信箱門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.277708+00:00", "last_reported": "2025-08-25T10:32:21.277708+00:00", "last_updated": "2025-08-25T10:32:21.277708+00:00", "context": {"id": "01K3GCJF0X16B8SBPKQME0J4YE", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c13870139ddef7_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.279139+00:00", "last_reported": "2025-08-25T10:32:21.279139+00:00", "last_updated": "2025-08-25T10:32:21.279139+00:00", "context": {"id": "01K3GCJF0ZP6ZRCSV5HSZ07P6C", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138591f9083d0_illuminance_interval", "state": "60", "attributes": {"min": 1.0, "max": 720.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "minutes", "friendly_name": "車庫側門感應 Illuminance interval"}, "last_changed": "2025-08-25T10:32:21.286031+00:00", "last_reported": "2025-08-25T10:32:21.286031+00:00", "last_updated": "2025-08-25T10:32:21.286031+00:00", "context": {"id": "01K3GCJF16V89D51K5S4ZHP037", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 720.0, "native_min_value": 1.0, "native_step": 1.0, "native_unit_of_measurement": "minutes", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x842712fffe7ce6d3_motion_timeout", "state": "unknown", "attributes": {"min": 5.0, "max": 60.0, "step": 1.0, "mode": "auto", "friendly_name": "3F 動作感應 Motion timeout"}, "last_changed": "2025-08-25T10:31:42.110447+00:00", "last_reported": "2025-08-25T10:31:42.110447+00:00", "last_updated": "2025-08-25T10:31:42.110447+00:00", "context": {"id": "01K3GCH8RYGT8J46FK2WP89PS8", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 60.0, "native_min_value": 5.0, "native_step": 1.0, "native_unit_of_measurement": null, "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_large_motion_detection_sensitivity", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Large motion detection sensitivity"}, "last_changed": "2025-08-25T10:31:46.862745+00:00", "last_reported": "2025-08-25T10:31:46.862745+00:00", "last_updated": "2025-08-25T10:31:46.862745+00:00", "context": {"id": "01K3GCHDDEMNYXK2YCK2H7TTNF", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 6}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_large_motion_detection_distance", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Large motion detection distance"}, "last_changed": "2025-08-25T10:31:46.862015+00:00", "last_reported": "2025-08-25T10:31:46.862015+00:00", "last_updated": "2025-08-25T10:31:46.862015+00:00", "context": {"id": "01K3GCHDDEX4Z7YDB5VJFRYSY1", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 6}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_fading_time", "state": "60", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "3F 人在感應 Fading time"}, "last_changed": "2025-08-25T10:31:46.862347+00:00", "last_reported": "2025-08-25T10:31:46.862347+00:00", "last_updated": "2025-08-25T10:31:46.862347+00:00", "context": {"id": "01K3GCHDDEMK3K1NJ0AX3SX3WN", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 60}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_medium_motion_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Medium motion detection distance"}, "last_changed": "2025-08-25T10:31:46.862637+00:00", "last_reported": "2025-08-25T10:31:46.862637+00:00", "last_updated": "2025-08-25T10:31:46.862637+00:00", "context": {"id": "01K3GCHDDED2DCDFE37R8AQTD5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_medium_motion_detection_sensitivity", "state": "8", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Medium motion detection sensitivity"}, "last_changed": "2025-08-25T10:31:46.862291+00:00", "last_reported": "2025-08-25T10:31:46.862291+00:00", "last_updated": "2025-08-25T10:31:46.862291+00:00", "context": {"id": "01K3GCHDDE4J74T2KRTFP04A1C", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 8}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_small_detection_distance", "state": "4", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Small detection distance"}, "last_changed": "2025-08-25T10:31:46.862141+00:00", "last_reported": "2025-08-25T10:31:46.862141+00:00", "last_updated": "2025-08-25T10:31:46.862141+00:00", "context": {"id": "01K3GCHDDE42HK9TY5A3FFC17Y", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 4}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_small_detection_sensitivity", "state": "8", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "3F 人在感應 Small detection sensitivity"}, "last_changed": "2025-08-25T10:31:46.862691+00:00", "last_reported": "2025-08-25T10:31:46.862691+00:00", "last_updated": "2025-08-25T10:31:46.862691+00:00", "context": {"id": "01K3GCHDDEBFAA2ASVZM9JMJYP", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 8}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.3f_ren_zai_gan_ying_minimum_range", "state": "unknown", "attributes": {"min": 0.0, "max": 6.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "3F 人在感應 Minimum range"}, "last_changed": "2025-08-25T10:31:42.116534+00:00", "last_reported": "2025-08-25T10:31:42.116534+00:00", "last_updated": "2025-08-25T10:31:42.116534+00:00", "context": {"id": "01K3GCH8S4WG310ZSMCTPTN916", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 6.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x70c59cfffe264997_countdown_l1", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l1"}, "last_changed": "2025-08-25T10:32:21.295424+00:00", "last_reported": "2025-08-25T10:32:21.295424+00:00", "last_updated": "2025-08-25T10:32:21.295424+00:00", "context": {"id": "01K3GCJF1FRJT3WBHVHMDB8CFW", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0x70c59cfffe264997_countdown_l2", "state": "0", "attributes": {"min": 0.0, "max": 43200.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "4F 樓梯開關 Countdown l2"}, "last_changed": "2025-08-25T10:32:21.295845+00:00", "last_reported": "2025-08-25T10:32:21.295845+00:00", "last_updated": "2025-08-25T10:32:21.295845+00:00", "context": {"id": "01K3GCJF1FDM7HVXD2YG1Y4YW3", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 43200.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 0}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_fading_time", "state": "30", "attributes": {"min": 0.0, "max": 28800.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "s", "friendly_name": "0xa4c138b04b602c88 Fading time"}, "last_changed": "2025-08-25T10:32:21.353182+00:00", "last_reported": "2025-08-25T10:32:21.353182+00:00", "last_updated": "2025-08-25T10:32:21.353182+00:00", "context": {"id": "01K3GCJF39X9HMRAR8Z8BY13JF", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 28800.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "s", "native_value": 30}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_static_detection_distance", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 0.01, "mode": "auto", "unit_of_measurement": "m", "friendly_name": "0xa4c138b04b602c88 Static detection distance"}, "last_changed": "2025-08-25T10:32:21.353334+00:00", "last_reported": "2025-08-25T10:32:21.353334+00:00", "last_updated": "2025-08-25T10:32:21.353334+00:00", "context": {"id": "01K3GCJF39PJ7P0E7Z5B616BFY", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 0.01, "native_unit_of_measurement": "m", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_static_detection_sensitivity", "state": "6", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "0xa4c138b04b602c88 Static detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.352620+00:00", "last_reported": "2025-08-25T10:32:21.352620+00:00", "last_updated": "2025-08-25T10:32:21.352620+00:00", "context": {"id": "01K3GCJF387F116CXPNDQZ4D4V", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 6}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.0xa4c138b04b602c88_motion_detection_sensitivity", "state": "5", "attributes": {"min": 0.0, "max": 10.0, "step": 1.0, "mode": "auto", "unit_of_measurement": "x", "friendly_name": "0xa4c138b04b602c88 Motion detection sensitivity"}, "last_changed": "2025-08-25T10:32:21.353395+00:00", "last_reported": "2025-08-25T10:32:21.353395+00:00", "last_updated": "2025-08-25T10:32:21.353395+00:00", "context": {"id": "01K3GCJF39GKX175Z915NF2PSP", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 10.0, "native_min_value": 0.0, "native_step": 1.0, "native_unit_of_measurement": "x", "native_value": 5}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.zigbee2mqtt_bridge_log_level", "state": "info", "attributes": {"options": ["error", "warning", "info", "debug"], "friendly_name": "Zigbee2MQTT Bridge Log level"}, "last_changed": "2025-08-25T10:31:42.123445+00:00", "last_reported": "2025-08-25T10:31:42.123445+00:00", "last_updated": "2025-08-25T10:31:42.123445+00:00", "context": {"id": "01K3GCH8SBHNGTD0QCG2JMN3J5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c2f31_power_on_behavior", "state": "previous", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 廁所開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.197127+00:00", "last_reported": "2025-08-25T10:32:21.197127+00:00", "last_updated": "2025-08-25T10:32:21.197127+00:00", "context": {"id": "01K3GCJEYD6YPE6E0Y03GEATZZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c98f6_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 壁燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.197726+00:00", "last_reported": "2025-08-25T10:32:21.197726+00:00", "last_updated": "2025-08-25T10:32:21.197726+00:00", "context": {"id": "01K3GCJEYD1KHDJ1BGCF8P0PPZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:32:21.236427+00:00", "last_reported": "2025-08-25T10:32:21.236427+00:00", "last_updated": "2025-08-25T10:32:21.236427+00:00", "context": {"id": "01K3GCJEZM1P1D2ZDQTF21DRD5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫燈開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:32:21.236086+00:00", "last_reported": "2025-08-25T10:32:21.236086+00:00", "last_updated": "2025-08-25T10:32:21.236086+00:00", "context": {"id": "01K3GCJEZM9Z197M0N2J60S32G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫燈開關 Switch type"}, "last_changed": "2025-08-25T10:31:42.109607+00:00", "last_reported": "2025-08-25T10:31:42.109607+00:00", "last_updated": "2025-08-25T10:31:42.109607+00:00", "context": {"id": "01K3GCH8RXR2V5F2GDV1R2KH03", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe19abea_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "車庫燈開關 Indicator mode"}, "last_changed": "2025-08-25T10:32:21.236268+00:00", "last_reported": "2025-08-25T10:32:21.236268+00:00", "last_updated": "2025-08-25T10:32:21.236268+00:00", "context": {"id": "01K3GCJEZM8D3GG8K7VR9HJ9J6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13840bdea388c_switch_type", "state": "state", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "車庫門 Switch type"}, "last_changed": "2025-08-25T10:32:21.237560+00:00", "last_reported": "2025-08-25T10:32:21.237560+00:00", "last_updated": "2025-08-25T10:32:21.237560+00:00", "context": {"id": "01K3GCJEZNSN0KEQ6Q0EF2N40T", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.che_ku_men_power_on_behavior", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "車庫門 Power-on behavior"}, "last_changed": "2025-08-25T10:31:42.116816+00:00", "last_reported": "2025-08-25T10:31:42.116816+00:00", "last_updated": "2025-08-25T10:31:42.116816+00:00", "context": {"id": "01K3GCH8S4A71FK9P7J38C7FKX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.shu_fang_deng_kai_guan_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "書房燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.243695+00:00", "last_reported": "2025-08-25T10:32:21.243695+00:00", "last_updated": "2025-08-25T10:32:21.243695+00:00", "context": {"id": "01K3GCJEZVPK2QV7SBP28Q5KGY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe5487e3_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.244752+00:00", "last_reported": "2025-08-25T10:32:21.244752+00:00", "last_updated": "2025-08-25T10:32:21.244752+00:00", "context": {"id": "01K3GCJEZWVAJK9866ABEB8DAH", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe548a6d_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "更衣室開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.247140+00:00", "last_reported": "2025-08-25T10:32:21.247140+00:00", "last_updated": "2025-08-25T10:32:21.247140+00:00", "context": {"id": "01K3GCJEZZ1H4XJ5Z6XN4TC9Z5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:32:21.295558+00:00", "last_reported": "2025-08-25T10:32:21.295558+00:00", "last_updated": "2025-08-25T10:32:21.295558+00:00", "context": {"id": "01K3GCJF1FN94Z81R258BG1NW3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:32:21.295895+00:00", "last_reported": "2025-08-25T10:32:21.295895+00:00", "last_updated": "2025-08-25T10:32:21.295895+00:00", "context": {"id": "01K3GCJF1FRDJNYGW395192V1E", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_switch_type", "state": "unknown", "attributes": {"options": ["toggle", "state", "momentary"], "icon": "mdi:tune", "friendly_name": "4F 樓梯開關 Switch type"}, "last_changed": "2025-08-25T10:31:42.110742+00:00", "last_reported": "2025-08-25T10:31:42.110742+00:00", "last_updated": "2025-08-25T10:31:42.110742+00:00", "context": {"id": "01K3GCH8RYDG7TMR93BC8CQKFF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70c59cfffe264997_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "4F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-25T10:32:21.295675+00:00", "last_reported": "2025-08-25T10:32:21.295675+00:00", "last_updated": "2025-08-25T10:32:21.295675+00:00", "context": {"id": "01K3GCJF1FYATE52NPBJBX8200", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1c9c6a_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 陽台燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.297999+00:00", "last_reported": "2025-08-25T10:32:21.297999+00:00", "last_updated": "2025-08-25T10:32:21.297999+00:00", "context": {"id": "01K3GCJF1HAD2QZWCMZRE3E6JG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1cb10f_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈門邊開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.299471+00:00", "last_reported": "2025-08-25T10:32:21.299471+00:00", "last_updated": "2025-08-25T10:32:21.299471+00:00", "context": {"id": "01K3GCJF1KQAJYRCSEPH9XAMJS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x6cfd22fffe1a034e_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "臥室燈牆面開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.316589+00:00", "last_reported": "2025-08-25T10:32:21.316589+00:00", "last_updated": "2025-08-25T10:32:21.316589+00:00", "context": {"id": "01K3GCJF24TRDQEEBMF8PE5FWW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x4c97a1fffe548970_power_on_behavior", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "4F 浴室燈開關 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.321550+00:00", "last_reported": "2025-08-25T10:32:21.321550+00:00", "last_updated": "2025-08-25T10:32:21.321550+00:00", "context": {"id": "01K3GCJF299F3NZJ77ZA56M1X7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138a6ff2209eb_color_power_on_behavior", "state": "previous", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "4F 浴室燈 Color power on behavior"}, "last_changed": "2025-08-25T10:32:21.330552+00:00", "last_reported": "2025-08-25T10:32:21.330552+00:00", "last_updated": "2025-08-25T10:32:21.330552+00:00", "context": {"id": "01K3GCJF2JV6YAQ1ZWVZ5A3DNR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138ec6626cadd_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳中間崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.114173+00:00", "last_reported": "2025-08-25T10:31:42.114173+00:00", "last_updated": "2025-08-25T10:31:42.114173+00:00", "context": {"id": "01K3GCH8S2NBQYBJ2GBEWN3BGY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13844a96c99c6_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳右崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.114340+00:00", "last_reported": "2025-08-25T10:31:42.114340+00:00", "last_updated": "2025-08-25T10:31:42.114340+00:00", "context": {"id": "01K3GCH8S285RM6R27EXZMZ076", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0x70b3d52b601209fa_power_on_behavior", "state": "off", "attributes": {"options": ["off", "on", "toggle", "previous"], "icon": "mdi:power-settings", "friendly_name": "客廳左吊燈 Power-on behavior"}, "last_changed": "2025-08-25T10:32:21.334116+00:00", "last_reported": "2025-08-25T10:32:21.334116+00:00", "last_updated": "2025-08-25T10:32:21.334116+00:00", "context": {"id": "01K3GCJF2P549CFGC2X6TCNTA5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13814b628056b_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右吊燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.118833+00:00", "last_reported": "2025-08-25T10:31:42.118833+00:00", "last_updated": "2025-08-25T10:31:42.118833+00:00", "context": {"id": "01K3GCH8S6AKRT6C8961D4B92Y", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138a6755b559d_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "餐廳左崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.117826+00:00", "last_reported": "2025-08-25T10:31:42.117826+00:00", "last_updated": "2025-08-25T10:31:42.117826+00:00", "context": {"id": "01K3GCH8S5EXGV9P3XYG27YACV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138385587e524_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左下崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.116057+00:00", "last_reported": "2025-08-25T10:31:42.116057+00:00", "last_updated": "2025-08-25T10:31:42.116057+00:00", "context": {"id": "01K3GCH8S47Q73DR85VJTHKZVE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c13893935668b4_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳左上崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.118191+00:00", "last_reported": "2025-08-25T10:31:42.118191+00:00", "last_updated": "2025-08-25T10:31:42.118191+00:00", "context": {"id": "01K3GCH8S684TEYRSWH77WMYNQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c1380a172e5a6a_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右上崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.111725+00:00", "last_reported": "2025-08-25T10:31:42.111725+00:00", "last_updated": "2025-08-25T10:31:42.111725+00:00", "context": {"id": "01K3GCH8RZ7QC5YEAYV5BEV1KJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138d449095da3_color_power_on_behavior", "state": "unknown", "attributes": {"options": ["initial", "previous", "customized"], "icon": "mdi:palette", "friendly_name": "客廳右下崁燈 Color power on behavior"}, "last_changed": "2025-08-25T10:31:42.108334+00:00", "last_reported": "2025-08-25T10:31:42.108334+00:00", "last_updated": "2025-08-25T10:31:42.108334+00:00", "context": {"id": "01K3GCH8RW4CW3NTTPTSZA8DJF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾右 Opening mode"}, "last_changed": "2025-08-25T10:32:21.348189+00:00", "last_reported": "2025-08-25T10:32:21.348189+00:00", "last_updated": "2025-08-25T10:32:21.348189+00:00", "context": {"id": "01K3GCJF34V6NVHE5A577G3W4Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾右 Motor direction"}, "last_changed": "2025-08-25T10:32:21.346613+00:00", "last_reported": "2025-08-25T10:32:21.346613+00:00", "last_updated": "2025-08-25T10:32:21.346613+00:00", "context": {"id": "01K3GCJF32JJK7FGQX5CCD27GE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe4d19bd_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾右 Set upper limit"}, "last_changed": "2025-08-25T10:32:21.346485+00:00", "last_reported": "2025-08-25T10:32:21.346485+00:00", "last_updated": "2025-08-25T10:32:21.346485+00:00", "context": {"id": "01K3GCJF32H8JPT82DG3KGWKXR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_opening_mode", "state": "lift", "attributes": {"options": ["tilt", "lift"], "friendly_name": "窗簾左 Opening mode"}, "last_changed": "2025-08-25T10:32:21.350420+00:00", "last_reported": "2025-08-25T10:32:21.350420+00:00", "last_updated": "2025-08-25T10:32:21.350420+00:00", "context": {"id": "01K3GCJF367HSJCZ6MA2BV0E7Z", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_motor_direction", "state": "right", "attributes": {"options": ["left", "right"], "friendly_name": "窗簾左 Motor direction"}, "last_changed": "2025-08-25T10:32:21.350732+00:00", "last_reported": "2025-08-25T10:32:21.350732+00:00", "last_updated": "2025-08-25T10:32:21.350732+00:00", "context": {"id": "01K3GCJF36CB60WS57M36BB6MS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xf84477fffe40747a_set_upper_limit", "state": "stop", "attributes": {"options": ["start", "stop"], "friendly_name": "窗簾左 Set upper limit"}, "last_changed": "2025-08-25T10:32:21.350670+00:00", "last_reported": "2025-08-25T10:32:21.350670+00:00", "last_updated": "2025-08-25T10:32:21.350670+00:00", "context": {"id": "01K3GCJF3673S8VPNC2WW6TJ62", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.0xa4c138b04b602c88_motion_detection_mode", "state": "only_pir", "attributes": {"options": ["only_pir", "pir_and_radar", "only_radar"], "friendly_name": "0xa4c138b04b602c88 Motion detection mode"}, "last_changed": "2025-08-25T10:32:21.353112+00:00", "last_reported": "2025-08-25T10:32:21.353112+00:00", "last_updated": "2025-08-25T10:32:21.353112+00:00", "context": {"id": "01K3GCJF397NVSE82V8ERVK4DY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:31:42.119236+00:00", "last_reported": "2025-08-25T10:31:42.119236+00:00", "last_updated": "2025-08-25T10:31:42.119236+00:00", "context": {"id": "01K3GCH8S76EGQMFFE5Q931NQT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:31:42.111673+00:00", "last_reported": "2025-08-25T10:31:42.111673+00:00", "last_updated": "2025-08-25T10:31:42.111673+00:00", "context": {"id": "01K3GCH8RZ41DHXQ5MAET0558J", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "客廳開關 Power-on behavior l3"}, "last_changed": "2025-08-25T10:31:42.114758+00:00", "last_reported": "2025-08-25T10:31:42.114758+00:00", "last_updated": "2025-08-25T10:31:42.114758+00:00", "context": {"id": "01K3GCH8S2TKFCB9V7387KZXJ8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.ke_ting_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "客廳開關 Indicator mode"}, "last_changed": "2025-08-25T10:31:42.111084+00:00", "last_reported": "2025-08-25T10:31:42.111084+00:00", "last_updated": "2025-08-25T10:31:42.111084+00:00", "context": {"id": "01K3GCH8RZZGMWE98HZQZTY91C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:31:42.113420+00:00", "last_reported": "2025-08-25T10:31:42.113420+00:00", "last_updated": "2025-08-25T10:31:42.113420+00:00", "context": {"id": "01K3GCH8S1AK077ZWATFB1BBHP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:31:42.117876+00:00", "last_reported": "2025-08-25T10:31:42.117876+00:00", "last_updated": "2025-08-25T10:31:42.117876+00:00", "context": {"id": "01K3GCH8S58YN080D9GW531J2F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "2F 樓梯開關 Power-on behavior l3"}, "last_changed": "2025-08-25T10:31:42.108988+00:00", "last_reported": "2025-08-25T10:31:42.108988+00:00", "last_updated": "2025-08-25T10:31:42.108988+00:00", "context": {"id": "01K3GCH8RWY4R481SD5VEHB4GZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.2f_lou_ti_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "2F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-25T10:31:42.110691+00:00", "last_reported": "2025-08-25T10:31:42.110691+00:00", "last_updated": "2025-08-25T10:31:42.110691+00:00", "context": {"id": "01K3GCH8RYB95FP9E3C18Q2T56", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:31:42.119337+00:00", "last_reported": "2025-08-25T10:31:42.119337+00:00", "last_updated": "2025-08-25T10:31:42.119337+00:00", "context": {"id": "01K3GCH8S77W1Q9WFV5XH6FHE9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:31:42.118428+00:00", "last_reported": "2025-08-25T10:31:42.118428+00:00", "last_updated": "2025-08-25T10:31:42.118428+00:00", "context": {"id": "01K3GCH8S6VF5WK64RJ8ANGZ41", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "廚房開關 Power-on behavior l3"}, "last_changed": "2025-08-25T10:31:42.111142+00:00", "last_reported": "2025-08-25T10:31:42.111142+00:00", "last_updated": "2025-08-25T10:31:42.111142+00:00", "context": {"id": "01K3GCH8RZVA9NQ603YAMQBC59", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.chu_fang_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "廚房開關 Indicator mode"}, "last_changed": "2025-08-25T10:31:42.118781+00:00", "last_reported": "2025-08-25T10:31:42.118781+00:00", "last_updated": "2025-08-25T10:31:42.118781+00:00", "context": {"id": "01K3GCH8S6Y6ZE17QZNDCRNVH5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l1", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:31:42.115091+00:00", "last_reported": "2025-08-25T10:31:42.115091+00:00", "last_updated": "2025-08-25T10:31:42.115091+00:00", "context": {"id": "01K3GCH8S3HY27DAX1FYKQTASE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l2", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:31:42.112687+00:00", "last_reported": "2025-08-25T10:31:42.112687+00:00", "last_updated": "2025-08-25T10:31:42.112687+00:00", "context": {"id": "01K3GCH8S0D9TYJG5KP72F28JD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_power_on_behavior_l3", "state": "unknown", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "玄關開關 Power-on behavior l3"}, "last_changed": "2025-08-25T10:31:42.113316+00:00", "last_reported": "2025-08-25T10:31:42.113316+00:00", "last_updated": "2025-08-25T10:31:42.113316+00:00", "context": {"id": "01K3GCH8S1Q289KARR6FX8YTAT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.xuan_guan_kai_guan_indicator_mode", "state": "unknown", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "玄關開關 Indicator mode"}, "last_changed": "2025-08-25T10:31:42.110261+00:00", "last_reported": "2025-08-25T10:31:42.110261+00:00", "last_updated": "2025-08-25T10:31:42.110261+00:00", "context": {"id": "01K3GCH8RYS0XKD0YKF62ZFK2C", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l1", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l1"}, "last_changed": "2025-08-25T10:32:21.268585+00:00", "last_reported": "2025-08-25T10:32:21.268585+00:00", "last_updated": "2025-08-25T10:32:21.268585+00:00", "context": {"id": "01K3GCJF0MRVFG3CWVB6A01BRM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l2", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l2"}, "last_changed": "2025-08-25T10:32:21.272308+00:00", "last_reported": "2025-08-25T10:32:21.272308+00:00", "last_updated": "2025-08-25T10:32:21.272308+00:00", "context": {"id": "01K3GCJF0RC14WGCV1XCEGRZJQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_power_on_behavior_l3", "state": "off", "attributes": {"options": ["off", "previous", "on"], "icon": "mdi:power-settings", "friendly_name": "3F 樓梯開關 Power-on behavior l3"}, "last_changed": "2025-08-25T10:32:21.268663+00:00", "last_reported": "2025-08-25T10:32:21.268663+00:00", "last_updated": "2025-08-25T10:32:21.268663+00:00", "context": {"id": "01K3GCJF0MXTK0QRYE7KP758FA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "select.3f_lou_ti_kai_guan_indicator_mode", "state": "on/off", "attributes": {"options": ["off", "off/on", "on/off", "on"], "friendly_name": "3F 樓梯開關 Indicator mode"}, "last_changed": "2025-08-25T10:32:21.268885+00:00", "last_reported": "2025-08-25T10:32:21.268885+00:00", "last_updated": "2025-08-25T10:32:21.268885+00:00", "context": {"id": "01K3GCJF0MXEBKDKN04G8C3MCN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.zigbee2mqtt_bridge_version", "state": "2.6.0", "attributes": {"icon": "mdi:zigbee", "friendly_name": "Zigbee2MQTT Bridge Version"}, "last_changed": "2025-08-25T10:31:42.123577+00:00", "last_reported": "2025-08-25T10:31:42.123577+00:00", "last_updated": "2025-08-25T10:31:42.123577+00:00", "context": {"id": "01K3GCH8SBSBS3B6B3W4S9Y66M", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "2.6.0", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138d20c4e154d_illuminance", "state": "1896", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "玄關人在感應 Illuminance"}, "last_changed": "2025-08-25T10:32:21.215318+00:00", "last_reported": "2025-08-25T10:32:21.215318+00:00", "last_updated": "2025-08-25T10:32:21.215318+00:00", "context": {"id": "01K3GCJEYZBGXWGVCKZ5GDN6N3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "1896", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138d20c4e154d_motion_state", "state": "large", "attributes": {"friendly_name": "玄關人在感應 Motion state"}, "last_changed": "2025-08-25T10:32:21.215457+00:00", "last_reported": "2025-08-25T10:32:21.215457+00:00", "last_updated": "2025-08-25T10:32:21.215457+00:00", "context": {"id": "01K3GCJEYZ2TXPNXZR5J2BFF7W", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "large", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.xuan_guan_ren_zai_gan_ying_target_distance", "state": "unknown", "attributes": {"unit_of_measurement": "m", "friendly_name": "玄關人在感應 Target distance"}, "last_changed": "2025-08-25T10:31:42.115450+00:00", "last_reported": "2025-08-25T10:31:42.115450+00:00", "last_updated": "2025-08-25T10:31:42.115450+00:00", "context": {"id": "01K3GCH8S3DZ41FPGERDWEK04K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": null, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_illumination", "state": "dim", "attributes": {"friendly_name": "玄關動作感應 Illumination"}, "last_changed": "2025-08-26T08:56:38.461061+00:00", "last_reported": "2025-08-26T08:56:38.461061+00:00", "last_updated": "2025-08-26T08:56:38.461061+00:00", "context": {"id": "01K3JSFXSXFGX0TMA81CN1J07N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "玄關動作感應 Battery"}, "last_changed": "2025-08-25T10:32:21.230433+00:00", "last_reported": "2025-08-25T10:32:21.230433+00:00", "last_updated": "2025-08-25T10:32:21.230433+00:00", "context": {"id": "01K3GCJEZEJG4RGGM3ERDFRY48", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7cec33_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "玄關動作感應 Voltage"}, "last_changed": "2025-08-25T10:32:21.230942+00:00", "last_reported": "2025-08-25T10:32:21.230942+00:00", "last_updated": "2025-08-25T10:32:21.230942+00:00", "context": {"id": "01K3GCJEZEB0JDQEHWTAV94BCB", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d00073a5cae_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "小米無線開關 Battery"}, "last_changed": "2025-08-25T10:32:21.274036+00:00", "last_reported": "2025-08-25T10:32:21.274036+00:00", "last_updated": "2025-08-25T10:32:21.274036+00:00", "context": {"id": "01K3GCJF0TQRF8M59XGBXFWC7N", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d00073a5cae_voltage", "state": "3062", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "小米無線開關 Voltage"}, "last_changed": "2025-08-25T10:32:21.273875+00:00", "last_reported": "2025-08-25T10:32:21.273875+00:00", "last_updated": "2025-08-25T10:32:21.273875+00:00", "context": {"id": "01K3GCJF0SZS1F167NWZS5T5H6", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3062", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138ba2d3d0757_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "信箱門感應 Illuminance"}, "last_changed": "2025-08-25T10:32:21.277312+00:00", "last_reported": "2025-08-25T10:32:21.277312+00:00", "last_updated": "2025-08-25T10:32:21.277312+00:00", "context": {"id": "01K3GCJF0XQ4X6KQXJAWZTDKR5", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138ba2d3d0757_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.277627+00:00", "last_reported": "2025-08-25T10:32:21.277627+00:00", "last_updated": "2025-08-25T10:32:21.277627+00:00", "context": {"id": "01K3GCJF0XTKQK2KX0E6AFCN6K", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c13870139ddef7_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫門感應 Illuminance"}, "last_changed": "2025-08-26T12:52:31.993305+00:00", "last_reported": "2025-08-26T12:52:31.993305+00:00", "last_updated": "2025-08-26T12:52:31.993305+00:00", "context": {"id": "01K3K6ZVKSNGGKZT8TJQWRVG9Z", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c13870139ddef7_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.279048+00:00", "last_reported": "2025-08-25T10:32:21.279048+00:00", "last_updated": "2025-08-25T10:32:21.279048+00:00", "context": {"id": "01K3GCJF0ZP3XV96PCMDFN9Q1G", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138591f9083d0_illuminance", "state": "0", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "車庫側門感應 Illuminance"}, "last_changed": "2025-08-26T16:36:53.822163+00:00", "last_reported": "2025-08-26T16:36:53.822163+00:00", "last_updated": "2025-08-26T16:36:53.822163+00:00", "context": {"id": "01K3KKTNXYD6PYCX0WDBAYT1BZ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "0", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138591f9083d0_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "車庫側門感應 Battery"}, "last_changed": "2025-08-25T10:32:21.285685+00:00", "last_reported": "2025-08-25T10:32:21.285685+00:00", "last_updated": "2025-08-25T10:32:21.285685+00:00", "context": {"id": "01K3GCJF15MSDXK9GVXN86J95P", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "信箱感應 Battery"}, "last_changed": "2025-08-25T10:32:21.289854+00:00", "last_reported": "2025-08-25T10:32:21.289854+00:00", "last_updated": "2025-08-25T10:32:21.289854+00:00", "context": {"id": "01K3GCJF19NVYTP1PGADNTBXCV", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_device_temperature", "state": "33", "attributes": {"state_class": "measurement", "unit_of_measurement": "°C", "device_class": "temperature", "friendly_name": "信箱感應 Temperature"}, "last_changed": "2025-08-26T14:57:15.104690+00:00", "last_reported": "2025-08-26T14:57:15.104690+00:00", "last_updated": "2025-08-26T14:57:15.104690+00:00", "context": {"id": "01K3KE47B02JCZ4322S7G8H1C3", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "33", "native_unit_of_measurement": "°C"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x00158d000708748f_voltage", "state": "3045", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "信箱感應 Voltage"}, "last_changed": "2025-08-26T15:47:28.215691+00:00", "last_reported": "2025-08-26T15:47:28.215691+00:00", "last_updated": "2025-08-26T15:47:28.215691+00:00", "context": {"id": "01K3KH05TQHS19AK7WNCTQGA5B", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3045", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_illumination", "state": "dim", "attributes": {"friendly_name": "3F 動作感應 Illumination"}, "last_changed": "2025-08-26T15:00:18.288643+00:00", "last_reported": "2025-08-26T15:00:18.288643+00:00", "last_updated": "2025-08-26T15:00:18.288643+00:00", "context": {"id": "01K3KE9T7GZWZ31MRG5FDDZR6Z", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "dim", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "3F 動作感應 Battery"}, "last_changed": "2025-08-25T10:32:21.292520+00:00", "last_reported": "2025-08-25T10:32:21.292520+00:00", "last_updated": "2025-08-25T10:32:21.292520+00:00", "context": {"id": "01K3GCJF1C1EW7CGEREDVJ2J3A", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0x842712fffe7ce6d3_voltage", "state": "3000", "attributes": {"state_class": "measurement", "unit_of_measurement": "mV", "device_class": "voltage", "friendly_name": "3F 動作感應 Voltage"}, "last_changed": "2025-08-25T10:32:21.292288+00:00", "last_reported": "2025-08-25T10:32:21.292288+00:00", "last_updated": "2025-08-25T10:32:21.292288+00:00", "context": {"id": "01K3GCJF1C5E265RV156PYPMJR", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "3000", "native_unit_of_measurement": "mV"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_illuminance", "state": "45", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "3F 人在感應 Illuminance"}, "last_changed": "2025-08-26T16:44:02.984202+00:00", "last_reported": "2025-08-26T16:44:02.984202+00:00", "last_updated": "2025-08-26T16:44:02.984202+00:00", "context": {"id": "01K3KM7S18K0HC65YD97DSWEYJ", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "45", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_motion_state", "state": "none", "attributes": {"friendly_name": "3F 人在感應 Motion state"}, "last_changed": "2025-08-26T15:34:32.609510+00:00", "last_reported": "2025-08-26T15:34:32.609510+00:00", "last_updated": "2025-08-26T15:34:32.609510+00:00", "context": {"id": "01K3KG8GD196T2ZDA2E419FYBK", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "none", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.3f_ren_zai_gan_ying_target_distance", "state": "unknown", "attributes": {"unit_of_measurement": "m", "friendly_name": "3F 人在感應 Target distance"}, "last_changed": "2025-08-25T10:31:42.114009+00:00", "last_reported": "2025-08-25T10:31:42.114009+00:00", "last_updated": "2025-08-25T10:31:42.114009+00:00", "context": {"id": "01K3GCH8S24PPPKTM2A2XDD3E1", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": null, "native_unit_of_measurement": "m"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe4d19bd_work_state", "state": "standby", "attributes": {"friendly_name": "窗簾右 Work state"}, "last_changed": "2025-08-25T10:32:21.346559+00:00", "last_reported": "2025-08-25T10:32:21.346559+00:00", "last_updated": "2025-08-25T10:32:21.346559+00:00", "context": {"id": "01K3GCJF32XJRAM8GAP5ZRH8TP", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "standby", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe4d19bd_battery", "state": "100", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾右 Battery"}, "last_changed": "2025-08-25T10:32:21.346334+00:00", "last_reported": "2025-08-25T10:32:21.346334+00:00", "last_updated": "2025-08-25T10:32:21.346334+00:00", "context": {"id": "01K3GCJF3259RS7B7H8M7CQK5F", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "100", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe40747a_work_state", "state": "learning", "attributes": {"friendly_name": "窗簾左 Work state"}, "last_changed": "2025-08-25T10:32:21.350876+00:00", "last_reported": "2025-08-25T10:32:21.350876+00:00", "last_updated": "2025-08-25T10:32:21.350876+00:00", "context": {"id": "01K3GCJF36TDJB51K2NVCA4PD7", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "learning", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xf84477fffe40747a_battery", "state": "50", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "窗簾左 Battery"}, "last_changed": "2025-08-25T10:32:21.350592+00:00", "last_reported": "2025-08-25T10:32:21.350592+00:00", "last_updated": "2025-08-25T10:32:21.350592+00:00", "context": {"id": "01K3GCJF36D83E1B31PX7AXXQX", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "50", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_motion_state", "state": "small", "attributes": {"friendly_name": "0xa4c138b04b602c88 Motion state"}, "last_changed": "2025-08-26T17:00:34.856258+00:00", "last_reported": "2025-08-26T17:00:34.856258+00:00", "last_updated": "2025-08-26T17:00:34.856258+00:00", "context": {"id": "01K3KN61N8B0BWDZHF4S73S39H", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "small", "native_unit_of_measurement": null}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_illuminance", "state": "1656", "attributes": {"state_class": "measurement", "unit_of_measurement": "lx", "device_class": "illuminance", "friendly_name": "0xa4c138b04b602c88 Illuminance"}, "last_changed": "2025-08-26T17:00:30.326539+00:00", "last_reported": "2025-08-26T17:00:30.326539+00:00", "last_updated": "2025-08-26T17:00:30.326539+00:00", "context": {"id": "01K3KN5X7PYQR2QQ7E7TBZ0N1T", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "1656", "native_unit_of_measurement": "lx"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "sensor.0xa4c138b04b602c88_battery", "state": "75", "attributes": {"state_class": "measurement", "unit_of_measurement": "%", "device_class": "battery", "friendly_name": "0xa4c138b04b602c88 Battery"}, "last_changed": "2025-08-25T10:32:21.353256+00:00", "last_reported": "2025-08-25T10:32:21.353256+00:00", "last_updated": "2025-08-25T10:32:21.353256+00:00", "context": {"id": "01K3GCJF39ZX3QS1JEM1EKX242", "parent_id": null, "user_id": null}}, "extra_data": {"native_value": "75", "native_unit_of_measurement": "%"}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.zigbee2mqtt_bridge_permit_join", "state": "off", "attributes": {"icon": "mdi:human-greeting-proximity", "friendly_name": "Zigbee2MQTT Bridge Permit join"}, "last_changed": "2025-08-25T10:31:42.123514+00:00", "last_reported": "2025-08-25T10:31:42.123514+00:00", "last_updated": "2025-08-25T10:31:42.123514+00:00", "context": {"id": "01K3GCH8SB5C2T50JVDYF4WK1P", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.2f_restroom", "state": "off", "attributes": {"friendly_name": "2F 廁所開關"}, "last_changed": "2025-08-26T12:39:32.703511+00:00", "last_reported": "2025-08-26T12:39:32.703511+00:00", "last_updated": "2025-08-26T12:39:32.703511+00:00", "context": {"id": "01K3K682JZYRVX7RMWAQDSY8MD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_wall_lamps", "state": "on", "attributes": {"friendly_name": "3F 壁燈開關"}, "last_changed": "2025-08-26T10:02:37.255796+00:00", "last_reported": "2025-08-26T10:02:37.255796+00:00", "last_updated": "2025-08-26T10:02:37.255796+00:00", "context": {"id": "01K3JX8PCT6334P9K0G04E1HAR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138d20c4e154d_indicator", "state": "on", "attributes": {"friendly_name": "玄關人在感應 Indicator"}, "last_changed": "2025-08-25T10:32:21.215852+00:00", "last_reported": "2025-08-25T10:32:21.215852+00:00", "last_updated": "2025-08-25T10:32:21.215852+00:00", "context": {"id": "01K3GCJEYZEXWNWNBEEF3B4RAG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.garage_washer", "state": "off", "attributes": {"friendly_name": "冰箱燈"}, "last_changed": "2025-08-25T10:32:21.236316+00:00", "last_reported": "2025-08-25T10:32:21.236316+00:00", "last_updated": "2025-08-25T10:32:21.236316+00:00", "context": {"id": "01K3GCJEZMQ5MDCC266YR70PAM", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.garage", "state": "off", "attributes": {"friendly_name": "車庫燈"}, "last_changed": "2025-08-26T15:38:27.191759+00:00", "last_reported": "2025-08-26T15:38:27.191759+00:00", "last_updated": "2025-08-26T15:38:27.191759+00:00", "context": {"id": "01K3KGFN9R0ZRQP75VQEZ9Z9CV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x6cfd22fffe19abea_backlight_mode", "state": "on", "attributes": {"friendly_name": "車庫燈開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.236152+00:00", "last_reported": "2025-08-25T10:32:21.236152+00:00", "last_updated": "2025-08-25T10:32:21.236152+00:00", "context": {"id": "01K3GCJEZMDKQXZ4G4QJV3WWDD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.garage_close", "state": "off", "attributes": {"friendly_name": "關"}, "last_changed": "2025-08-26T08:59:21.205934+00:00", "last_reported": "2025-08-26T08:59:21.205934+00:00", "last_updated": "2025-08-26T08:59:21.205934+00:00", "context": {"id": "01K3JSMTEB97G5MJ0Z9HGET6YW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.garage_open", "state": "off", "attributes": {"friendly_name": "開"}, "last_changed": "2025-08-26T11:49:15.800666+00:00", "last_reported": "2025-08-26T11:49:15.800666+00:00", "last_updated": "2025-08-26T11:49:15.800666+00:00", "context": {"id": "01K3K3BYC7J86A8SH4CY54H5VR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.studio", "state": "on", "attributes": {"friendly_name": "書房燈開關"}, "last_changed": "2025-08-26T12:39:49.923164+00:00", "last_reported": "2025-08-26T12:39:49.923164+00:00", "last_updated": "2025-08-26T12:39:49.923164+00:00", "context": {"id": "01K3K68KD3WKN93WCAEQW8WVYR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_bathroom", "state": "off", "attributes": {"friendly_name": "3F 浴室燈開關"}, "last_changed": "2025-08-26T12:41:38.404325+00:00", "last_reported": "2025-08-26T12:41:38.404325+00:00", "last_updated": "2025-08-26T12:41:38.404325+00:00", "context": {"id": "01K3K6BXB4FRZKV8ZDNQJ7PZ5F", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.dress_room", "state": "off", "attributes": {"friendly_name": "更衣室開關"}, "last_changed": "2025-08-26T14:06:30.072872+00:00", "last_reported": "2025-08-26T14:06:30.072872+00:00", "last_updated": "2025-08-26T14:06:30.072872+00:00", "context": {"id": "01K3KB79NR2R96Y3Y2BPHW369E", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_ren_zai_gan_ying_indicator", "state": "on", "attributes": {"friendly_name": "3F 人在感應 Indicator"}, "last_changed": "2025-08-25T10:31:46.862578+00:00", "last_reported": "2025-08-25T10:31:46.862578+00:00", "last_updated": "2025-08-25T10:31:46.862578+00:00", "context": {"id": "01K3GCHDDESBJ6222CDFAEDSF6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x70c59cfffe264997_l1", "state": "off", "attributes": {"friendly_name": "4F 樓梯開關 L1"}, "last_changed": "2025-08-25T10:32:21.295726+00:00", "last_reported": "2025-08-25T10:32:21.295726+00:00", "last_updated": "2025-08-25T10:32:21.295726+00:00", "context": {"id": "01K3GCJF1FZ2ZM25QVV7KKX7S5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.4f_wall_lamp", "state": "on", "attributes": {"friendly_name": "4F 壁燈開關"}, "last_changed": "2025-08-26T10:02:37.422945+00:00", "last_reported": "2025-08-26T10:02:37.422945+00:00", "last_updated": "2025-08-26T10:02:37.422945+00:00", "context": {"id": "01K3JX8PCT6334P9K0G04E1HAR", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0x70c59cfffe264997_backlight_mode", "state": "on", "attributes": {"friendly_name": "4F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.295624+00:00", "last_reported": "2025-08-25T10:32:21.295624+00:00", "last_updated": "2025-08-25T10:32:21.295624+00:00", "context": {"id": "01K3GCJF1FM39AA6NT7X91FAVJ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.4f_balcony", "state": "off", "attributes": {"friendly_name": "4F 陽台燈開關"}, "last_changed": "2025-08-25T10:32:21.298176+00:00", "last_reported": "2025-08-25T10:32:21.298176+00:00", "last_updated": "2025-08-25T10:32:21.298176+00:00", "context": {"id": "01K3GCJF1J28ECX33DP2BEJ0NS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.bedroom_door", "state": "off", "attributes": {"friendly_name": "臥室燈門邊開關"}, "last_changed": "2025-08-26T15:35:11.468965+00:00", "last_reported": "2025-08-26T15:35:11.468965+00:00", "last_updated": "2025-08-26T15:35:11.468965+00:00", "context": {"id": "01K3KG9P3KKVZKAN7H98HS61QD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.bedroom_wall", "state": "off", "attributes": {"friendly_name": "臥室燈牆面開關"}, "last_changed": "2025-08-26T15:35:11.219619+00:00", "last_reported": "2025-08-26T15:35:11.219619+00:00", "last_updated": "2025-08-26T15:35:11.219619+00:00", "context": {"id": "01K3KG9NWSDFX1ZFG0J8Q7PPBW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.4f_bathroom", "state": "off", "attributes": {"friendly_name": "4F 浴室燈開關"}, "last_changed": "2025-08-26T14:25:24.781307+00:00", "last_reported": "2025-08-26T14:25:24.781307+00:00", "last_updated": "2025-08-26T14:25:24.781307+00:00", "context": {"id": "01K3KC9XSD0S1Q36ES2CA78GB5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138a6ff2209eb_do_not_disturb", "state": "off", "attributes": {"friendly_name": "4F 浴室燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.330682+00:00", "last_reported": "2025-08-25T10:32:21.330682+00:00", "last_updated": "2025-08-25T10:32:21.330682+00:00", "context": {"id": "01K3GCJF2JJG6N7FHA003Y25AZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138ec6626cadd_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳中間崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.332284+00:00", "last_reported": "2025-08-25T10:32:21.332284+00:00", "last_updated": "2025-08-25T10:32:21.332284+00:00", "context": {"id": "01K3GCJF2MB9XESZCZATSSYED3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13844a96c99c6_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳右崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.333179+00:00", "last_reported": "2025-08-25T10:32:21.333179+00:00", "last_updated": "2025-08-25T10:32:21.333179+00:00", "context": {"id": "01K3GCJF2NMNV7N9FCF74JM6WG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13814b628056b_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右吊燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.335385+00:00", "last_reported": "2025-08-25T10:32:21.335385+00:00", "last_updated": "2025-08-25T10:32:21.335385+00:00", "context": {"id": "01K3GCJF2QP0MJB17CT9BX7NY8", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138a6755b559d_do_not_disturb", "state": "on", "attributes": {"friendly_name": "餐廳左崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.337423+00:00", "last_reported": "2025-08-25T10:32:21.337423+00:00", "last_updated": "2025-08-25T10:32:21.337423+00:00", "context": {"id": "01K3GCJF2SR6Z0292Q0GJ8RKGS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138385587e524_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳左下崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.343185+00:00", "last_reported": "2025-08-25T10:32:21.343185+00:00", "last_updated": "2025-08-25T10:32:21.343185+00:00", "context": {"id": "01K3GCJF2ZGVPTCPREY2EW2X0G", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c13893935668b4_do_not_disturb", "state": "off", "attributes": {"friendly_name": "客廳左上崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.340438+00:00", "last_reported": "2025-08-25T10:32:21.340438+00:00", "last_updated": "2025-08-25T10:32:21.340438+00:00", "context": {"id": "01K3GCJF2W30SD6RR43S7WKXEQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c1380a172e5a6a_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右上崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.339662+00:00", "last_reported": "2025-08-25T10:32:21.339662+00:00", "last_updated": "2025-08-25T10:32:21.339662+00:00", "context": {"id": "01K3GCJF2VZM16YBX6W78X9MW4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138d449095da3_do_not_disturb", "state": "on", "attributes": {"friendly_name": "客廳右下崁燈 Do not disturb"}, "last_changed": "2025-08-25T10:32:21.338598+00:00", "last_reported": "2025-08-25T10:32:21.338598+00:00", "last_updated": "2025-08-25T10:32:21.338598+00:00", "context": {"id": "01K3GCJF2TZWWAD52RZY0VE2BQ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.0xa4c138b04b602c88_indicator", "state": "on", "attributes": {"friendly_name": "0xa4c138b04b602c88 Indicator"}, "last_changed": "2025-08-25T10:32:21.352966+00:00", "last_reported": "2025-08-25T10:32:21.352966+00:00", "last_updated": "2025-08-25T10:32:21.352966+00:00", "context": {"id": "01K3GCJF38GGT8SY60HQ9E04X4", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.dining", "state": "off", "attributes": {"friendly_name": "餐廳燈開關"}, "last_changed": "2025-08-25T14:54:01.275416+00:00", "last_reported": "2025-08-25T14:54:01.275416+00:00", "last_updated": "2025-08-25T14:54:01.275416+00:00", "context": {"id": "01K3GVHJWZYJ81TKBAE8ESTZ2K", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.ceiling", "state": "off", "attributes": {"friendly_name": "客廳吊燈開關"}, "last_changed": "2025-08-25T16:41:30.706849+00:00", "last_reported": "2025-08-25T16:41:30.706849+00:00", "last_updated": "2025-08-25T16:41:30.706849+00:00", "context": {"id": "01K3H1PD4N6ZHM7APJ4C6C8BV9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.living_room_downlight", "state": "off", "attributes": {"friendly_name": "客廳嵌燈開關"}, "last_changed": "2025-08-26T15:39:18.348633+00:00", "last_reported": "2025-08-26T15:39:18.348633+00:00", "last_updated": "2025-08-26T15:39:18.348633+00:00", "context": {"id": "01K3KGH7ECZQZRV0TY5NACCYGF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.ke_ting_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "客廳開關 Backlight mode"}, "last_changed": "2025-08-25T10:31:42.114065+00:00", "last_reported": "2025-08-25T10:31:42.114065+00:00", "last_updated": "2025-08-25T10:31:42.114065+00:00", "context": {"id": "01K3GCH8S2KYG85VGEB1M7SMGP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.map", "state": "off", "attributes": {"friendly_name": "地圖燈開關"}, "last_changed": "2025-08-25T10:32:21.196364+00:00", "last_reported": "2025-08-25T10:32:21.196364+00:00", "last_updated": "2025-08-25T10:32:21.196364+00:00", "context": {"id": "01K3GCJEYCPRXR3BXJGHN2CFSS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_l2", "state": "off", "attributes": {"friendly_name": "2F 樓梯開關 L2"}, "last_changed": "2025-08-25T10:32:21.196309+00:00", "last_reported": "2025-08-25T10:32:21.196309+00:00", "last_updated": "2025-08-25T10:32:21.196309+00:00", "context": {"id": "01K3GCJEYC6YS5DHBYBRWRR9YA", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.2f_landing", "state": "off", "attributes": {"friendly_name": "2F 樓梯間開關"}, "last_changed": "2025-08-25T10:32:21.196153+00:00", "last_reported": "2025-08-25T10:32:21.196153+00:00", "last_updated": "2025-08-25T10:32:21.196153+00:00", "context": {"id": "01K3GCJEYCH3R7KQGVXEB6YWWD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.2f_lou_ti_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "2F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-25T10:31:42.110397+00:00", "last_reported": "2025-08-25T10:31:42.110397+00:00", "last_updated": "2025-08-25T10:31:42.110397+00:00", "context": {"id": "01K3GCH8RYHBHXBNQ3EP0J3YY6", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.bar", "state": "off", "attributes": {"friendly_name": "吧檯燈"}, "last_changed": "2025-08-25T12:44:52.896106+00:00", "last_reported": "2025-08-25T12:44:52.896106+00:00", "last_updated": "2025-08-25T12:44:52.896106+00:00", "context": {"id": "01K3GM549002PYESJJ814THMHT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.kitchen", "state": "off", "attributes": {"friendly_name": "廚房燈"}, "last_changed": "2025-08-26T15:39:13.711111+00:00", "last_reported": "2025-08-26T15:39:13.711111+00:00", "last_updated": "2025-08-26T15:39:13.711111+00:00", "context": {"id": "01K3KGH2XF7B7623AZG25PS298", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.balcony", "state": "off", "attributes": {"friendly_name": "後陽台燈"}, "last_changed": "2025-08-26T13:58:59.573229+00:00", "last_reported": "2025-08-26T13:58:59.573229+00:00", "last_updated": "2025-08-26T13:58:59.573229+00:00", "context": {"id": "01K3KASHQN861644JW460TCGYF", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.chu_fang_kai_guan_backlight_mode", "state": "unknown", "attributes": {"friendly_name": "廚房開關 Backlight mode"}, "last_changed": "2025-08-25T10:31:42.116618+00:00", "last_reported": "2025-08-25T10:31:42.116618+00:00", "last_updated": "2025-08-25T10:31:42.116618+00:00", "context": {"id": "01K3GCH8S4SNFE5DRWW2D7SRE0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_l1", "state": "off", "attributes": {"friendly_name": "玄關開關 L1"}, "last_changed": "2025-08-25T10:32:21.233273+00:00", "last_reported": "2025-08-25T10:32:21.233273+00:00", "last_updated": "2025-08-25T10:32:21.233273+00:00", "context": {"id": "01K3GCJEZHRM8Z3CW6FAS22ZMZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.entrance", "state": "off", "attributes": {"friendly_name": "玄關燈"}, "last_changed": "2025-08-26T15:40:40.580594+00:00", "last_reported": "2025-08-26T15:40:40.580594+00:00", "last_updated": "2025-08-26T15:40:40.580594+00:00", "context": {"id": "01K3KGC9FDCSRTTN7PFK2326F5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.door", "state": "on", "attributes": {"friendly_name": "玄關開關 L3"}, "last_changed": "2025-08-26T12:30:00.543289+00:00", "last_reported": "2025-08-26T12:30:00.543289+00:00", "last_updated": "2025-08-26T12:30:00.543289+00:00", "context": {"id": "01K3K5PKQ0PTFGF2DZE41WTAWT", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.xuan_guan_kai_guan_backlight_mode", "state": "off", "attributes": {"friendly_name": "玄關開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.233325+00:00", "last_reported": "2025-08-25T10:32:21.233325+00:00", "last_updated": "2025-08-25T10:32:21.233325+00:00", "context": {"id": "01K3GCJEZHBRQ2RJFQDVC6W2S7", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_stairs", "state": "off", "attributes": {"friendly_name": "3F 樓梯燈"}, "last_changed": "2025-08-26T15:33:23.343803+00:00", "last_reported": "2025-08-26T15:33:23.343803+00:00", "last_updated": "2025-08-26T15:33:23.343803+00:00", "context": {"id": "01K3KG3KQQN4FK4H2WWKC5GWC5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_landing", "state": "off", "attributes": {"friendly_name": "3F 樓梯間"}, "last_changed": "2025-08-25T10:32:21.268781+00:00", "last_reported": "2025-08-25T10:32:21.268781+00:00", "last_updated": "2025-08-25T10:32:21.268781+00:00", "context": {"id": "01K3GCJF0MJ31SYN971KVVREW9", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.2f_stairs", "state": "off", "attributes": {"friendly_name": "2F 樓梯燈"}, "last_changed": "2025-08-25T10:32:21.268833+00:00", "last_reported": "2025-08-25T10:32:21.268833+00:00", "last_updated": "2025-08-25T10:32:21.268833+00:00", "context": {"id": "01K3GCJF0MV2RN0Z73H0XQD6J0", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "switch.3f_lou_ti_kai_guan_backlight_mode", "state": "on", "attributes": {"friendly_name": "3F 樓梯開關 Backlight mode"}, "last_changed": "2025-08-25T10:32:21.268728+00:00", "last_reported": "2025-08-25T10:32:21.268728+00:00", "last_updated": "2025-08-25T10:32:21.268728+00:00", "context": {"id": "01K3GCJF0MDMFJF2FDV6EZYKE5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.0x842712fffe7cec33", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "玄關動作感應", "supported_features": 5}, "last_changed": "2025-08-25T10:32:21.230739+00:00", "last_reported": "2025-08-25T10:32:21.230739+00:00", "last_updated": "2025-08-25T10:32:21.230739+00:00", "context": {"id": "01K3GCJEZE7BTKD1SPJGQT7273", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.0x842712fffe7ce6d3", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "8705", "in_progress": false, "latest_version": "8705", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "device_class": "firmware", "entity_picture": "https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png", "friendly_name": "3F 動作感應", "supported_features": 5}, "last_changed": "2025-08-25T10:32:21.292087+00:00", "last_reported": "2025-08-25T10:32:21.292087+00:00", "last_updated": "2025-08-25T10:32:21.292087+00:00", "context": {"id": "01K3GCJF1CTKWKT5PNGSSY47PP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_C220_BC79 Reboot"}, "last_changed": "2025-08-25T10:31:44.196303+00:00", "last_reported": "2025-08-25T10:31:44.196303+00:00", "last_updated": "2025-08-25T10:31:44.196303+00:00", "context": {"id": "01K3GCHAT49DQAW90YZQJ36CXG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_C220_BC79 Format SD Card"}, "last_changed": "2025-08-25T10:31:44.196579+00:00", "last_reported": "2025-08-25T10:31:44.196579+00:00", "last_updated": "2025-08-25T10:31:44.196579+00:00", "context": {"id": "01K3GCHAT4WN7GCH4P9ZF9M0FS", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_C220_BC79 Manual Alarm Start"}, "last_changed": "2025-08-25T10:31:44.196813+00:00", "last_reported": "2025-08-25T10:31:44.196813+00:00", "last_updated": "2025-08-25T10:31:44.196813+00:00", "context": {"id": "01K3GCHAT4D83WZ13VXCG2KHGE", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_C220_BC79 Manual Alarm Stop"}, "last_changed": "2025-08-25T10:31:44.197054+00:00", "last_reported": "2025-08-25T10:31:44.197054+00:00", "last_updated": "2025-08-25T10:31:44.197054+00:00", "context": {"id": "01K3GCHAT514S88YM8NWAZ2CYP", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-27 01:01:30", "seconds_from_1970": 1756227690}, "dst_data": {"enabled": "1", "synced": "1", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_C220_BC79 Sync Time"}, "last_changed": "2025-08-26T04:47:21.831851+00:00", "last_reported": "2025-08-26T17:01:32.276363+00:00", "last_updated": "2025-08-26T17:01:32.276363+00:00", "context": {"id": "01K3KN7SQM9MYGX4R9FR7163KW", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_C220_BC79 Calibrate"}, "last_changed": "2025-08-26T04:47:21.831998+00:00", "last_reported": "2025-08-26T05:01:25.635589+00:00", "last_updated": "2025-08-26T04:47:21.831998+00:00", "context": {"id": "01K3JB7FQ7ECHWHRXGZCGQ97TV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_C220_BC79 Move Up"}, "last_changed": "2025-08-26T04:47:21.832082+00:00", "last_reported": "2025-08-26T05:01:25.635652+00:00", "last_updated": "2025-08-26T04:47:21.832082+00:00", "context": {"id": "01K3JB7FQ8D16TQWPG5T6BQCJN", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_C220_BC79 Move Down"}, "last_changed": "2025-08-26T04:47:21.832168+00:00", "last_reported": "2025-08-26T05:01:25.635716+00:00", "last_updated": "2025-08-26T04:47:21.832168+00:00", "context": {"id": "01K3JB7FQ8VCWHKNZ5HF8DPY75", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_C220_BC79 Move Right"}, "last_changed": "2025-08-26T04:47:21.832245+00:00", "last_reported": "2025-08-26T05:01:25.635780+00:00", "last_updated": "2025-08-26T04:47:21.832245+00:00", "context": {"id": "01K3JB7FQ8EV46QWBGESDP69YV", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_c220_bc79_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_C220_BC79 Move Left"}, "last_changed": "2025-08-26T04:47:21.832322+00:00", "last_reported": "2025-08-26T05:01:25.635835+00:00", "last_updated": "2025-08-26T04:47:21.832322+00:00", "context": {"id": "01K3JB7FQ8DS2GP8TSKFH71N0A", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.tapo_c220_bc79_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_C220_BC79 Movement Angle"}, "last_changed": "2025-08-25T10:31:44.199441+00:00", "last_reported": "2025-08-25T10:31:44.199441+00:00", "last_updated": "2025-08-25T10:31:44.199441+00:00", "context": {"id": "01K3GCHAT7X63QRB3WD6ZFMTGC", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.tapo_c220_bc79_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.3.2 Build 250610 Rel.61762n", "in_progress": false, "latest_version": "1.3.2 Build 250610 Rel.61762n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_C220_BC79 Update", "supported_features": 21}, "last_changed": "2025-08-25T10:31:44.208517+00:00", "last_reported": "2025-08-25T10:31:44.208517+00:00", "last_updated": "2025-08-25T10:31:44.208517+00:00", "context": {"id": "01K3GCHATG4G3JY33WG280RFVY", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_reboot", "state": "unknown", "attributes": {"device_class": "restart", "friendly_name": "Tapo_Camera Reboot"}, "last_changed": "2025-08-25T10:31:44.216074+00:00", "last_reported": "2025-08-25T10:31:44.216074+00:00", "last_updated": "2025-08-25T10:31:44.216074+00:00", "context": {"id": "01K3GCHATRC0PEHNKHF35X4QQ5", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_format_sd_card", "state": "unknown", "attributes": {"icon": "mdi:eraser", "friendly_name": "Tapo_Camera Format SD Card"}, "last_changed": "2025-08-25T10:31:44.216338+00:00", "last_reported": "2025-08-25T10:31:44.216338+00:00", "last_updated": "2025-08-25T10:31:44.216338+00:00", "context": {"id": "01K3GCHATRA9MNR4JH6D26CQPG", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_manual_alarm_start", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-outline", "friendly_name": "Tapo_Camera Manual Alarm Start"}, "last_changed": "2025-08-25T10:31:44.216897+00:00", "last_reported": "2025-08-25T10:31:44.216897+00:00", "last_updated": "2025-08-25T10:31:44.216897+00:00", "context": {"id": "01K3GCHATR57BYFAHQAY0HC200", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_manual_alarm_stop", "state": "unknown", "attributes": {"icon": "mdi:alarm-light-off-outline", "friendly_name": "Tapo_Camera Manual Alarm Stop"}, "last_changed": "2025-08-25T10:31:44.217177+00:00", "last_reported": "2025-08-25T10:31:44.217177+00:00", "last_updated": "2025-08-25T10:31:44.217177+00:00", "context": {"id": "01K3GCHATS80EXW3D7BX388YE3", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_sync_time", "state": "unknown", "attributes": {"clock_data": {"local_time": "2025-08-27 01:01:39", "seconds_from_1970": 1756227699}, "dst_data": {"enabled": "0", "synced": "0", "has_rule": "0"}, "icon": "mdi:timer-sync-outline", "friendly_name": "Tapo_Camera Sync Time"}, "last_changed": "2025-08-25T10:32:17.162086+00:00", "last_reported": "2025-08-26T17:01:41.604268+00:00", "last_updated": "2025-08-26T17:01:41.604268+00:00", "context": {"id": "01K3KN82V4AJDEGD4QYYZ41430", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_calibrate", "state": "unknown", "attributes": {"friendly_name": "Tapo_Camera Calibrate"}, "last_changed": "2025-08-25T10:31:44.217688+00:00", "last_reported": "2025-08-25T10:31:44.217688+00:00", "last_updated": "2025-08-25T10:31:44.217688+00:00", "context": {"id": "01K3GCHATSXVJZXQJ3NVYX3S3D", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_up", "state": "unknown", "attributes": {"icon": "mdi:arrow-up", "friendly_name": "Tapo_Camera Move Up"}, "last_changed": "2025-08-25T10:31:44.217936+00:00", "last_reported": "2025-08-25T10:31:44.217936+00:00", "last_updated": "2025-08-25T10:31:44.217936+00:00", "context": {"id": "01K3GCHATS3VHC3XCB9GNYFAJZ", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_down", "state": "unknown", "attributes": {"icon": "mdi:arrow-down", "friendly_name": "Tapo_Camera Move Down"}, "last_changed": "2025-08-25T10:31:44.218176+00:00", "last_reported": "2025-08-25T10:31:44.218176+00:00", "last_updated": "2025-08-25T10:31:44.218176+00:00", "context": {"id": "01K3GCHATTP0BDDK8X4WDH8HPD", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_right", "state": "unknown", "attributes": {"icon": "mdi:arrow-right", "friendly_name": "Tapo_Camera Move Right"}, "last_changed": "2025-08-25T10:31:44.218404+00:00", "last_reported": "2025-08-25T10:31:44.218404+00:00", "last_updated": "2025-08-25T10:31:44.218404+00:00", "context": {"id": "01K3GCHATTM9BT0BA8VBNNK0DX", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "button.tapo_camera_move_left", "state": "unknown", "attributes": {"icon": "mdi:arrow-left", "friendly_name": "Tapo_Camera Move Left"}, "last_changed": "2025-08-25T10:31:44.218634+00:00", "last_reported": "2025-08-25T10:31:44.218634+00:00", "last_updated": "2025-08-25T10:31:44.218634+00:00", "context": {"id": "01K3GCHATTZCQ7EHEA35BC3W3X", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "number.tapo_camera_movement_angle", "state": "15", "attributes": {"min": 5, "max": 120, "step": 5, "mode": "auto", "icon": "mdi:map-marker-distance", "friendly_name": "Tapo_Camera Movement Angle"}, "last_changed": "2025-08-25T10:31:44.219580+00:00", "last_reported": "2025-08-25T10:31:44.219580+00:00", "last_updated": "2025-08-25T10:31:44.219580+00:00", "context": {"id": "01K3GCHATV5K6DP2PRD00TE4G5", "parent_id": null, "user_id": null}}, "extra_data": {"native_max_value": 120, "native_min_value": 5, "native_step": 5, "native_unit_of_measurement": null, "native_value": 15}, "last_seen": "2025-08-26T17:02:03.550549+00:00"}, {"state": {"entity_id": "update.tapo_camera_update", "state": "off", "attributes": {"auto_update": false, "display_precision": 0, "installed_version": "1.4.7 Build 250625 Rel.58841n", "in_progress": false, "latest_version": "1.4.7 Build 250625 Rel.58841n", "release_summary": null, "release_url": null, "skipped_version": null, "title": null, "update_percentage": null, "entity_picture": "https://brands.home-assistant.io/_/tapo_control/icon.png", "friendly_name": "Tapo_Camera Update", "supported_features": 21}, "last_changed": "2025-08-25T10:31:44.226190+00:00", "last_reported": "2025-08-25T10:31:44.226190+00:00", "last_updated": "2025-08-25T10:31:44.226190+00:00", "context": {"id": "01K3GCHAV2PX1AZ8MT14WZFF2H", "parent_id": null, "user_id": null}}, "extra_data": null, "last_seen": "2025-08-26T17:02:03.550549+00:00"}]}