{"version": 1, "minor_version": 1, "key": "lovelace.dashboard_home", "data": {"config": {"views": [{"title": "Home", "sections": [{"type": "grid", "cards": [{"type": "custom:bubble-card", "card_type": "button", "button_type": "name", "entity": "sensor.climate_h_91e0ec_temperature_indoor", "show_attribute": false, "show_name": true, "show_icon": true, "scrolling_effect": true, "show_state": true, "card_layout": "large-2-rows", "name": "Living Room", "icon": "mdi:television", "sub_button": [{"entity": "climate.climate_h_91e0ec_climate", "show_last_changed": false, "show_attribute": false, "show_state": false, "tap_action": {"action": "more-info"}, "show_background": true, "state_background": true, "show_icon": true, "icon": ""}, {"entity": "light.downlight", "show_last_changed": false, "show_attribute": false, "show_state": false, "tap_action": {"action": "toggle"}, "show_background": true, "hold_action": {"action": "more-info"}, "icon": "mdi:light-recessed"}, {"entity": "light.ceiling", "show_last_changed": false, "show_attribute": false, "show_state": false, "tap_action": {"action": "toggle"}, "show_background": true, "hold_action": {"action": "more-info"}, "icon": "mdi:ceiling-light"}], "styles": ":host{\n    --icon-Background-Color: #FF595E;\n    --color-yellow: 255, 202, 58 ;\n    --color-blue: 25, 130, 196 ;\n    --color-green: 138, 201, 38 ;\n    --color-purple: 106, 76, 147 ;\n}", "modules": ["default", "room_cards_styling"], "button_action": {"tap_action": {"action": "navigate", "navigation_path": "#living_room"}}, "tap_action": {"action": "navigate", "navigation_path": "#living_room"}, "grid_options": {"columns": 6, "rows": 3}}, {"type": "custom:bubble-card", "card_type": "button", "button_type": "name", "show_attribute": false, "show_name": true, "show_icon": true, "scrolling_effect": true, "show_state": true, "card_layout": "large-2-rows", "name": "Kitchen", "icon": "mdi:fridge", "sub_button": [{"entity": "light.dining", "icon": "mdi:white-balance-sunny"}, {"entity": "light.dining", "show_last_changed": false, "show_attribute": false, "show_state": false, "tap_action": {"action": "toggle"}, "show_background": true, "icon": "mdi:light-recessed"}, {"entity": "switch.bar", "icon": "mdi:glass-wine"}], "styles": ":host{\n    --icon-Background-Color: #FF595E;\n    --color-yellow: 255, 202, 58 ;\n    --color-blue: 25, 130, 196 ;\n    --color-green: 138, 201, 38 ;\n    --color-purple: 106, 76, 147 ;\n}", "modules": ["default", "room_cards_styling"], "button_action": {"tap_action": {"action": "navigate", "navigation_path": "#kitchen"}}, "tap_action": {"action": "navigate", "navigation_path": "#kitchen"}, "grid_options": {"rows": 3, "columns": 6}}, {"type": "custom:bubble-card", "card_type": "button", "button_type": "name", "show_attribute": false, "show_name": true, "show_icon": true, "scrolling_effect": true, "show_state": true, "card_layout": "large-2-rows", "name": "Studio", "icon": "mdi:desktop-classic", "sub_button": [{"entity": "climate.cs_ux36ba2"}, {"entity": "light.studio", "show_state": false, "show_attribute": false, "show_last_updated": false}], "styles": ":host{\n    --icon-Background-Color: #FF595E;\n    --color-yellow: 255, 202, 58 ;\n    --color-blue: 25, 130, 196 ;\n    --color-green: 138, 201, 38 ;\n    --color-purple: 106, 76, 147 ;\n}", "modules": ["default", "room_cards_styling"], "button_action": {"tap_action": {"action": "navigate", "navigation_path": "#kitchen"}}, "tap_action": {"action": "navigate", "navigation_path": "#studio"}, "grid_options": {"rows": 3, "columns": 6}, "entity": "climate.cs_ux36ba2"}, {"type": "custom:bubble-card", "card_type": "button", "button_type": "name", "show_attribute": false, "show_name": true, "show_icon": true, "scrolling_effect": true, "show_state": true, "card_layout": "large-2-rows", "name": "Bedroom", "icon": "mdi:bed", "sub_button": [{"entity": "switch.bedroom_wall", "icon": "mdi:light-recessed"}], "styles": ":host{\n    --icon-Background-Color: #FF595E;\n    --color-yellow: 255, 202, 58 ;\n    --color-blue: 25, 130, 196 ;\n    --color-green: 138, 201, 38 ;\n    --color-purple: 106, 76, 147 ;\n}", "modules": ["default", "room_cards_styling"], "button_action": {"tap_action": {"action": "navigate", "navigation_path": "#kitchen"}}, "tap_action": {"action": "navigate", "navigation_path": "#bedroom"}, "grid_options": {"rows": 3, "columns": 6}}]}, {"type": "grid", "cards": [{"type": "vertical-stack", "cards": [{"type": "custom:bubble-card", "card_type": "pop-up", "hash": "#living_room", "name": "Living Room", "icon": "mdi:sofa", "entity": "climate.climate_h_991efc_climate", "show_state": true, "show_attribute": false, "attribute": "target_temp_step"}, {"type": "custom:bubble-card", "card_type": "separator", "name": "Lampes", "icon": "mdi:lightbulb"}, {"type": "vertical-stack", "cards": [{"type": "horizontal-stack", "cards": [{"type": "custom:bubble-card", "card_type": "button", "entity": "light.downlight", "use_accent_color": false, "button_type": "slider"}, {"type": "custom:bubble-card", "card_type": "button", "entity": "light.ceiling", "card_layout": "normal", "use_accent_color": false, "show_state": false, "show_last_changed": false, "scrolling_effect": true, "show_icon": true, "force_icon": false, "button_type": "slider"}]}]}, {"type": "custom:bubble-card", "card_type": "separator", "name": "Vacuum", "icon": "mdi:vacuum"}, {"type": "custom:bubble-card", "card_type": "button", "entity": "vacuum.s8_pro_ultra", "show_state": true, "show_last_changed": true, "button_action": {"tap_action": {"action": "more-info"}}, "sub_button": [{"entity": "vacuum.s8_pro_ultra", "show_attribute": true, "attribute": "battery_level", "icon": "mdi:battery", "state_background": false, "show_background": false}, {"entity": "vacuum.s8_pro_ultra", "tap_action": {"action": "perform-action", "perform_action": "vacuum.return_to_base", "target": {"device_id": "8dad38d0a2498b75f53e58220e1f1116"}, "data": {}}, "icon": "mdi:home", "show_background": false}, {"entity": "vacuum.s8_pro_ultra", "icon": "mdi:pause", "show_background": false, "show_attribute": false, "tap_action": {"action": "perform-action", "target": {"device_id": "8dad38d0a2498b75f53e58220e1f1116"}, "data": {}, "perform_action": "vacuum.pause"}}, {"entity": "vacuum.s8_pro_ultra", "show_background": true, "icon": "mdi:play", "tap_action": {"action": "perform-action", "perform_action": "vacuum.start", "target": {}}}], "styles": ".bubble-button-card-container {\n  /* Change the background color when the vacuum get an error (optional), more details in the styles template section */\n  background: ${state === 'error' ? 'rgb(200, 80, 40)' : ''} !important;\n}\n/* Change the first sub-button battery icon based on the battery_icon attribute, more details in the styles template section */\n${subButtonIcon[0].setAttribute(\"icon\", hass.states['vacuum.s8_pro_ultra'].attributes.battery_icon)}"}, {"type": "custom:bubble-card", "card_type": "separator", "name": "Media", "icon": "mdi:television"}, {"type": "vertical-stack", "cards": [{"type": "custom:bubble-card", "card_type": "media-player", "entity": "media_player.apple_tv_4k"}, {"type": "custom:bubble-card", "card_type": "media-player", "entity": "media_player.sonos_one"}, {"type": "custom:bubble-card", "card_type": "media-player", "entity": "media_player.unnamed_room"}, {"type": "custom:bubble-card", "card_type": "media-player", "entity": "media_player.ke_ting"}]}, {"type": "custom:bubble-card", "card_type": "separator", "name": "Climates", "icon": "mdi:thermometer"}, {"type": "vertical-stack", "cards": [{"type": "custom:bubble-card", "card_type": "climate", "entity": "climate.climate_h_91e0ec_climate", "sub_button": []}]}]}, {"type": "vertical-stack", "cards": [{"type": "custom:bubble-card", "card_type": "pop-up", "hash": "#kitchen", "name": "Kitchen", "icon": "mdi:fridge"}, {"type": "custom:bubble-card", "card_type": "separator", "name": "Lampes", "icon": "mdi:lightbulb"}, {"type": "horizontal-stack", "cards": [{"type": "custom:bubble-card", "card_type": "button", "button_type": "slider", "entity": "light.kitchen"}, {"type": "custom:bubble-card", "card_type": "button", "entity": "switch.bar", "button_type": "switch", "icon": "mdi:glass-cocktail"}]}]}, {"type": "vertical-stack", "cards": [{"type": "custom:bubble-card", "card_type": "pop-up", "hash": "#bedroom", "name": "Bedroom", "entity": "light.bedroom_wall", "button_type": "switch", "show_last_changed": true}, {"type": "custom:bubble-card", "card_type": "climate", "entity": "climate.a1n_tw_nha0167a", "sub_button": [{"name": "HVAC modes menu", "select_attribute": "hvac_modes", "state_background": false, "show_arrow": false}]}]}]}], "badges": [{"type": "entity", "show_name": true, "show_state": true, "show_icon": true, "entity": "person.karl_li"}, {"type": "entity", "show_name": true, "show_state": true, "show_icon": true, "entity": "person.phibie"}, {"type": "entity", "show_name": true, "show_state": true, "show_icon": true, "entity": "binary_sensor.mailbox"}, {"type": "entity", "show_name": true, "show_state": true, "show_icon": true, "entity": "sensor.lao_ya_hao_arrival_time"}], "header": {"layout": "center", "badges_position": "bottom"}}]}}}