{"version": 1, "minor_version": 5, "key": "core.config_entries", "data": {"entries": [{"created_at": "2025-02-21T18:08:08.186178+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "sun", "entry_id": "01JMMV41ZT20GXGE71FM3YDD1K", "minor_version": 1, "modified_at": "2025-02-21T18:08:08.186188+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Sun", "unique_id": null, "version": 1}, {"created_at": "2025-02-21T18:08:10.801557+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "go2rtc", "entry_id": "01JMMV44HHTRFVKB5TWZR050SG", "minor_version": 1, "modified_at": "2025-02-21T18:08:10.801563+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "system", "subentries": [], "title": "go2rtc", "unique_id": null, "version": 1}, {"created_at": "2025-02-21T18:08:17.841742+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_38420B56FCB401400@起居室._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_804AF24341F601400@Sonos Beam._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_7828CA06C23E01400@Living Room._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_38420B56FCB401400@Living Room._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_804AF24341F601400@Living Room._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_7828CA06C23E01400@客廳._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_804AF24341F601400@客廳._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_38420B56FCB401400@客廳._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_38420B56FCB401400@Sonos Beam._sonos._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_sonos._tcp.local.", "RINCON_7828CA06C23E01400@Sonos Beam._sonos._tcp.local."], "version": 1}]}, "domain": "sonos", "entry_id": "01JMMV4BDHQM8D07E8FB4XW83N", "minor_version": 1, "modified_at": "2025-08-04T03:58:39.124936+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Sonos", "unique_id": "sonos", "version": 1}, {"created_at": "2025-02-22T03:12:28.620779+00:00", "data": {"track_home": true}, "disabled_by": null, "discovery_keys": {}, "domain": "met", "entry_id": "01JMNT8RYCD1W54KZNXHSKAB5M", "minor_version": 1, "modified_at": "2025-02-22T03:12:28.620790+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "onboarding", "subentries": [], "title": "Home", "unique_id": null, "version": 1}, {"created_at": "2025-02-22T03:14:14.075129+00:00", "data": {"host": "*************", "mac": ["00-11-32-F7-CD-C7", "00-11-32-F7-CD-C8"], "password": "LQVxxfVXRK3dkNAh", "port": 5000, "ssl": false, "username": "killtw", "verify_ssl": false}, "disabled_by": null, "discovery_keys": {"ssdp": [{"domain": "ssdp", "key": "uuid:73796E6F-6473-6D00-0000-001132f7cdc7", "version": 1}]}, "domain": "synology_dsm", "entry_id": "01JMNTBZXVE854PH4CQV7G1AX5", "minor_version": 1, "modified_at": "2025-03-14T04:04:38.525236+00:00", "options": {"backup_path": "ha_backup_home", "backup_share": "/home", "snap_profile_type": 1}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ssdp", "subentries": [], "title": "DSM", "unique_id": "2150SBR8MJ728", "version": 1}, {"created_at": "2025-02-22T04:09:03.172221+00:00", "data": {"token": "****************************************"}, "disabled_by": null, "discovery_keys": {}, "domain": "hacs", "entry_id": "01JMNXGBY4P6VCMHFQ3A1EHDSF", "minor_version": 1, "modified_at": "2025-02-22T04:09:03.172228+00:00", "options": {"experimental": true}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "", "unique_id": null, "version": 1}, {"created_at": "2025-02-22T08:21:52.482353+00:00", "data": {"app_data": {"push_token": "dJH5tk9p6UX_nSDcP2KVej:APA91bELG37YVN9j7hEzC0qxLbB9sZlgqjafnbaA6rP7Jb89O7S3esf0xtXnlA7fOqgT5gF14NY-U-n7fUodNVU4FnE7SUOmlMNuWoVCNiSnYeSgI705Yzs", "push_url": "https://mobile-apps.home-assistant.io/api/sendPushNotification"}, "app_id": "io.robbie.HomeAssistant", "app_name": "Home Assistant", "app_version": "2025.7.2 (2025.1356)", "device_id": "22FAF99C-5EC1-4491-BE3B-4285ADF6F7F2", "device_name": "<PERSON><PERSON>s iPhone 16 Pro", "manufacturer": "Apple", "model": "iPhone17,1", "no_legacy_encryption": true, "os_name": "iOS", "os_version": "18.6.2", "secret": "2e099230c2501a555869d582d63bf32bfca1c65621e425157c8df6b2537a607f", "supports_encryption": true, "user_id": "9b1cbb01e5804f8d8d27eeb071638aab", "webhook_id": "4a63a04ca3a3598b57f0d9388366895634aa9afba4c8ca253ff096c33120711e"}, "disabled_by": null, "discovery_keys": {}, "domain": "mobile_app", "entry_id": "01JMPBZ9Q234X0N4FJ20CS3Y9C", "minor_version": 1, "modified_at": "2025-08-23T20:20:37.521307+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "registration", "subentries": [], "title": "<PERSON><PERSON>s iPhone 16 Pro", "unique_id": "io.robbie.HomeAssistant-22FAF99C-5EC1-4491-BE3B-4285ADF6F7F2", "version": 1}, {"created_at": "2025-02-22T17:18:35.776525+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_meshcop._udp.local.", "Aqara BorderRouter #976F._meshcop._udp.local."], "version": 1}]}, "domain": "thread", "entry_id": "01JMQAP280J79FBCXF4K1PJBY2", "minor_version": 1, "modified_at": "2025-02-22T17:18:35.776536+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "<PERSON><PERSON><PERSON>", "unique_id": null, "version": 1}, {"created_at": "2025-02-24T10:25:46.036633+00:00", "data": {"host": "*************", "mac": "8c:ea:48:6a:97:5c", "manufacturer": "Samsung", "method": "websocket", "model": "QA65Q70TAWXZW", "name": "Samsung Q70 Series (65)", "port": 8002, "ssdp_main_tv_agent_location": null, "ssdp_rendering_control_location": "http://*************:9197/dmr", "token": "24734178"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "8cea486a975c", "version": 1}], "ssdp": [{"domain": "ssdp", "key": "uuid:fc19a986-2249-4793-93cd-dc0d5b3625bb", "version": 1}], "zeroconf": [{"domain": "zeroconf", "key": ["_airplay._tcp.local.", "Samsung Q70 Series (65)._airplay._tcp.local."], "version": 1}]}, "domain": "samsungtv", "entry_id": "01JMVQVK1MHFN095DDPB261EDG", "minor_version": 2, "modified_at": "2025-08-19T04:24:46.744342+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "Samsung Q70 Series (65) (QA65Q70TAWXZW)", "unique_id": "0ee3f5b9-6802-43ec-851b-31445b32ec72", "version": 2}, {"created_at": "2025-02-28T04:14:52.690090+00:00", "data": {"client_id": "761a70801c556d8d618eb7b940de6cf78661fac0b9907bc79ac1219f59402158", "language": "en-TW", "oauth2_url": "https://kr.lgeapi.com/", "region": "TW", "token": "bbcf1efa7ee21cf83ae4c509d9a623ab5c7bf77eddcc5cfb01852a422c1822fdb14970198ef881f9506475f1178c8080", "use_api_v2": true}, "disabled_by": null, "discovery_keys": {}, "domain": "smartthinq_sensors", "entry_id": "01JN5C7B8J158YAXXKZPBQ2M20", "minor_version": 1, "modified_at": "2025-02-28T04:14:52.690102+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "LGE Devices", "unique_id": null, "version": 1}, {"created_at": "2025-02-28T18:13:05.007155+00:00", "data": {"base_url": "https://usiot.roborock.com", "user_data": {"avatarurl": "https://files.roborock.com/iottest/default_avatar.png", "country": "TW", "countrycode": "886", "nickname": "Xhv2391657", "region": "us", "rriot": {"h": "ygQcBAjBPl", "k": "rX4LvQET", "r": {"a": "https://api-us.roborock.com", "l": "https://wood-us.roborock.com", "m": "ssl://mqtt-us-2.roborock.com:8883", "r": "US"}, "s": "8tEz2v", "u": "5oS0WYyYXd8ctF3nyNCDxk"}, "rruid": "rr6313ec8c739830", "token": "b36d179a05c44a619822564e0e0ba473-NrijmYV1A/gO45/yFhwOeA==", "tokentype": "", "tuyaDeviceState": 0, "uid": 1894349}, "username": "<EMAIL>"}, "disabled_by": null, "discovery_keys": {}, "domain": "roborock", "entry_id": "01JN6W64VFF71GFGRK5RRY3SH2", "minor_version": 2, "modified_at": "2025-05-08T03:37:54.782760+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "<EMAIL>", "unique_id": "rr6313ec8c739830", "version": 1}, {"created_at": "2025-03-02T08:54:06.162511+00:00", "data": {"auth": {"account": "41733f47-c425-4dfb-9d97-0688be57a71c", "token": "4371E55A3FA0B803E41424FE42416F14942372936C0389925580052EDCA5A8FF-1", "tokenType": "Bearer"}, "region": "TW"}, "disabled_by": null, "discovery_keys": {}, "domain": "dyson_local", "entry_id": "01JNB1022J26CYHKSHQTGWP7PW", "minor_version": 1, "modified_at": "2025-03-02T08:54:06.162522+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "MyDyson: <EMAIL> (TW)", "unique_id": "<EMAIL>", "version": 1}, {"created_at": "2025-03-02T08:54:53.545794+00:00", "data": {"credential": "K78FJ1ggOW7ybq/Dg2witIGdhR76mqlf8kobleGWRjYqEMq1nlhv1D2MpzVd6Y4TN8jdvjkrSFqnY3qaUZprsg==", "device_type": "527", "host": "", "name": "A1N-TW-NHA0167A", "serial": "A1N-TW-NHA0167A"}, "disabled_by": null, "discovery_keys": {}, "domain": "dyson_local", "entry_id": "01JNB11GB9FM7DH4XM5WPF3H81", "minor_version": 1, "modified_at": "2025-03-02T08:54:53.545817+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "discovery", "subentries": [], "title": "A1N-TW-NHA0167A", "unique_id": "A1N-TW-NHA0167A", "version": 1}, {"created_at": "2025-03-03T07:42:18.321598+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "group", "entry_id": "01JNDF9A6H0F4806K2EK0KBG3H", "minor_version": 1, "modified_at": "2025-03-12T01:42:32.217457+00:00", "options": {"all": false, "entities": ["light.qk_cblc5_left", "light.qk_cblc5_right"], "group_type": "light", "hide_members": true, "name": "晴空燈"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "晴空燈", "unique_id": null, "version": 1}, {"created_at": "2025-03-03T17:10:20.173898+00:00", "data": {"name": "Garage Bridge", "port": 21063}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNEFSD8DD5F66YQ0NH3ACJ8T", "minor_version": 1, "modified_at": "2025-05-29T11:31:51.779035+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["cover.garage", "switch.garage", "switch.garage_washer", "binary_sensor.garage_side_door"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Garage Bridge:21063", "unique_id": null, "version": 1}, {"created_at": "2025-03-03T17:25:58.602857+00:00", "data": {"name": "Living Room Bridge", "port": 21064}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNEGP1PAKQY9BDABX5QBRJY4", "minor_version": 1, "modified_at": "2025-06-09T15:35:11.458498+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["climate.climate_h_91e0ec_climate", "light.ceiling", "light.downlight"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Living Room Bridge:21064", "unique_id": null, "version": 1}, {"created_at": "2025-03-03T17:25:58.603157+00:00", "data": {"name": "Kitchen Bridge", "port": 21065}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNEGP1PBGC9JK41DQP1ZERZY", "minor_version": 1, "modified_at": "2025-05-29T11:31:51.779159+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["light.dining", "switch.bar", "light.kitchen"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Kitchen Bridge:21065", "unique_id": null, "version": 1}, {"created_at": "2025-03-04T03:19:53.648653+00:00", "data": {"name": "Balcony Bridge", "port": 21066}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNFJNHHGHGGMYY9AKP0JMRFW", "minor_version": 1, "modified_at": "2025-05-29T11:31:51.779181+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["switch.balcony"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Balcony Bridge:21066", "unique_id": null, "version": 1}, {"created_at": "2025-03-05T09:17:42.240218+00:00", "data": {"name": "Bedroom Bridge", "port": 21067}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNJSHDZ0HGGMYY9AKP0JMRFW", "minor_version": 1, "modified_at": "2025-05-29T18:27:58.337211+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["climate.a1n_tw_nha0167a", "fan.a1n_tw_nha0167a", "switch.bedroom_wall", "switch.4f_balcony", "switch.pear", "switch.candle"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Bedroom Bridge:21067", "unique_id": null, "version": 1}, {"created_at": "2025-03-05T09:17:42.240481+00:00", "data": {"name": "Studio Bridge", "port": 21068}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNJSHDZ0D5F66YQ0NH3ACJ8T", "minor_version": 1, "modified_at": "2025-05-30T05:30:22.800808+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["switch.studio", "climate.cs_ux36ba2"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Studio Bridge:21068", "unique_id": null, "version": 1}, {"created_at": "2025-03-05T09:17:42.240675+00:00", "data": {"name": "Dress Room Bridge", "port": 21069}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNJSHDZ019VH2CCE3CF7V55K", "minor_version": 1, "modified_at": "2025-05-30T05:30:22.800859+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["switch.dress_room", "humidifier.md171qsk3"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Dress Room Bridge:21069", "unique_id": null, "version": 1}, {"created_at": "2025-03-05T09:17:42.240834+00:00", "data": {"name": "Stairs Bridge", "port": 21071}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JNJSHDZ0S5HFNA1VR1BF41VJ", "minor_version": 1, "modified_at": "2025-05-30T05:30:22.800895+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["switch.door", "switch.entrance", "switch.map", "switch.wall_lamps", "switch.2f_stairs", "switch.2f_landing", "switch.3f_stairs", "switch.3f_landing"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Stairs Bridge:21071", "unique_id": null, "version": 1}, {"created_at": "2025-03-07T08:58:00.315260+00:00", "data": {"endpoint": "https://apigw.tuyaus.com", "terminal_id": "1745214301225wMxdh0", "token_info": {"access_token": "52ae98a4862061a5b859a65fbc2ceb42", "expire_time": 7200, "refresh_token": "3dcde899d59bb10865556c4dd165a486", "t": 1756225019646, "uid": "az1669742421939sI3GU"}, "user_code": "Bxsl3GU"}, "disabled_by": null, "discovery_keys": {}, "domain": "tuya", "entry_id": "01JNQX6SQV4H932G788M8N2S7Q", "minor_version": 1, "modified_at": "2025-08-26T16:16:59.731177+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "ap-000815.5cb7ee80d8e14f0497ec4ba1a000b93a.0120", "unique_id": null, "version": 1}, {"created_at": "2025-03-07T14:02:28.495313+00:00", "data": {"address": "************", "credentials": {"3": "334f96a11fd38d37203330562f9493d317994c9f5b237cecf7c50ef75711059f:ce36cf707eb8355686faa8e4733c744dc2c5ca5dfd559e1ab398a26ade696e52:39383839383538452d313043342d344135462d423837442d314637444330303135423842:65333761626438312d393864652d343139312d623837632d333134313631643537666635", "4": "334f96a11fd38d37203330562f9493d317994c9f5b237cecf7c50ef75711059f:3527296994715d475ac9ba1244015a62189e66d5c4617063e55f3d31e993cbb2:39383839383538452d313043342d344135462d423837442d314637444330303135423842:34333937326366612d643237362d343765382d383162312d663836333064616661613735", "5": "334f96a11fd38d37203330562f9493d317994c9f5b237cecf7c50ef75711059f:aed2e6e9c1c81bf773a9392093e81a52461e99242eb2101836faf88749d14bd2:39383839383538452d313043342d344135462d423837442d314637444330303135423842:64376636363661342d393865642d343530342d626237352d363363393132643338346137"}, "identifiers": ["A8:51:AB:D0:B3:83", "9889858E-10C4-4A5F-B87D-1F7DC0015B8B", "A851ABD0B383"], "name": "Apple TV 4K"}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_airplay._tcp.local.", "Apple TV 4K._airplay._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_raop._tcp.local.", "A851ABD0B383@Apple TV 4K._raop._tcp.local."], "version": 1}]}, "domain": "apple_tv", "entry_id": "01JNREM9RFVV4ZR3Y7TRRKFVSA", "minor_version": 1, "modified_at": "2025-03-07T14:03:03.522805+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Apple TV 4K", "unique_id": "A8:51:AB:D0:B3:83", "version": 1}, {"created_at": "2025-03-07T14:09:17.395522+00:00", "data": {"address": "*************", "credentials": {"3": null, "5": null}, "identifiers": ["7E:D8:E1:F6:4E:85", "7ED8E1F64E85"], "name": "客廳"}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_airplay._tcp.local.", "客廳._airplay._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_raop._tcp.local.", "7ED8E1F64E85@客廳._raop._tcp.local."], "version": 1}]}, "domain": "apple_tv", "entry_id": "01JNRF0S2KD83Y8TSD6HVBGYNC", "minor_version": 1, "modified_at": "2025-03-07T14:16:49.759725+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "客廳", "unique_id": "7E:D8:E1:F6:4E:85", "version": 1}, {"created_at": "2025-03-12T01:39:49.801375+00:00", "data": {"device_id": "eb402982bd9005e9edelog", "host": "************", "local_key": "2*0pOz:4?^L{G?'R", "poll_only": false, "protocol_version": "auto", "type": "rgbcw_lightbulb"}, "disabled_by": null, "discovery_keys": {}, "domain": "tuya_local", "entry_id": "01JP4042D95AJFJW4N4QTHH1V2", "minor_version": 9, "modified_at": "2025-05-26T07:41:26.258586+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "QK-CBLC5 Left", "unique_id": null, "version": 13}, {"created_at": "2025-03-12T01:42:09.848179+00:00", "data": {"device_id": "eb472ce7e2274fc6befaei", "host": "************", "local_key": "U|0jQuc|Bp[^{qO;", "poll_only": false, "protocol_version": "auto", "type": "rgbcw_lightbulb"}, "disabled_by": null, "discovery_keys": {}, "domain": "tuya_local", "entry_id": "01JP408B5RE83QQS8M22MTJC7A", "minor_version": 9, "modified_at": "2025-05-26T07:41:26.263373+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "QK-CBLC5 Right", "unique_id": null, "version": 13}, {"created_at": "2025-03-12T02:39:17.488385+00:00", "data": {"name": "QK-CBLC5"}, "disabled_by": null, "discovery_keys": {}, "domain": "adaptive_lighting", "entry_id": "01JP43GYFG9Q6HNEX1HJPXW3DX", "minor_version": 1, "modified_at": "2025-04-16T16:46:38.723124+00:00", "options": {"adapt_delay": 0.0, "adapt_only_on_bare_turn_on": false, "autoreset_control_seconds": 0, "brightness_mode": "default", "brightness_mode_time_dark": 900, "brightness_mode_time_light": 3600, "detect_non_ha_changes": false, "include_config_in_attributes": false, "initial_transition": 1.0, "intercept": true, "interval": 90, "lights": ["light.qk_cblc5_left", "light.qk_cblc5_right"], "max_brightness": 100, "max_color_temp": 5500, "max_sunrise_time": "None", "max_sunset_time": "None", "min_brightness": 15, "min_color_temp": 2000, "min_sunrise_time": "None", "min_sunset_time": "None", "multi_light_intercept": true, "only_once": false, "prefer_rgb_color": false, "send_split_delay": 0, "separate_turn_on_commands": false, "skip_redundant_commands": false, "sleep_brightness": 1, "sleep_color_temp": 1000, "sleep_rgb_color": [255, 56, 0], "sleep_rgb_or_color_temp": "color_temp", "sleep_transition": 1.0, "sunrise_offset": 0, "sunrise_time": "None", "sunset_offset": 0, "sunset_time": "None", "take_over_control": true, "transition": 45.0, "transition_until_sleep": false}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "QK-CBLC5", "unique_id": "QK-CBLC5", "version": 1}, {"created_at": "2025-03-13T02:00:58.649266+00:00", "data": {"host": "*************", "mac": "00:7c:2d:f7:ca:e8", "manufacturer": "Samsung", "method": "websocket", "model": "UA55RU7400WXZW", "name": "Samsung 7 Series (55)", "port": 8002, "ssdp_main_tv_agent_location": null, "ssdp_rendering_control_location": "http://*************:9197/dmr", "token": "16267204"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "007c2df7cae8", "version": 1}], "ssdp": [{"domain": "ssdp", "key": "uuid:908fb668-9834-40bf-b602-8369e69f89c2", "version": 1}], "zeroconf": [{"domain": "zeroconf", "key": ["_airplay._tcp.local.", "Samsung 7 Series (55)._airplay._tcp.local."], "version": 1}]}, "domain": "samsungtv", "entry_id": "01JP6KQGGSMS0THTR2GQQWMGHP", "minor_version": 2, "modified_at": "2025-07-04T16:21:16.724193+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Samsung 7 Series (55) (UA55RU7400WXZW)", "unique_id": "75b69464-8987-48bc-a0ad-ff1abea87d6d", "version": 2}, {"created_at": "2025-03-13T15:16:39.022439+00:00", "data": {"broker": "*************", "port": 1883}, "disabled_by": null, "discovery_keys": {}, "domain": "mqtt", "entry_id": "01JP818DZESEH4JW2TENGNAZWY", "minor_version": 2, "modified_at": "2025-03-13T15:16:39.022447+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "*************", "unique_id": null, "version": 1}, {"created_at": "2025-03-13T15:34:40.390420+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "template", "entry_id": "01JP829E06NM13M3C739BJNZE7", "minor_version": 1, "modified_at": "2025-03-19T02:51:53.378280+00:00", "options": {"name": "Mailbox", "state": "{{ is_state('input_boolean.mailbox', 'on') }}", "template_type": "binary_sensor"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "Mailbox", "unique_id": null, "version": 1}, {"created_at": "2025-03-16T05:42:18.567157+00:00", "data": {"name": "Restroom Bridge", "port": 21072}, "disabled_by": null, "discovery_keys": {}, "domain": "homekit", "entry_id": "01JPEQJY67HJZP0MR3VAGMJ5GB", "minor_version": 1, "modified_at": "2025-05-30T05:30:22.800929+00:00", "options": {"entity_config": {}, "filter": {"exclude_domains": [], "exclude_entities": [], "exclude_entity_globs": [], "include_domains": [], "include_entities": ["switch.2f_restroom", "switch.3f_bathroom", "switch.4f_bathroom"], "include_entity_globs": []}, "mode": "bridge"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "import", "subentries": [], "title": "Restroom Bridge:21072", "unique_id": null, "version": 1}, {"created_at": "2025-03-18T08:22:40.101643+00:00", "data": {"device_id": "uuid:fc19a986-2249-4793-93cd-dc0d5b3625bb", "mac": "8c:ea:48:6a:97:5c", "type": "urn:schemas-upnp-org:device:MediaRenderer:1", "url": "http://*************:9197/dmr"}, "disabled_by": null, "discovery_keys": {"ssdp": [{"domain": "ssdp", "key": "uuid:fc19a986-2249-4793-93cd-dc0d5b3625bb", "version": 1}]}, "domain": "dlna_dmr", "entry_id": "01JPM5J075MX81CP4EW86P05HR", "minor_version": 1, "modified_at": "2025-03-18T08:22:40.101650+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "[TV] Samsung Q70 Series (65)", "unique_id": "uuid:fc19a986-2249-4793-93cd-dc0d5b3625bb", "version": 1}, {"created_at": "2025-03-20T04:37:32.828773+00:00", "data": {"device_type": "A1MINI", "serial": "0309CA4C1901915"}, "disabled_by": null, "discovery_keys": {}, "domain": "bambu_lab", "entry_id": "01JPRXF7GWYH8QBAFVJMFZN7YJ", "minor_version": 1, "modified_at": "2025-08-24T12:43:48.219697+00:00", "options": {"access_code": "14372710", "auth_token": "AACFvA8yLxkVzlqzXcWALqAl2e1G0DEMJ1wGpcn0cmQe2CcrbnpO4_5qrtysRI7Hd8Ke4WTz3LksbYq1w2KytDRSbbweT37u-fA35EOIvqb2q5angjmYC8yDlBF9nmLKjvIF7R5d7eVWMjKF", "disable_ssl_verify": false, "email": "<EMAIL>", "enable_firmware_update": false, "host": "*************", "local_mqtt": false, "name": "3DP-030-915", "region": "AsiaPacific", "usage_hours": 331.2099999999999, "username": "u_2979673500"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "0309CA4C1901915", "unique_id": null, "version": 2}, {"created_at": "2025-03-23T08:44:05.774945+00:00", "data": {"device_name": "climate-h-91e0ec", "host": "*************", "noise_psk": "", "password": "", "port": 6053}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "188b0e91e0ec", "version": 1}], "zeroconf": [{"domain": "zeroconf", "key": ["_esphomelib._tcp.local.", "climate-h-91e0ec._esphomelib._tcp.local."], "version": 1}]}, "domain": "esphome", "entry_id": "01JQ12RTREKYB5<PERSON>B21HTTYREP", "minor_version": 1, "modified_at": "2025-03-24T08:01:41.466842+00:00", "options": {"allow_service_calls": false}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Climate-H 91e0ec", "unique_id": "18:8b:0e:91:e0:ec", "version": 1}, {"created_at": "2025-03-31T10:16:25.088846+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Studio C1CE._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQNV7M80KJQG3G4NKTEADCZA", "minor_version": 1, "modified_at": "2025-03-31T10:16:25.088854+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Studio C1CE (Ip Camera)", "unique_id": "24:2d:bb:3b:ae:40", "version": 1}, {"created_at": "2025-03-31T10:16:26.902556+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Balcony 82BC._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQNV7P0P7G57F3BFM2G7Z69K", "minor_version": 1, "modified_at": "2025-03-31T10:16:26.902563+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Balcony 82BC (Ip Camera)", "unique_id": "25:12:3c:97:f5:bd", "version": 1}, {"created_at": "2025-03-31T10:16:28.568062+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Bedroom D407._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQNV7QMR8523YJTPDTD8TWQA", "minor_version": 1, "modified_at": "2025-03-31T10:16:28.568069+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Bedroom D407 (Ip Camera)", "unique_id": "88:ca:1e:ea:f2:86", "version": 1}, {"created_at": "2025-03-31T10:16:30.320428+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Kitchen 545D._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQNV7SBG27TXMP9W3S7YEH98", "minor_version": 1, "modified_at": "2025-03-31T10:16:30.320435+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Kitchen 545D (Ip Camera)", "unique_id": "ec:ff:44:f9:3e:13", "version": 1}, {"created_at": "2025-03-31T10:16:32.685115+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Living Room F22E._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQNV7VNDHQK8CPACCNAPWMVV", "minor_version": 1, "modified_at": "2025-03-31T10:16:32.685123+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Living Room F22E (Ip Camera)", "unique_id": "92:38:32:9f:c0:6a", "version": 1}, {"created_at": "2025-04-02T06:02:27.347371+00:00", "data": {"AccessoryIP": "*************", "AccessoryIPs": ["*************"], "AccessoryPort": 34649}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "kitchen DEAE._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JQTHG1MKAQP696T0PFH1QB5Q", "minor_version": 1, "modified_at": "2025-04-02T06:02:28.292134+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "kitchen DEAE (Ip Camera)", "unique_id": "11:58:a8:bd:99:6a", "version": 1}, {"created_at": "2025-04-03T11:04:16.671355+00:00", "data": {"address": "***********", "credentials": {"3": null, "5": null}, "identifiers": ["DE:69:0F:A4:79:24", "DE690FA47924"], "name": "臥室"}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_airplay._tcp.local.", "臥室._airplay._tcp.local."], "version": 1}, {"domain": "zeroconf", "key": ["_raop._tcp.local.", "DE690FA47924@臥室._raop._tcp.local."], "version": 1}]}, "domain": "apple_tv", "entry_id": "01JQXN5DGZMSATWC7N9206D7D5", "minor_version": 1, "modified_at": "2025-04-03T14:38:56.694845+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "臥室", "unique_id": "DE:69:0F:A4:79:24", "version": 1}, {"created_at": "2025-04-03T11:05:19.646770+00:00", "data": {"cloud_password": "aA22661296", "control_port": 443, "custom_stream": "", "enable_motion_sensor": true, "enable_sound_detection": false, "enable_stream": true, "enable_time_sync": true, "enable_webhooks": true, "extra_arguments": "", "ip_address": "************", "is_klap_device": false, "media_sync_cold_storage_path": "", "media_sync_hours": "", "media_view_days_order": "Ascending", "media_view_recordings_order": "Ascending", "password": "aA22661296", "reported_ip_address": "************", "rtsp_transport": "tcp", "sound_detection_duration": 1, "sound_detection_peak": -30, "sound_detection_reset": 10, "time_sync_dst": 1, "time_sync_ndst": 0, "update_interval_battery": 600, "update_interval_main": 30, "username": "killtw"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "9ca2f4381702", "version": 1}]}, "domain": "tapo_control", "entry_id": "01JQXN7B0Y57FFWG0XXV2NWKC0", "minor_version": 1, "modified_at": "2025-06-04T13:31:12.945492+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "dhcp", "subentries": [], "title": "************", "unique_id": "tapo_control************", "version": 22}, {"created_at": "2025-04-03T11:08:06.613421+00:00", "data": {"cloud_password": "aA22661296", "control_port": 443, "custom_stream": "", "enable_motion_sensor": true, "enable_sound_detection": false, "enable_stream": true, "enable_time_sync": false, "enable_webhooks": true, "extra_arguments": "", "ip_address": "*************", "is_klap_device": false, "media_sync_cold_storage_path": "", "media_sync_hours": "", "media_view_days_order": "Ascending", "media_view_recordings_order": "Ascending", "password": "aA22661296", "reported_ip_address": "*************", "rtsp_transport": "tcp", "sound_detection_duration": 1, "sound_detection_peak": -30, "sound_detection_reset": 10, "time_sync_dst": 1, "time_sync_ndst": 0, "update_interval_battery": 600, "update_interval_main": 30, "username": "killtw"}, "disabled_by": null, "discovery_keys": {}, "domain": "tapo_control", "entry_id": "01JQXNCE2NHPTM976GPZYQ9MA7", "minor_version": 1, "modified_at": "2025-06-04T13:31:12.945895+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "*************", "unique_id": "tapo_control*************", "version": 22}, {"created_at": "2025-04-03T15:41:13.644508+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "backup", "entry_id": "01JQY50H1CGJW3FZKK6RQV5155", "minor_version": 1, "modified_at": "2025-04-03T15:41:13.644516+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "system", "subentries": [], "title": "Backup", "unique_id": null, "version": 1}, {"created_at": "2025-04-17T06:48:04.151484+00:00", "data": {"AccessoryIP": "*************", "AccessoryIPs": ["*************"], "AccessoryPort": 49604}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Scrypted CCF4._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JS182B9QFGVJSJ391TQ6F3QY", "minor_version": 1, "modified_at": "2025-04-17T07:11:33.442428+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Scrypted CCF4 (Bridge)", "unique_id": "b2:82:81:99:f7:f3", "version": 1}, {"created_at": "2025-04-24T06:28:19.169164+00:00", "data": {"integration_created_addon": false, "url": "ws://localhost:5580/ws", "use_addon": false}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_matter._tcp.local.", "BFB79723A9DBF033-000000000001B669._matter._tcp.local."], "version": 1}]}, "domain": "matter", "entry_id": "01JSK7Q731ZVEF9AT356DQGPPR", "minor_version": 1, "modified_at": "2025-04-24T06:28:19.169170+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Matter", "unique_id": null, "version": 1}, {"created_at": "2025-04-27T17:29:50.153716+00:00", "data": {"ignored_zones": [], "tolerance": 70.0, "tracked_entities": ["person.karl_li", "person.phibie", "device_tracker.lao_ya_hao_location_tracker"], "zone": "zone.home"}, "disabled_by": null, "discovery_keys": {}, "domain": "proximity", "entry_id": "01JSW4RMT9F4TGKK0WKHYRPKFK", "minor_version": 1, "modified_at": "2025-08-25T07:11:32.809646+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "PKDKLF", "unique_id": null, "version": 1}, {"created_at": "2025-04-29T14:49:33.084760+00:00", "data": {"AccessoryIP": "*************", "AccessoryIPs": ["*************"], "AccessoryPort": 38300}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Bedroom 2E0C._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JT10CK4W0JTRPHBK9FCKQXS8", "minor_version": 1, "modified_at": "2025-04-29T15:04:50.572441+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Bedroom 2E0C (Ip Camera)", "unique_id": "b3:31:54:c5:41:05", "version": 1}, {"created_at": "2025-05-07T05:21:10.758315+00:00", "data": {"access_token": "eyJraWQiOiIxMGZhMWQwOWY4YjM2OGFjYmE4YmRiNDYxOTFmZmVhODE1MmZiM2YzZjQ5N2RhZjk1OWFjNWIzNDM5ZDI3OGY0IiwiYWxnIjoiUlMyNTYiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sEnZMsxpOK1L8zHegTRlOq5tmQofj6_AS6x4jjuQa02dhl84eFJNRD-AOlXFeimh4aUGkfrYaFLkxmW7rCigjHbGC-wljEVkd-jCcTz0KjpLwzeFG8ccsw0cVGPv2LBQIg-cnqMV6EunPtCPU2YlpDKi9ldsZ1wVovWIjE95YxOUj8VMguBwkUZnPmoETEOGdM7NVwxNYZqObo4TJcf5_XrbtIw39of9dQDKDIFniJTFGY6OlnUIIhNj_da8nGWicyzmI2rizpCXRBPuxMXSo9-QD93ONKHOyz3FSBFulyUw-BBT-ydZJRJF2n6HpxWOnrsa72PYxsDkBZf51IVjjw", "api_key": "a_189b2827-b2af-4b12-93f3-62f9ac33566a", "refresh_token": "9KXrlWHMbpb6XzHVzrECHwnxcDsKclivuOXcg5U2b8h2vZfpnPZ2qKOVGgpvL17lNqGZAShTKGU4unCSE3mgbYq6qAtaguv2hPlmWofRPuDBA6VGXRMp2RGPQkRkSdjY"}, "disabled_by": null, "discovery_keys": {}, "domain": "wellbeing", "entry_id": "01JTMK1M36Y0HCAGPBV82YJATB", "minor_version": 1, "modified_at": "2025-08-26T14:45:56.262292+00:00", "options": {"scan_interval": 300}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "Electrolux Wellbeing", "unique_id": null, "version": 1}, {"created_at": "2025-05-09T06:42:33.824076+00:00", "data": {"cloud_password": "aA22661296", "control_port": 443, "custom_stream": "", "enable_motion_sensor": true, "enable_sound_detection": false, "enable_stream": true, "enable_time_sync": true, "enable_webhooks": true, "extra_arguments": "", "ip_address": "************", "is_klap_device": false, "media_sync_cold_storage_path": "", "media_sync_hours": "", "media_view_days_order": "Ascending", "media_view_recordings_order": "Ascending", "password": "aA22661296", "reported_ip_address": "************", "rtsp_transport": "tcp", "sound_detection_duration": 1, "sound_detection_peak": -30, "sound_detection_reset": 10, "time_sync_dst": 1, "time_sync_ndst": 0, "update_interval_battery": 600, "update_interval_main": 30, "username": "killtw"}, "disabled_by": null, "discovery_keys": {}, "domain": "tapo_control", "entry_id": "01JTSWG2Q0GKZDYBFSJK0VMBCS", "minor_version": 1, "modified_at": "2025-06-04T13:31:12.946088+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "************", "unique_id": "tapo_control************", "version": 22}, {"created_at": "2025-05-21T05:57:58.210290+00:00", "data": {"access_token": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "api_proxy_cert": null, "api_proxy_url": null, "client_id": "ownerapi", "domain": "https://auth.tesla.com", "expiration": 1756261404, "include_energysites": true, "include_vehicles": true, "initial_setup": false, "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "username": "<EMAIL>"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "4cfcaa06c92f", "version": 1}]}, "domain": "tesla_custom", "entry_id": "01JVRPQ1T2WCTJWTZCN3P6M4SK", "minor_version": 1, "modified_at": "2025-08-26T10:23:34.085555+00:00", "options": {"enable_teslamate": true, "enable_wake_on_start": false, "polling_policy": "normal", "scan_interval": 660}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "dhcp", "subentries": [], "title": "<EMAIL>", "unique_id": null, "version": 1}, {"created_at": "2025-05-23T15:20:47.748119+00:00", "data": {"password": "2zoxCoXwiVduysy", "proxy": "", "update_interval": 180, "username": "<EMAIL>"}, "disabled_by": null, "discovery_keys": {}, "domain": "panasonic_smart_app", "entry_id": "01JVYVQ1W492E97Z26X37DXNKH", "minor_version": 1, "modified_at": "2025-06-08T16:31:05.830990+00:00", "options": {"proxy": "", "update_interval": 180}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "<EMAIL>", "unique_id": null, "version": 1}, {"created_at": "2025-05-29T03:05:06.738078+00:00", "data": {"device": {"path": "socket://192.168.1.108:6638"}}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_xzg._tcp.local.", "XZG._xzg._tcp.local."], "version": 1}]}, "domain": "zha", "entry_id": "01JWD009DJTM21QJMM7BW1H4BS", "minor_version": 1, "modified_at": "2025-05-29T03:53:24.652230+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "XZG", "unique_id": "XZG", "version": 4}, {"created_at": "2025-05-30T03:50:58.192748+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "group", "entry_id": "01JWFN0ZCGHGDB4QRFYS3RTCS6", "minor_version": 1, "modified_at": "2025-05-30T03:50:58.192750+00:00", "options": {"entities": ["switch.3f_wall_lamps", "switch.4f_wall_lamp"], "group_type": "switch", "hide_members": false, "name": "壁燈"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "壁燈", "unique_id": null, "version": 1}, {"created_at": "2025-06-21T08:14:48.790053+00:00", "data": {"host": "fd6d:2491:6377:b245:56ef:44ff:fe6c:d838"}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_aqara._tcp.local.", "Aqara-Hub-M2-D838._aqara._tcp.local."], "version": 1}]}, "domain": "aqara_gateway", "entry_id": "01JY8RVWYP6QH01D4PPP60CX0Z", "minor_version": 1, "modified_at": "2025-07-17T06:22:16.994969+00:00", "options": {"host": "************", "model": "lumi.gateway.iragl8", "noffline": true, "password": "", "token": ""}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "zeroconf", "subentries": [], "title": "Aqara-Hub-M2-D838", "unique_id": "aqara_gateway_Aqara-Hub-M2-D838", "version": 1}, {"created_at": "2025-07-04T16:53:31.477372+00:00", "data": {"device_id": "uuid:908fb668-9834-40bf-b602-8369e69f89c2", "mac": null, "type": null, "url": null}, "disabled_by": null, "discovery_keys": {"ssdp": [{"domain": "ssdp", "key": "uuid:908fb668-9834-40bf-b602-8369e69f89c2", "version": 1}]}, "domain": "dlna_dmr", "entry_id": "01JZB5Q16NEVJMNGDN2CA1781E", "minor_version": 1, "modified_at": "2025-07-04T16:53:31.477377+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "[TV] Samsung 7 Series (55)", "unique_id": "uuid:908fb668-9834-40bf-b602-8369e69f89c2", "version": 1}, {"created_at": "2025-07-06T08:14:26.067210+00:00", "data": {"alias": "Daisy 香氛", "connection_parameters": {"device_family": "SMART.TAPOPLUG", "encryption_type": "KLAP", "http_port": 80, "https": false, "login_version": 2}, "credentials_hash": "qXLfB2EJYw1/Ovb3f4PBiTO+UgLKuqoEpPNEM7EyHNg=", "host": "*************", "model": "P100", "uses_http": true}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "3c52a1bfaa2e", "version": 1}]}, "domain": "tplink", "entry_id": "01JZFCSZRKR0H1467B4QDKGQR7", "minor_version": 5, "modified_at": "2025-07-29T09:56:04.639406+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "梨子燈 P100", "unique_id": "3c:52:a1:bf:aa:2e", "version": 1}, {"created_at": "2025-07-06T08:15:16.441596+00:00", "data": {"alias": "融燭燈", "connection_parameters": {"device_family": "SMART.TAPOPLUG", "encryption_type": "KLAP", "http_port": 80, "https": false, "login_version": 2}, "credentials_hash": "qXLfB2EJYw1/Ovb3f4PBiTO+UgLKuqoEpPNEM7EyHNg=", "host": "*************", "model": "P100", "uses_http": true}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "3c52a1bfc069", "version": 1}]}, "domain": "tplink", "entry_id": "01JZFCVGYSKBB85ZVS8G9F5YRJ", "minor_version": 5, "modified_at": "2025-07-20T18:03:31.494277+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "融燭燈 P100", "unique_id": "3c:52:a1:bf:c0:69", "version": 1}, {"created_at": "2025-07-08T06:23:08.141842+00:00", "data": {"host": "************"}, "disabled_by": null, "discovery_keys": {}, "domain": "tplink", "entry_id": "01JZMB7MBDWFD02X4ZFYS4W16H", "minor_version": 5, "modified_at": "2025-07-08T06:24:45.966869+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Tapo_Camera C210 (************)", "unique_id": "9c:a2:f4:38:17:02", "version": 1}, {"created_at": "2025-07-08T06:23:08.142376+00:00", "data": {"host": "*************"}, "disabled_by": null, "discovery_keys": {}, "domain": "tplink", "entry_id": "01JZMB7MBEQY8E569SCXEGKZ28", "minor_version": 5, "modified_at": "2025-07-08T06:24:45.967102+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Tapo_C220_BC79 C220 (*************)", "unique_id": "e4:fa:c4:f6:bc:79", "version": 1}, {"created_at": "2025-07-08T06:39:45.153222+00:00", "data": {}, "disabled_by": null, "discovery_keys": {"zeroconf": [{"domain": "zeroconf", "key": ["_hap._tcp.local.", "Bedroom 9661._hap._tcp.local."], "version": 1}]}, "domain": "homekit_controller", "entry_id": "01JZMC6201ZNHK6YGRZK8J1BK5", "minor_version": 1, "modified_at": "2025-07-08T06:39:45.153228+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "ignore", "subentries": [], "title": "Bedroom 9661 (Ip Camera)", "unique_id": "d8:70:4e:12:b0:21", "version": 1}, {"created_at": "2025-07-13T05:54:06.396255+00:00", "data": {"app_data": {"push_token": "eht81xCLB0hBk45F5lDU83:APA91bFkkQEReLuBTjNb4vX-C_m0YdpnUOCs1OI-i3CkIWNPEu5am6ZBHeNcXgRNfn5Xo0cYwAkmvsIjOgbhSWjh49NiGp5-N979rPY4XIm4yDvO8yoj084", "push_url": "https://mobile-apps.home-assistant.io/api/sendPushNotification"}, "app_id": "io.robbie.HomeAssistant", "app_name": "Home Assistant", "app_version": "2025.7.2 (2025.1356)", "device_id": "834705A2-A5F1-46C4-85A6-E464485D5564", "device_name": "Phibz iPhone15", "manufacturer": "Apple", "model": "iPhone15,4", "no_legacy_encryption": true, "os_name": "iOS", "os_version": "18.6", "secret": "b5d0b21737ac56f20c176d4f0a4c900a304f29d9810d8944550817ce3959fc39", "supports_encryption": true, "user_id": "c752771c5ce34b52b02cb076cd9db031", "webhook_id": "153dfc758df9923f0782dc0434d2e0ccc67be8ba5d574bfbaf58fb7ed025a4a0"}, "disabled_by": null, "discovery_keys": {}, "domain": "mobile_app", "entry_id": "01K015J2DWTWMP7YFQTZBRR1WH", "minor_version": 1, "modified_at": "2025-08-12T09:46:25.521960+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "registration", "subentries": [], "title": "Phibz iPhone15", "unique_id": "io.robbie.HomeAssistant-834705A2-A5F1-46C4-85A6-E464485D5564", "version": 1}, {"created_at": "2025-07-31T14:04:55.769530+00:00", "data": {"access_token": "thinqpat_aa5249878cc164f2bece8b36dc25439bcaebbc4d4ad5ad347d8c", "connect_client_id": "home-assistant-48606abc-e658-47ee-b289-f039789431ff", "country": "TW"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "34e6e6a399e8", "version": 1}]}, "domain": "lg_thinq", "entry_id": "01K1GCSQJS4S81FY8YQ5M117G1", "minor_version": 1, "modified_at": "2025-07-31T14:04:55.769537+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "dhcp", "subentries": [], "title": "LG ThinQ", "unique_id": "thinqpat_aa5249878cc164f2bece8b36dc25439bcaebbc4d4ad5ad347d8c", "version": 1}, {"created_at": "2025-08-04T07:31:54.095742+00:00", "data": {"npsso": "9zolfDBaHAy7wz4BTpTJmEhLjAvl5IGmbPq43iSyF0VUOvKCzlS1FrB5dYvudewv"}, "disabled_by": null, "discovery_keys": {"dhcp": [{"domain": "dhcp", "key": "70662a21c86c", "version": 1}]}, "domain": "playstation_network", "entry_id": "01K1SZWYKFCR09SBQ9JCT50NEY", "minor_version": 1, "modified_at": "2025-08-04T07:31:54.095748+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "dhcp", "subentries": [], "title": "killtw", "unique_id": "965706123255233259", "version": 1}, {"created_at": "2025-08-04T09:55:16.894560+00:00", "data": {"app_data": {"push_token": "dDJVsbnjMEt7suvHn-OFKZ:APA91bFnqB-Xivl6uo_m0g15M36k8_Ko2xZgTR6Zg9wLIE0FqI-32gi_TKAFwBSB-_KrII-1e2Ll52pzpRZCBl-Uzj5kyN1gOTyUXm9Hsp24Mep0SwqUytAGKTe4oTFBjJSdMi0G3IYN", "push_url": "https://mobile-apps.home-assistant.io/api/sendPushNotification"}, "app_id": "io.robbie.HomeAssistant", "app_name": "Home Assistant", "app_version": "2025.7.2 (2025.1356)", "device_id": "26720809-0987-4ABD-A361-D7514E3E6F94", "device_name": "iPad mini", "manufacturer": "Apple", "model": "iPad14,1", "no_legacy_encryption": true, "os_name": "iPadOS", "os_version": "18.6", "secret": "4140e5fc6010688034a65a36cb1c7a9b1a9113f0f884abc2c2b9eaba36e6e617", "supports_encryption": true, "user_id": "9b1cbb01e5804f8d8d27eeb071638aab", "webhook_id": "26661dc3e7ceca294c28c1aba5601ce998dee1b64eea71822657a1f87d31c18b"}, "disabled_by": null, "discovery_keys": {}, "domain": "mobile_app", "entry_id": "01K1T83FRY42VDSA4NR53KRYW8", "minor_version": 1, "modified_at": "2025-08-15T07:08:51.776450+00:00", "options": {}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "registration", "subentries": [], "title": "iPad mini", "unique_id": "io.robbie.HomeAssistant-26720809-0987-4ABD-A361-D7514E3E6F94", "version": 1}, {"created_at": "2025-08-14T14:27:38.249443+00:00", "data": {}, "disabled_by": null, "discovery_keys": {}, "domain": "switch_as_x", "entry_id": "01K2MFNC4981FAN6V4CYN9PB2P", "minor_version": 3, "modified_at": "2025-08-14T14:27:38.249446+00:00", "options": {"entity_id": "switch.studio", "invert": false, "target_domain": "light"}, "pref_disable_new_entities": false, "pref_disable_polling": false, "source": "user", "subentries": [], "title": "studio", "unique_id": null, "version": 1}]}}