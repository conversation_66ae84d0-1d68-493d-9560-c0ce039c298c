
# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
#   themes: !include_dir_merge_named themes

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml
timer: !include timer.yaml

# Core Configuration
homeassistant:
  packages: !include_dir_named packages
  allowlist_external_dirs:
  - /config/www/bubble
  - /config/www

# Persistent storage for Bubble Card modules
template:
  - trigger:
      - platform: event
        event_type: bubble_card_update_modules
    sensor:
      - name: "Bubble Card Modules"
        state: "saved"
        attributes:
          modules: "{{ trigger.event.data.modules }}"
          last_updated: "{{ trigger.event.data.last_updated }}"
              