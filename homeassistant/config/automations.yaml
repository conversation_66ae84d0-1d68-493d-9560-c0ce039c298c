- id: '1740766955668'
  alias: 3F 自動樓梯燈
  description: ''
  use_blueprint:
    path: Blackshome/sensor-light.yaml
    input:
      motion_trigger:
      - binary_sensor.3f_ren_zai_gan_ying_presence
      - binary_sensor.0x842712fffe7ce6d3_occupancy
      light_switch:
        entity_id: switch.3f_stairs
      time_delay: 0.5
      include_sun: sun_disabled
- id: '1740984869662'
  alias: 客廳崁燈
  description: ''
  use_blueprint:
    path: dbrand666/link_multiple_devices.yaml
    input:
      linked_entities:
      - switch.living_room_downlight
      - light.downlight
- id: '1740984964318'
  alias: 客廳吊燈
  description: ''
  use_blueprint:
    path: dbrand666/link_multiple_devices.yaml
    input:
      linked_entities:
      - light.ceiling
      - switch.ceiling
- id: '1740987782030'
  alias: 晴空燈
  description: ''
  use_blueprint:
    path: dbrand666/link_multiple_devices.yaml
    input:
      linked_entities:
      - light.kitchen
      - switch.kitchen
- id: '1741240299234'
  alias: 自動樓梯壁燈
  description: ''
  triggers:
  - trigger: sun
    event: sunrise
    offset: 04:00:00
    id: sunrise
  - trigger: sun
    event: sunset
    offset: -00:15:00
    id: sunset
  conditions: []
  actions:
  - choose:
    - conditions:
      - condition: trigger
        id:
        - sunset
      sequence:
      - action: switch.turn_on
        metadata: {}
        data: {}
        target:
          entity_id:
          - switch.wall_lamps
    - conditions:
      - condition: trigger
        id:
        - sunrise
      sequence:
      - action: switch.turn_off
        metadata: {}
        data: {}
        target:
          entity_id: switch.wall_lamps
  mode: single
- id: '1741241552349'
  alias: 自動開關車庫門
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - device_tracker.lao_ya_hao_location_tracker
    from: home
    to: not_home
    id: leave
  - trigger: state
    entity_id:
    - device_tracker.lao_ya_hao_location_tracker
    from: not_home
    to: home
    id: home
  conditions: []
  actions:
  - choose:
    - conditions:
      - condition: trigger
        id:
        - home
      sequence:
      - action: cover.open_cover
        metadata: {}
        data: {}
        target:
          entity_id: cover.garage
    - conditions:
      - condition: trigger
        id:
        - leave
      sequence:
      - action: cover.close_cover
        metadata: {}
        data: {}
        target:
          entity_id: cover.garage
  mode: single
- id: '1741539922722'
  alias: Mailbox
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - binary_sensor.0x00158d000708748f_contact
    to: 'on'
    id: mail_delivered
  - trigger: state
    entity_id:
    - binary_sensor.0xa4c138ba2d3d0757_contact
    to: 'on'
    id: mailbox_opened
  conditions: []
  actions:
  - choose:
    - conditions:
      - condition: trigger
        id:
        - mail_delivered
      sequence:
      - action: input_boolean.turn_on
        metadata: {}
        data: {}
        target:
          entity_id: input_boolean.mailbox
    - conditions:
      - condition: trigger
        id:
        - mailbox_opened
      sequence:
      - action: input_boolean.turn_off
        metadata: {}
        data: {}
        target:
          entity_id: input_boolean.mailbox
  mode: single
- id: '1741834329889'
  alias: 門開啟後開燈
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - binary_sensor.garage_contact
    to: 'on'
    id: garage_open
  - trigger: state
    entity_id:
    - binary_sensor.garage_side_door
    to: 'on'
    id: garage_door_open
  conditions: []
  actions:
  - action: switch.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: switch.garage
  mode: single
- id: '1742129441705'
  alias: 餐廳燈
  description: ''
  use_blueprint:
    path: dbrand666/link_multiple_devices.yaml
    input:
      linked_entities:
      - light.dining
      - switch.dining
- id: '1746689048551'
  alias: 自動關融燭燈
  description: ''
  triggers:
  - type: turned_on
    device_id: 08052569b095cac3236dd82fdc4b3775
    entity_id: 9b359baf5f2b5b371a668b517de2412c
    domain: switch
    trigger: device
  conditions: []
  actions:
  - delay:
      hours: 1
      minutes: 0
      seconds: 0
  - type: turn_off
    device_id: 08052569b095cac3236dd82fdc4b3775
    entity_id: 9b359baf5f2b5b371a668b517de2412c
    domain: switch
  mode: single
- id: '1747027764081'
  alias: 自動玄關燈
  description: ''
  use_blueprint:
    path: Blackshome/sensor-light.yaml
    input:
      motion_trigger:
      - binary_sensor.0x842712fffe7cec33_occupancy
      - binary_sensor.garage_side_door
      - binary_sensor.0x54ef441001387177_door_state
      light_switch:
        entity_id: switch.entrance
      time_delay: 3
- id: '1748529345949'
  alias: 臥室燈
  description: ''
  use_blueprint:
    path: dbrand666/link_multiple_devices.yaml
    input:
      linked_entities:
      - switch.bedroom_wall
      - switch.bedroom_door
- id: '1717518265000'
  alias: 車庫門關閉後延遲關燈
  description: 當車庫兩個門都關閉時，延遲兩分鐘後關閉車庫燈，如果期間任一門被打開則重置計時器
  triggers:
  - trigger: state
    entity_id:
    - binary_sensor.garage_side_door
    - binary_sensor.garage_contact
    to: 'off'
    id: garage_doors_closed
  - trigger: state
    entity_id:
    - binary_sensor.garage_contact
    - binary_sensor.garage_side_door
    to: 'on'
    id: doors_opened
  - trigger: event
    event_type: timer.finished
    event_data:
      entity_id: timer.garage_light_off_delay
    id: timer_finished
  conditions: []
  actions:
  - choose:
    - conditions:
      - condition: and
        conditions:
        - condition: trigger
          id:
          - garage_doors_closed
        - condition: state
          entity_id: binary_sensor.garage_contact
          state: 'off'
        - condition: state
          entity_id: binary_sensor.garage_side_door
          state: 'off'
      sequence:
      - action: timer.start
        metadata: {}
        data: {}
        target:
          entity_id: timer.garage_light_off_delay
    - conditions:
      - condition: trigger
        id:
        - doors_opened
      sequence:
      - action: timer.cancel
        metadata: {}
        data: {}
        target:
          entity_id: timer.garage_light_off_delay
    - conditions:
      - condition: trigger
        id:
        - timer_finished
      sequence:
      - action: switch.turn_off
        metadata: {}
        data: {}
        target:
          entity_id: switch.garage
  mode: restart
- id: '1750523301886'
  alias: 大門燈
  description: ''
  triggers:
  - trigger: time
    at: '20:30:00'
    id: 'on'
  - trigger: time
    at: 02:00:00
    id: 'off'
  conditions: []
  actions:
  - choose:
    - conditions:
      - condition: trigger
        id:
        - 'on'
      sequence:
      - action: switch.turn_on
        metadata: {}
        data: {}
        target:
          entity_id: switch.door
    - conditions:
      - condition: trigger
        id:
        - 'off'
      sequence:
      - action: switch.turn_off
        metadata: {}
        data: {}
        target:
          entity_id: switch.door
  mode: single
- id: '1754650959484'
  alias: Mail notifier
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - binary_sensor.mailbox
    from: 'off'
    to: 'on'
  conditions: []
  actions:
  - action: notify.mobile_app_karls_iphone_16_pro
    metadata: {}
    data:
      message: You got a mail!
  mode: single
