Fatal Python error: Segmentation fault

Thread 0x00007febaf556b30 (most recent call first):
  <no Python frame>

Thread 0x00007feba3a4cb30 (most recent call first):
  <no Python frame>

Thread 0x00007febaf689b30 (most recent call first):
  File "/usr/local/lib/python3.13/threading.py", line 363 in wait
  File "/usr/local/lib/python3.13/threading.py", line 659 in wait
  File "/config/custom_components/bambu_lab/pybambu/bambu_client.py", line 63 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febaf19fb30 (most recent call first):
  File "/usr/local/lib/python3.13/asyncio/unix_events.py", line 1443 in _do_waitpid
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007feba38e6b30 (most recent call first):
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 1663 in _loop
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2297 in loop_forever
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4523 in _thread_main
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febaaea9b30 (most recent call first):
  <no Python frame>

Thread 0x00007febaeb2cb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febaec5fb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febafdefb30 (most recent call first):
  File "/usr/local/lib/python3.13/threading.py", line 359 in wait
  File "/usr/local/lib/python3.13/threading.py", line 659 in wait
  File "/usr/local/lib/python3.13/threading.py", line 979 in start
  File "/config/custom_components/bambu_lab/pybambu/bambu_client.py", line 499 in start_camera
  File "/config/custom_components/bambu_lab/pybambu/bambu_client.py", line 521 in _on_connect
  File "/config/custom_components/bambu_lab/pybambu/bambu_client.py", line 490 in on_connect
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 3922 in _handle_connack
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 3814 in _packet_handle
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 3142 in _packet_read
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2100 in loop_read
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 1686 in _loop
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2297 in loop_forever
  File "/config/custom_components/bambu_lab/pybambu/bambu_client.py", line 254 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb1139b30 (most recent call first):
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 1663 in _loop
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2297 in loop_forever
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4523 in _thread_main
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb136cb30 (most recent call first):
  File "/usr/src/homeassistant/homeassistant/util/loop.py", line 201 in protected_loop_func
  File "/usr/local/lib/python3.13/site-packages/tuya_sharing/mq.py", line 122 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb159fb30 (most recent call first):
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb2bdfb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb4296b30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb43c9b30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb44fcb30 (most recent call first):
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 1663 in _loop
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2297 in loop_forever
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4523 in _thread_main
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febb80ffb30 (most recent call first):
  File "/usr/local/lib/python3.13/socket.py", line 849 in create_connection
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4640 in _create_socket_connection
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4609 in _create_socket
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 1598 in reconnect
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 2324 in loop_forever
  File "/usr/local/lib/python3.13/site-packages/paho/mqtt/client.py", line 4523 in _thread_main
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd387fb30 (most recent call first):
  <no Python frame>

Thread 0x00007febbadc3b30 (most recent call first):
  File "/usr/local/lib/python3.13/selectors.py", line 452 in select
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1996 in _run_once
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 677 in run_forever
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 706 in run_until_complete
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 118 in run
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 195 in run
  File "/usr/local/lib/python3.13/site-packages/pyrate_limiter/abstracts/bucket.py", line 196 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febbaef6b30 (most recent call first):
  File "/usr/local/lib/python3.13/selectors.py", line 452 in select
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1996 in _run_once
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 677 in run_forever
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 706 in run_until_complete
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 118 in run
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 195 in run
  File "/usr/local/lib/python3.13/site-packages/pyrate_limiter/abstracts/bucket.py", line 196 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febc1b1fb30 (most recent call first):
  File "/usr/local/lib/python3.13/selectors.py", line 452 in select
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1996 in _run_once
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 677 in run_forever
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 706 in run_until_complete
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 118 in run
  File "/usr/local/lib/python3.13/asyncio/runners.py", line 195 in run
  File "/usr/local/lib/python3.13/site-packages/pyrate_limiter/abstracts/bucket.py", line 196 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd097fb30 (most recent call first):
  File "/usr/local/lib/python3.13/threading.py", line 359 in wait
  File "/usr/local/lib/python3.13/concurrent/futures/_base.py", line 451 in result
  File "/usr/src/homeassistant/homeassistant/core.py", line 2121 in all
  File "/usr/src/homeassistant/homeassistant/components/sensor/recorder.py", line 113 in _get_sensor_states
  File "/usr/src/homeassistant/homeassistant/components/sensor/recorder.py", line 463 in compile_statistics
  File "/usr/src/homeassistant/homeassistant/components/recorder/statistics.py", line 670 in _compile_statistics
  File "/usr/src/homeassistant/homeassistant/components/recorder/statistics.py", line 617 in compile_statistics
  File "/usr/src/homeassistant/homeassistant/components/recorder/util.py", line 641 in wrapper
  File "/usr/src/homeassistant/homeassistant/components/recorder/tasks.py", line 170 in run
  File "/usr/src/homeassistant/homeassistant/components/recorder/core.py", line 907 in _process_one_task_or_event_or_recover
  File "/usr/src/homeassistant/homeassistant/components/recorder/core.py", line 887 in _guarded_process_one_task_or_event_or_recover
  File "/usr/src/homeassistant/homeassistant/components/recorder/core.py", line 853 in _run_event_loop
  File "/usr/src/homeassistant/homeassistant/components/recorder/core.py", line 812 in _run
  File "/usr/src/homeassistant/homeassistant/components/recorder/core.py", line 690 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd56cdb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd57ffb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd5d5ab30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd5e8cb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd5fbeb30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd60f0b30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd6354b30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febd6222b30 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febe29ffb38 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febe41ffb38 (most recent call first):
  File "/usr/local/lib/python3.13/logging/__init__.py", line 1136 in flush
  File "/usr/local/lib/python3.13/logging/__init__.py", line 1154 in emit
  File "/usr/local/lib/python3.13/logging/__init__.py", line 1264 in emit
  File "/usr/local/lib/python3.13/logging/handlers.py", line 81 in emit
  File "/usr/local/lib/python3.13/logging/__init__.py", line 1026 in handle
  File "/usr/local/lib/python3.13/logging/handlers.py", line 1576 in handle
  File "/usr/src/homeassistant/homeassistant/util/logging.py", line 53 in handle
  File "/usr/local/lib/python3.13/logging/handlers.py", line 1595 in _monitor
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Thread 0x00007febe4762b38 (most recent call first):
  File "/usr/local/lib/python3.13/concurrent/futures/thread.py", line 90 in _worker
  File "/usr/local/lib/python3.13/threading.py", line 992 in run
  File "/usr/local/lib/python3.13/threading.py", line 1041 in _bootstrap_inner
  File "/usr/local/lib/python3.13/threading.py", line 1012 in _bootstrap

Current thread 0x00007febef2d2b28 (most recent call first):
  File "/usr/local/lib/python3.13/site-packages/aioesphomeapi/client.py", line 277 in device_info
  File "/usr/src/homeassistant/homeassistant/util/async_.py", line 44 in create_eager_task
  File "/usr/src/homeassistant/homeassistant/components/esphome/manager.py", line 427 in _on_connect
  File "/usr/src/homeassistant/homeassistant/components/esphome/manager.py", line 375 in on_connect
  File "/usr/local/lib/python3.13/site-packages/aioesphomeapi/reconnect_logic.py", line 229 in _try_connect
  File "/usr/local/lib/python3.13/site-packages/aioesphomeapi/reconnect_logic.py", line 323 in _connect_once_or_reschedule
  File "/usr/local/lib/python3.13/asyncio/events.py", line 89 in _run
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 2034 in _run_once
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 677 in run_forever
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 706 in run_until_complete
  File "/usr/src/homeassistant/homeassistant/runner.py", line 154 in run
  File "/usr/src/homeassistant/homeassistant/__main__.py", line 209 in main
  File "/usr/src/homeassistant/homeassistant/__main__.py", line 223 in <module>
  File "<frozen runpy>", line 88 in _run_code
  File "<frozen runpy>", line 198 in _run_module_as_main

Extension modules: _cffi_backend, ciso8601, yaml._yaml, propcache._helpers_c, ulid_transform._ulid_impl, multidict._multidict, yarl._quoting_c, markupsafe._speedups, _brotli, aiohttp._http_writer, aiohttp._http_parser, aiohttp._websocket.mask, aiohttp._websocket.reader_c, frozenlist._frozenlist, annotatedyaml.reference, annotatedyaml.constructors, _awscrt, charset_normalizer.md, simplejson._speedups, requests.packages.charset_normalizer.md, requests.packages.chardet.md, dbus_fast.signature, dbus_fast._private.marshaller, dbus_fast.message, dbus_fast.service, dbus_fast._private.address, dbus_fast.unpack, dbus_fast.message_bus, dbus_fast._private.unmarshaller, dbus_fast.aio.message_reader, habluetooth.models, habluetooth.advertisement_tracker, bluetooth_data_tools.gap, bluetooth_data_tools._time_impl, bluetooth_data_tools._utils_impl, bluetooth_data_tools.utils, habluetooth.manager, habluetooth.base_scanner, habluetooth.scanner, zeroconf._utils.time, zeroconf._dns, zeroconf._protocol.outgoing, zeroconf._protocol.incoming, zeroconf._cache, zeroconf._record_update, zeroconf._history, zeroconf._updates, zeroconf._services, zeroconf._utils.ipaddress, zeroconf._services.info, zeroconf._services.registry, zeroconf._handlers.answers, zeroconf._handlers.multicast_outgoing_queue, zeroconf._handlers.query_handler, zeroconf._handlers.record_manager, zeroconf._listener, zeroconf._services.browser, sqlalchemy.cyextension.collections, sqlalchemy.cyextension.immutabledict, sqlalchemy.cyextension.processors, sqlalchemy.cyextension.resultproxy, sqlalchemy.cyextension.util, greenlet._greenlet, fnv_hash_fast._fnv_impl, PIL._imaging, numpy._core._multiarray_umath, numpy.linalg._umath_linalg, isal.igzip_lib, isal.isal_zlib, cached_ipaddress.ipaddress, audioop._audioop, av._core, av.logging, av.bytesource, av.buffer, av.audio.format, av.enum, av.error, av.utils, av.option, av.descriptor, av.container.pyio, av.dictionary, av.format, av.stream, av.container.streams, av.sidedata.motionvectors, av.sidedata.sidedata, av.opaque, av.packet, av.container.input, av.container.output, av.container.core, av.codec.context, av.video.format, av.video.reformatter, av.plane, av.video.plane, av.video.frame, av.video.stream, av.codec.codec, av.frame, av.audio.layout, av.audio.plane, av.audio.frame, av.audio.stream, av.filter.pad, av.filter.link, av.filter.context, av.filter.graph, av.filter.filter, av.audio.resampler, av.audio.codeccontext, av.audio.fifo, av.bitstream, av.video.codeccontext, psutil._psutil_linux, psutil._psutil_posix, lxml._elementpath, lxml.etree, google._upb._message, lxml.builder, PIL._imagingft, PIL._imagingmath, websockets.speedups, kasa_crypt._crypt_impl, aioesphomeapi._frame_helper.base, aioesphomeapi._frame_helper.pack, aioesphomeapi._frame_helper.noise_encryption, aioesphomeapi._frame_helper.packets, aioesphomeapi._frame_helper.noise, aioesphomeapi._frame_helper.plain_text, aioesphomeapi.connection, aioesphomeapi.client_base, bleak_esphome.backend.scanner, cchardet._cchardet, gpiod._ext, psycopg2._psycopg (total: 138)
