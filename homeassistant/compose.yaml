services:
  homeassistant:
    container_name: homeassistant
    image: ghcr.io/home-assistant/home-assistant:stable
    volumes:
      - ./config:/config
      - /etc/localtime:/etc/localtime:ro
    network_mode: host
    restart: unless-stopped
    privileged: true
    environment:
     - TZ=Asia/Taipei
     
  matter-server:
    container_name: matter-server
    image: ghcr.io/home-assistant-libs/python-matter-server:stable
    volumes:
      - ./matter:/data
    restart: unless-stopped
    security_opt:
      - apparmor=unconfined
    network_mode: host
