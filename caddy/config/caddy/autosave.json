{"apps": {"http": {"servers": {"srv0": {"listen": [":443"], "routes": [{"handle": [{"handler": "subroute", "routes": [{"group": "group2", "handle": [{"handler": "subroute", "routes": [{"handle": [{"encodings": {"gzip": {}}, "handler": "encode", "prefer": ["gzip"]}, {"handler": "reverse_proxy", "upstreams": [{"dial": "grafana:3000"}]}]}]}], "match": [{"path": ["/grafana*"]}]}, {"group": "group2", "handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "authentication", "providers": {"http_basic": {"accounts": [{"password": "$2y$10$KKZLNbs2UiT9H22bPCU4Ee8GFpR4NzH9nYGRMxzY.CW1wKQwfgha6", "username": "killtw"}], "hash": {"algorithm": "bcrypt"}, "hash_cache": {}}}}], "match": [{"not": [{"remote_ip": {"ranges": ["***********/24"]}}]}]}, {"handle": [{"encodings": {"gzip": {}}, "handler": "encode", "prefer": ["gzip"]}, {"handler": "reverse_proxy", "upstreams": [{"dial": "teslamate:4000"}]}]}]}]}]}], "match": [{"host": ["tesla.killtw.im"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"encodings": {"gzip": {}}, "handler": "encode", "prefer": ["gzip"]}, {"handler": "reverse_proxy", "upstreams": [{"dial": "localhost:8123"}]}]}]}], "match": [{"host": ["home.killtw.im"]}], "terminal": true}]}}}}}