tesla.killtw.im {
  handle /grafana* {
    encode gzip
    reverse_proxy grafana:3000
  }

  handle {
    encode gzip
    @not-local not remote_ip ***********/24
    basic_auth @not-local {
        killtw $2y$10$KKZLNbs2UiT9H22bPCU4Ee8GFpR4NzH9nYGRMxzY.CW1wKQwfgha6
    }
    reverse_proxy teslamate:4000
  }
}

home.killtw.im {
  encode gzip
  # 使用主機的 Docker 網路 IP 或主機 IP
  reverse_proxy host.docker.internal:8123
}