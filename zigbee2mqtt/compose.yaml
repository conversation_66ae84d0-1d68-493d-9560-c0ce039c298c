services:
  zigbee2mqtt:
    image: ghcr.io/koenkk/zigbee2mqtt
    restart: unless-stopped
    environment:
      - TZ=Asia/Taipei
      - Z2M_WATCHDOG=default
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    volumes:
      - ./data:/app/data
    networks:
      - default
      - homelab
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  homelab:
    external: true
    name: homelab