version: 4
mqtt:
  base_topic: zigbee2mqtt
  server: mqtt://172.17.0.1
serial:
  port: tcp://192.168.1.108:6638
  adapter: zstack
  baudrate: 115200
  disable_led: false
advanced:
  transmit_power: 20
  log_level: info
  channel: 25
  network_key:
    - 239
    - 126
    - 248
    - 153
    - 55
    - 25
    - 215
    - 74
    - 43
    - 90
    - 225
    - 94
    - 115
    - 37
    - 220
    - 46
  pan_id: 33335
  ext_pan_id:
    - 44
    - 102
    - 63
    - 177
    - 14
    - 64
    - 191
    - 129
frontend:
  enabled: true
  port: 8080
homeassistant:
  enabled: true
  base_topic: zigbee2mqtt
  force_disable_retain: false
  include_device_information: false
  keepalive: 60
  maximum_packet_size: 1048576
  reject_unauthorized: true
  server: mqtt://172.17.0.1
  version: 4
devices: devices.yaml
groups: groups.yaml
