{"1": {"brightness": 255, "state": "OFF", "color": {"x": 0.459, "y": 0.383}, "color_mode": "xy", "color_temp": 404}, "4": {"state": "OFF", "brightness": 254}, "8": {"state": "OFF", "brightness": 33, "color_mode": "color_temp", "color": {"x": 0.5267, "y": 0.4133}, "color_temp": 500}, "0x6cfd22fffee80f9c": {"state_left": "OFF", "state_center": "OFF", "state_right": "OFF", "linkquality": 87, "state_l1": "OFF", "state_l2": "OFF", "state_l3": "OFF"}, "0x6cfd22fffee86cd8": {"state_left": "OFF", "state_center": "OFF", "state_right": "OFF", "linkquality": 51, "state_l1": "OFF", "state_l2": "OFF", "state_l3": "OFF"}, "0x6cfd22fffe1c2f31": {"state": "OFF", "power_on_behavior": "previous", "linkquality": 58}, "0x6cfd22fffe1c98f6": {"state": "ON", "power_on_behavior": "off", "linkquality": 65}, "0x6cfd22fffee7f99e": {"state_left": "OFF", "state_center": "ON", "state_right": "OFF", "linkquality": 51, "state_l1": "OFF", "state_l2": "OFF", "state_l3": "OFF"}, "0xa4c138d20c4e154d": {"presence": true, "motion_state": "large", "illuminance": 1896, "fading_time": 60, "large_motion_detection_distance": 4, "medium_motion_detection_distance": 3, "large_motion_detection_sensitivity": 2, "medium_motion_detection_sensitivity": 5, "indicator": "ON", "small_detection_distance": 3, "small_detection_sensitivity": 5, "linkquality": 61}, "0x842712fffe7cec33": {"occupancy": false, "illumination": "dim", "battery": 100, "voltage": 3000, "update": {"state": "idle", "installed_version": 8705, "latest_version": 8705}, "linkquality": 54}, "0x6cfd22fffee7f78b": {"state_left": "OFF", "state_center": "ON", "state_right": "OFF", "linkquality": 32, "state_l2": "OFF", "backlight_mode": "OFF", "state_l1": "OFF", "state_l3": "ON"}, "0x6cfd22fffe19abea": {"state_l1": "OFF", "countdown_l1": 0, "backlight_mode": "ON", "indicator_mode": "on/off", "power_on_behavior_l1": "off", "state_l2": "OFF", "countdown_l2": 0, "power_on_behavior_l2": "off", "inching_control_set": {"inching_control_1": "DISABLE", "inching_time_1": 0, "inching_control_2": "DISABLE", "inching_time_2": 0}, "linkquality": 47}, "0xa4c13840bdea388c": {"state_l1": "OFF", "countdown_l1": 0, "backlight_mode": "ON", "indicator_mode": "off", "power_on_behavior_l1": "off", "switch_type": "state", "state_l2": "OFF", "countdown_l2": 0, "power_on_behavior_l2": "off", "inching_control_set": {"inching_control_1": "ENABLE", "inching_time_1": 1, "inching_control_2": "ENABLE", "inching_time_2": 1}, "linkquality": 54}, "0x4c97a1fffe5489aa": {"state": "ON", "linkquality": 58, "power_on_behavior": "off"}, "0x4c97a1fffe5487e3": {"state": "OFF", "power_on_behavior": "off", "linkquality": 32}, "0x4c97a1fffe548a6d": {"state": "OFF", "power_on_behavior": "off", "linkquality": 58}, "0x6cfd22fffee80636": {"state_left": "OFF", "state_center": "OFF", "state_right": "OFF", "linkquality": 58, "state_l2": "OFF", "state_l1": "OFF", "state_l3": "OFF", "backlight_mode": "ON", "indicator_mode": "on/off", "power_on_behavior_l1": "off", "power_on_behavior_l2": "off", "power_on_behavior_l3": "off"}, "0x00158d00073a5cae": {"linkquality": 18, "voltage": 3062, "battery": 100, "power_outage_count": 123}, "0xa4c138ba2d3d0757": {"battery": 100, "contact": true, "illuminance": 0, "linkquality": 47, "illuminance_interval": 60}, "0xa4c13870139ddef7": {"battery": 100, "linkquality": 51, "illuminance": 0, "contact": true, "illuminance_interval": 60}, "0xa4c138591f9083d0": {"battery": 100, "contact": true, "illuminance": 0, "linkquality": 47, "illuminance_interval": 60}, "0x00158d000708748f": {"linkquality": 51, "contact": true, "voltage": 3045, "battery": 100, "device_temperature": 33, "power_outage_count": 101, "trigger_count": 0}, "0x842712fffe7ce6d3": {"occupancy": false, "battery": 100, "voltage": 3000, "illumination": "dim", "update": {"state": "idle", "installed_version": 8705, "latest_version": 8705}, "linkquality": 10}, "0xa4c138851b4ddae3": {"presence": false, "motion_state": "none", "illuminance": 45, "linkquality": 32, "indicator": "ON", "fading_time": 60, "large_motion_detection_distance": 6, "medium_motion_detection_distance": 4, "large_motion_detection_sensitivity": 6, "medium_motion_detection_sensitivity": 8, "small_detection_distance": 4, "small_detection_sensitivity": 8}, "0x70c59cfffe264997": {"state_l1": "OFF", "countdown_l1": 0, "backlight_mode": "ON", "indicator_mode": "on/off", "power_on_behavior_l1": "off", "state_l2": "ON", "countdown_l2": 0, "power_on_behavior_l2": "off", "inching_control_set": {"inching_control_1": "DISABLE", "inching_time_1": 0, "inching_control_2": "DISABLE", "inching_time_2": 0}, "linkquality": 32}, "0x6cfd22fffe1c9c6a": {"state": "OFF", "power_on_behavior": "off", "linkquality": 32}, "0x6cfd22fffe1cb10f": {"state": "OFF", "power_on_behavior": "off", "linkquality": 29}, "0x6cfd22fffe1a034e": {"state": "OFF", "power_on_behavior": "off", "linkquality": 58}, "0x4c97a1fffe548970": {"state": "OFF", "power_on_behavior": "off", "linkquality": 32}, "0xa4c138a6ff2209eb": {"linkquality": 29, "state": "ON", "color_mode": "color_temp", "color_temp": 500, "color": {"x": 0.5267, "y": 0.4133, "hue": 25, "saturation": 95, "h": 25, "s": 95}, "brightness": 216, "color_power_on_behavior": "previous", "do_not_disturb": false}, "0x70b3d52b601209fa": {"brightness": 1, "color": {"x": 0.459, "y": 0.383}, "color_mode": "xy", "color_temp": 404, "state": "OFF", "linkquality": 58, "power_on_behavior": "off", "level_config": {"on_level": "previous"}}, "0xa4c13814b628056b": {"brightness": 255, "state": "OFF", "color": {"x": 0.459, "y": 0.383}, "color_mode": "xy", "color_temp": 404, "linkquality": 83, "do_not_disturb": true, "level_config": {"on_level": "previous"}}, "0xa4c138ec6626cadd": {"state": "OFF", "linkquality": 7, "color_mode": "color_temp", "color": {"x": 0.5267, "y": 0.4133}, "color_temp": 500, "brightness": 254, "do_not_disturb": true}, "0xa4c13844a96c99c6": {"state": "OFF", "linkquality": 25, "color_mode": "color_temp", "color": {"x": 0.5267, "y": 0.4133}, "color_temp": 500, "brightness": 254, "do_not_disturb": true}, "0xa4c138a6755b559d": {"state": "OFF", "linkquality": 36, "color_mode": "color_temp", "color": {"x": 0.5267, "y": 0.4133}, "color_temp": 500, "brightness": 254, "do_not_disturb": true}, "0xa4c138d449095da3": {"state": "OFF", "linkquality": 32, "brightness": 33, "color_mode": "color_temp", "color_temp": 500, "do_not_disturb": true, "color": {"x": 0.5267, "y": 0.4133}}, "0xa4c1380a172e5a6a": {"state": "OFF", "linkquality": 80, "brightness": 33, "color_mode": "color_temp", "color_temp": 500, "do_not_disturb": true, "color": {"x": 0.5267, "y": 0.4133}}, "0xa4c13893935668b4": {"state": "OFF", "linkquality": 10, "brightness": 33, "color_mode": "color_temp", "color_temp": 500, "color": {"x": 0.5267, "y": 0.4133}}, "0xa4c138385587e524": {"state": "OFF", "linkquality": 29, "brightness": 33, "color_mode": "color_temp", "color_temp": 500, "do_not_disturb": true, "color": {"x": 0.5267, "y": 0.4133}}, "0xf84477fffe4d19bd": {"state": "CLOSE", "position": 0, "work_state": "standby", "battery": 100, "motor_direction": "right", "set_upper_limit": "stop", "opening_mode": "lift", "linkquality": 76}, "0xf84477fffe40747a": {"linkquality": 10, "opening_mode": "lift", "motor_direction": "right", "set_upper_limit": "stop", "position": 0, "state": "CLOSE", "work_state": "learning", "battery": 50, "illuminance": 10}, "0xa4c138b04b602c88": {"battery": 75, "motion_state": "small", "illuminance": 1664, "indicator": "ON", "linkquality": 32, "presence": true, "static_detection_distance": 5, "fading_time": 30, "static_detection_sensitivity": 6, "motion_detection_mode": "only_pir", "motion_detection_sensitivity": 5}}